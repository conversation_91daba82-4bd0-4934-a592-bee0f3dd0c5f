import { useState } from "react";
import { Student } from "@/lib/types";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { supabase } from "@/lib/supabase";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  ChevronDown,
  Mail,
  Book,
  Home,
  DoorClosed,
  UserCircle,
} from "lucide-react";
import { toast as sonnerToast } from "sonner";
import { format } from "date-fns";
import { useAuth } from "@/context/AuthContext";
import { useTranslation } from "react-i18next";

interface StudentListViewProps {
  students: Student[];
  getAttendanceStatus: (
    studentId: string
  ) => "present" | "absent" | "late" | "excused";
  getStatusIcon: (
    status: "present" | "absent" | "late" | "excused"
  ) => JSX.Element;
  selectedRoom: string;
  onStatusUpdate?: (
    studentId: string,
    newStatus: "present" | "absent" | "late" | "excused"
  ) => void;
}

export function StudentListView({
  students,
  getAttendanceStatus,
  getStatusIcon,
  selectedRoom,
  onStatusUpdate,
}: StudentListViewProps) {
  const { t } = useTranslation();
  const { toast } = useToast();
  const { profile } = useAuth();
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
  const [localStatuses, setLocalStatuses] = useState<
    Record<string, "present" | "absent" | "late" | "excused">
  >({});

  // Create a map to track recently updated student IDs and timestamps
  const recentlyUpdatedStudents = new Map<string, number>();

  const getEffectiveStatus = (studentId: string) => {
    return localStatuses[studentId] || getAttendanceStatus(studentId);
  };

  const updateAttendanceStatus = async (
    studentId: string,
    newStatus: "present" | "absent" | "late" | "excused"
  ) => {
    const student = students.find((s) => s.id === studentId);
    if (!student) return;

    try {
      // Update local state immediately
      setLocalStatuses((prev) => ({
        ...prev,
        [studentId]: newStatus,
      }));

      // Track this student as recently updated to prevent polling from overriding it
      recentlyUpdatedStudents.set(studentId, Date.now());

      // Set a timeout to remove this student from the protection after 2 minutes
      setTimeout(() => {
        recentlyUpdatedStudents.delete(studentId);
      }, 120000); // 2 minutes

      // If parent component provides a callback, use it and don't make direct database calls
      if (onStatusUpdate) {
        onStatusUpdate(studentId, newStatus);
        // Don't continue with database operations - let the parent handle it
        return;
      }

      // The code below will only run if there's no parent callback (standalone mode)
      console.log(
        "StudentListView operating in standalone mode - making direct database calls"
      );

      // Get today's date at midnight for comparison
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const todayStr = today.toISOString();

      // Check for existing record today
      const { data: existingRecords, error: fetchError } = await supabase
        .from("attendance_records")
        .select("id, status, room_id")
        .eq("student_id", studentId)
        .gte("timestamp", todayStr);

      if (fetchError) {
        console.error("Error fetching attendance records:", fetchError);
        throw fetchError;
      }

      // Determine which room ID to use - we need a valid room ID
      let roomId = student.roomId || student.room_id || selectedRoom || "";

      // If we still don't have a valid room ID, try to find one from existing records
      if (
        !roomId &&
        existingRecords &&
        existingRecords.length > 0 &&
        existingRecords[0].room_id
      ) {
        roomId = existingRecords[0].room_id;
        console.log(`Using room ID from existing record: ${roomId}`);
      }

      // Validate that we have a non-empty room ID
      if (!roomId || roomId === "") {
        throw new Error("No valid room ID available for attendance update");
      }

      const now = new Date().toISOString();
      let recordId: string;

      try {
        if (existingRecords && existingRecords.length > 0) {
          // Update existing record
          recordId = existingRecords[0].id;
          const { error } = await supabase
            .from("attendance_records")
            .update({
              status: newStatus,
              verification_method: "manual",
              device_info: "Manual update by teacher",
              timestamp: now,
              room_id: roomId, // Ensure room_id is included
            })
            .eq("id", recordId);

          if (error) {
            console.error("Error updating attendance record:", error);
            throw error;
          }
        } else {
          // Create new record
          try {
            const { data, error } = await supabase
              .from("attendance_records")
              .insert({
                student_id: studentId,
                room_id: roomId,
                timestamp: now,
                device_info: "Manual update by teacher",
                verification_method: "manual",
                status: newStatus,
                location: null,
              })
              .select("id")
              .single();

            if (error) {
              console.error("Error inserting attendance record:", error);
              throw error;
            }
            recordId = data.id;
          } catch (insertError) {
            console.error("Exception during insert:", insertError);
            throw insertError;
          }
        }

        try {
          // Get room name for logging purposes
          let roomName = "Unknown";
          if (roomId) {
            const { data: roomData } = await supabase
              .from("rooms")
              .select("name")
              .eq("id", roomId)
              .single();

            if (roomData) {
              roomName = roomData.name;
            }
          }

          // No need to create a notification here
          // The database trigger will handle notification creation when the attendance record is updated
          console.log(`Attendance updated for student ${studentId}`);
        } catch (error) {
          console.error("Error getting room data:", error);
          // Continue execution even if room data fetch fails
        }

        // Show primary toast
        toast({
          title: t("teacher.attendance.statusUpdated"),
          description: (
            <div className="flex flex-col gap-1">
              <span>
                {t("teacher.attendance.studentMarkedAs", {
                  name: student.name,
                  status: "",
                })}{" "}
                <span
                  className={
                    newStatus === "present"
                      ? "text-green-500"
                      : newStatus === "absent"
                      ? "text-red-500"
                      : newStatus === "late"
                      ? "text-amber-500"
                      : "text-blue-500"
                  }
                >
                  {newStatus}
                </span>
              </span>
              <span className="text-sm text-muted-foreground">
                {t("teacher.attendance.updatedAt", {
                  time: format(new Date(), "h:mm a"),
                })}
              </span>
            </div>
          ),
        });

        // Show additional notification with sonner
        sonnerToast.success(t("teacher.attendance.attendanceUpdated"), {
          description: t("teacher.attendance.studentNowMarked", {
            name: student.name,
            status: newStatus,
          }),
          duration: 4000,
          position: "bottom-right",
        });
      } catch (error) {
        console.error("Error updating attendance record:", error);
        throw error;
      }
    } catch (error: any) {
      // Revert local state on error
      setLocalStatuses((prev) => {
        const newState = { ...prev };
        delete newState[studentId];
        return newState;
      });

      console.error("Error updating attendance status:", error);

      // Show error toast with more details
      let errorMessage = t("teacher.attendance.failedToUpdate", {
        name: student.name,
        status: newStatus,
      });

      // Add more specific error details if available
      if (error.message) {
        if (error.message.includes("room ID")) {
          errorMessage = t("teacher.attendance.roomAssignmentIssue", {
            message: error.message,
            name: student.name,
          });
        } else {
          errorMessage += `. ${error.message}`;
        }
      }

      toast({
        title: t("teacher.attendance.errorUpdatingStatus"),
        description: errorMessage,
        variant: "destructive",
        duration: 5000,
      });
    }
  };

  return (
    <>
      <div className="rounded-md border w-full overflow-hidden">
        {/* Desktop Header - Hidden on mobile */}
        <div className="hidden md:grid grid-cols-12 gap-6 p-4 font-medium border-b">
          <div className="col-span-1">#</div>
          <div className="col-span-2">{t("common.photo")}</div>
          <div className="col-span-2">{t("students.directory.name")}</div>
          <div className="col-span-1 text-center">{t("common.id")}</div>
          <div className="col-span-2 pl-6">{t("students.directory.room")}</div>
          <div className="col-span-2">{t("students.directory.status")}</div>
          <div className="col-span-2 text-center">
            {t("students.directory.actions")}
          </div>
        </div>

        {students.length === 0 ? (
          <div className="p-4 text-center text-muted-foreground">
            {t("teacher.dashboard.noStudentsFound")}
          </div>
        ) : (
          students.map((student, index) => {
            const status = getEffectiveStatus(student.id);
            return (
              <div
                key={student.id}
                className="md:grid md:grid-cols-12 md:gap-6 p-2 md:p-4 items-center border-b last:border-b-0"
              >
                {/* Mobile Card View */}
                <div className="md:hidden flex flex-col space-y-1 w-full">
                  <div className="flex items-center space-x-2">
                    {student.photoUrl ? (
                      <img
                        src={student.photoUrl}
                        alt={student.name}
                        className="w-8 h-8 rounded-full object-cover ring-1 ring-primary/10"
                      />
                    ) : (
                      <div className="w-8 h-8 rounded-full bg-muted flex items-center justify-center ring-1 ring-primary/10">
                        <UserCircle className="w-5 h-5 text-muted-foreground" />
                      </div>
                    )}
                    <div className="flex-1 min-w-0">
                      <div className="font-medium truncate text-xs">
                        {student.name}
                      </div>
                      <div className="text-[10px] text-muted-foreground truncate">
                        {t("students.directory.room")}:{" "}
                        {student.roomNumber || t("students.directory.notSet")}
                      </div>
                    </div>
                    <Button
                      variant="link"
                      className="text-primary p-0 h-auto text-[10px]"
                      onClick={() => setSelectedStudent(student)}
                    >
                      {t("students.directory.viewDetails")}
                    </Button>
                  </div>

                  <div className="flex justify-between items-center pt-1 border-t border-gray-100">
                    <div className="text-[10px] font-medium">
                      {t("students.directory.status")}
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          className="w-[110px] justify-start gap-1 h-6 text-[9px] px-2"
                        >
                          {getStatusIcon(status)}
                          <span className="capitalize truncate max-w-[60px]">
                            {status}
                          </span>
                          <ChevronDown className="h-2 w-2 ml-auto flex-shrink-0" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-[110px]">
                        <DropdownMenuItem
                          onClick={() =>
                            updateAttendanceStatus(student.id, "present")
                          }
                          className="gap-1 text-[9px] py-1"
                        >
                          {getStatusIcon("present")}{" "}
                          {t("teacher.dashboard.present")}
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() =>
                            updateAttendanceStatus(student.id, "absent")
                          }
                          className="gap-1 text-[9px] py-1"
                        >
                          {getStatusIcon("absent")}{" "}
                          {t("teacher.dashboard.absent")}
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() =>
                            updateAttendanceStatus(student.id, "late")
                          }
                          className="gap-1 text-[9px] py-1"
                        >
                          {getStatusIcon("late")} {t("teacher.dashboard.late")}
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() =>
                            updateAttendanceStatus(student.id, "excused")
                          }
                          className="gap-1 text-[9px] py-1"
                        >
                          {getStatusIcon("excused")}{" "}
                          {t("teacher.dashboard.excused")}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>

                {/* Desktop View - Hidden on mobile */}
                <div className="hidden md:block md:col-span-1">{index + 1}</div>
                <div className="hidden md:block md:col-span-2">
                  {student.photoUrl ? (
                    <img
                      src={student.photoUrl}
                      alt={student.name}
                      className="w-10 h-10 rounded-full object-cover"
                    />
                  ) : (
                    <div className="w-10 h-10 rounded-full bg-muted flex items-center justify-center">
                      <UserCircle className="w-6 h-6 text-muted-foreground" />
                    </div>
                  )}
                </div>
                <div className="hidden md:block md:col-span-2 truncate">
                  {student.name}
                </div>
                <div className="hidden md:block md:col-span-1 text-center truncate">
                  {student.studentId}
                </div>
                <div className="hidden md:block md:col-span-2 pl-6">
                  {student.roomNumber ? (
                    student.roomNumber
                  ) : (
                    <span className="text-muted-foreground">
                      {t("students.directory.notSet")}
                    </span>
                  )}
                </div>
                <div className="hidden md:block md:col-span-2">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-[130px] justify-start gap-2"
                      >
                        {getStatusIcon(status)}
                        <span className="capitalize">{status}</span>
                        <ChevronDown className="h-4 w-4 ml-auto" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuItem
                        onClick={() =>
                          updateAttendanceStatus(student.id, "present")
                        }
                        className="gap-2"
                      >
                        {getStatusIcon("present")}{" "}
                        {t("teacher.dashboard.present")}
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() =>
                          updateAttendanceStatus(student.id, "absent")
                        }
                        className="gap-2"
                      >
                        {getStatusIcon("absent")}{" "}
                        {t("teacher.dashboard.absent")}
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() =>
                          updateAttendanceStatus(student.id, "late")
                        }
                        className="gap-2"
                      >
                        {getStatusIcon("late")} {t("teacher.dashboard.late")}
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() =>
                          updateAttendanceStatus(student.id, "excused")
                        }
                        className="gap-2"
                      >
                        {getStatusIcon("excused")}{" "}
                        {t("teacher.dashboard.excused")}
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                <div className="hidden md:block md:col-span-2 text-center">
                  <Button
                    variant="link"
                    className="text-primary p-0 h-auto"
                    onClick={() => setSelectedStudent(student)}
                  >
                    {t("students.directory.viewDetails")}
                  </Button>
                </div>
              </div>
            );
          })
        )}
      </div>

      <Dialog
        open={!!selectedStudent}
        onOpenChange={() => setSelectedStudent(null)}
      >
        <DialogContent className="sm:max-w-[425px] max-w-[90vw] p-3 sm:p-6">
          <DialogHeader>
            <DialogTitle>{t("students.profile.title")}</DialogTitle>
            <DialogDescription className="text-xs sm:text-sm">
              {t("students.profile.detailedInfo", {
                name: selectedStudent?.name,
              })}
            </DialogDescription>
          </DialogHeader>
          {selectedStudent && (
            <div className="grid gap-3 py-3">
              <div className="flex flex-col sm:flex-row items-center sm:items-start gap-3">
                {selectedStudent.photoUrl ? (
                  <img
                    src={selectedStudent.photoUrl}
                    alt={selectedStudent.name}
                    className="w-16 h-16 sm:w-20 sm:h-20 rounded-full object-cover"
                  />
                ) : (
                  <div className="w-16 h-16 sm:w-20 sm:h-20 rounded-full bg-muted flex items-center justify-center">
                    <UserCircle className="w-10 h-10 sm:w-12 sm:h-12 text-muted-foreground" />
                  </div>
                )}
                <div className="text-center sm:text-left">
                  <h3 className="font-medium text-base sm:text-lg">
                    {selectedStudent.name}
                  </h3>
                  <p className="text-xs sm:text-sm text-muted-foreground">
                    {t("students.profile.studentId")}:{" "}
                    {selectedStudent.studentId}
                  </p>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2 text-xs sm:text-sm">
                  <Mail className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" />
                  <span className="truncate">{selectedStudent.email}</span>
                </div>
                <div className="flex items-center gap-2 text-xs sm:text-sm">
                  <Book className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" />
                  <span className="truncate">
                    {t("students.profile.course")}:{" "}
                    {selectedStudent.course || t("students.directory.notSet")}
                  </span>
                </div>
                <div className="flex items-center gap-2 text-xs sm:text-sm">
                  <Home className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" />
                  <span className="truncate">
                    {t("students.profile.block")}:{" "}
                    {selectedStudent.blockName ||
                      t("students.directory.notAssigned")}
                  </span>
                </div>
                <div className="flex items-center gap-2 text-xs sm:text-sm">
                  <DoorClosed className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" />
                  <span className="truncate">
                    {t("students.profile.room")}:{" "}
                    {selectedStudent.roomNumber ||
                      t("students.directory.notAssigned")}
                  </span>
                </div>
              </div>

              <div className="mt-2 p-3 sm:p-4 bg-muted rounded-lg">
                <h4 className="font-medium mb-2 text-xs sm:text-sm">
                  {t("students.profile.todaysAttendance", "Today's Attendance")}
                </h4>
                <div className="flex items-center gap-2">
                  {getStatusIcon(getEffectiveStatus(selectedStudent.id))}
                  <span className="capitalize text-xs sm:text-sm">
                    {getEffectiveStatus(selectedStudent.id)}
                  </span>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
}
