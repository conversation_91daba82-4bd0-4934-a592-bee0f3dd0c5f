/**
 * 🎨 Logo Component
 * =====================================================
 * Consistent logo component that matches favicon design
 * Supports different variants and sizes
 */

import { cn } from "@/lib/utils";
import { useBranding } from "@/hooks/useBranding";

interface LogoProps {
  variant?: "square" | "horizontal" | "white";
  size?: "sm" | "md" | "lg" | "xl";
  className?: string;
  showText?: boolean;
}

const sizeClasses = {
  sm: "w-8 h-8",
  md: "w-12 h-12", 
  lg: "w-16 h-16",
  xl: "w-24 h-24"
};

const horizontalSizeClasses = {
  sm: "h-6 w-auto",
  md: "h-8 w-auto",
  lg: "h-10 w-auto", 
  xl: "h-12 w-auto"
};

export function Logo({ 
  variant = "square", 
  size = "md", 
  className,
  showText = false 
}: LogoProps) {
  const { branding } = useBranding();

  const getLogoSrc = () => {
    switch (variant) {
      case "horizontal":
        return "/logo-horizontal.svg";
      case "white":
        return "/logo-white.svg";
      default:
        return "/logo.svg";
    }
  };

  const getSizeClass = () => {
    if (variant === "horizontal") {
      return horizontalSizeClasses[size];
    }
    return sizeClasses[size];
  };

  return (
    <div className={cn("flex items-center gap-3", className)}>
      <img
        src={getLogoSrc()}
        alt={`${branding.APP_NAME} Logo`}
        className={cn(getSizeClass(), "flex-shrink-0")}
      />
      {showText && variant !== "horizontal" && (
        <div className="flex flex-col">
          <span className="font-bold text-primary text-lg leading-tight">
            {branding.APP_NAME}
          </span>
          <span className="text-xs text-muted-foreground">
            Attendance System
          </span>
        </div>
      )}
    </div>
  );
}

// Preset logo components for common use cases
export function NavbarLogo({ className }: { className?: string }) {
  return (
    <Logo 
      variant="horizontal" 
      size="md" 
      className={className}
    />
  );
}

export function LoginLogo({ className }: { className?: string }) {
  return (
    <Logo 
      variant="square" 
      size="xl" 
      className={cn("justify-center", className)}
    />
  );
}

export function LoadingLogo({ className }: { className?: string }) {
  return (
    <Logo 
      variant="square" 
      size="lg" 
      className={cn("justify-center animate-pulse", className)}
    />
  );
}

export function FooterLogo({ className }: { className?: string }) {
  return (
    <Logo 
      variant="square" 
      size="sm" 
      showText={true}
      className={className}
    />
  );
}
