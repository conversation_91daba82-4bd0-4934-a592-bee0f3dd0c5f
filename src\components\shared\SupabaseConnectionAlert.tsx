import { useState, useEffect } from "react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { AlertCircle, RefreshCw } from "lucide-react";
import { checkSupabaseConnection } from "@/lib/supabase";
import { useTranslation } from "react-i18next";

export default function SupabaseConnectionAlert() {
  const [connectionStatus, setConnectionStatus] = useState<{
    connected: boolean;
    error?: string;
    details?: string;
    checking: boolean;
  }>({
    connected: true,
    checking: false,
  });
  const { t } = useTranslation();

  // Check connection on component mount
  useEffect(() => {
    checkConnection();
  }, []);

  // Function to check Supabase connection
  const checkConnection = async () => {
    setConnectionStatus((prev) => ({ ...prev, checking: true }));

    try {
      const result = await checkSupabaseConnection();
      setConnectionStatus({
        ...result,
        checking: false,
      });
    } catch (error) {
      setConnectionStatus({
        connected: false,
        error: error instanceof Error ? error.message : "Unknown error",
        details: "Failed to check connection status",
        checking: false,
      });
    }
  };

  // If connected, don't show anything
  if (connectionStatus.connected) {
    return null;
  }

  return (
    <Alert variant="destructive" className="mb-4">
      <AlertCircle className="h-4 w-4" />
      <AlertTitle>{t("Database Connection Error")}</AlertTitle>
      <AlertDescription className="space-y-2">
        <p>
          {t(
            "Unable to connect to the database. This might be due to network issues or the database being temporarily unavailable."
          )}
        </p>
        {connectionStatus.error && (
          <p className="text-sm font-mono bg-destructive/10 p-2 rounded">
            {connectionStatus.error}
          </p>
        )}
        {connectionStatus.details && (
          <p className="text-sm">{connectionStatus.details}</p>
        )}
        <div className="flex items-center gap-2 mt-2">
          <Button
            variant="outline"
            size="sm"
            onClick={checkConnection}
            disabled={connectionStatus.checking}
          >
            {connectionStatus.checking ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                {t("Checking...")}
              </>
            ) : (
              <>
                <RefreshCw className="h-4 w-4 mr-2" />
                {t("Try Again")}
              </>
            )}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.location.reload()}
          >
            {t("Reload Page")}
          </Button>
        </div>
      </AlertDescription>
    </Alert>
  );
}
