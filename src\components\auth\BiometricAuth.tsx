import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  Fingerprint, 
  Scan, 
  Mic, 
  Eye, 
  Shield, 
  CheckCircle, 
  XCircle, 
  Loader2,
  Zap,
  Lock,
  Unlock
} from "lucide-react";
import { biometricService, type BiometricType, type BiometricAuthResult } from "@/lib/services/biometric-service";
import { useToast } from "@/hooks/use-toast";
import { cn } from "@/lib/utils";

interface BiometricAuthProps {
  userId: string;
  onSuccess: (result: BiometricAuthResult) => void;
  onError: (error: string) => void;
  mode: 'register' | 'authenticate';
  className?: string;
}

const biometricIcons = {
  fingerprint: Fingerprint,
  face: <PERSON><PERSON>,
  voice: <PERSON><PERSON>,
  iris: <PERSON>,
};

const biometricLabels = {
  fingerprint: 'Fingerprint',
  face: 'Face Recognition',
  voice: 'Voice Recognition',
  iris: 'Iris Scan',
};

const biometricColors = {
  fingerprint: 'from-blue-500 to-cyan-500',
  face: 'from-green-500 to-emerald-500',
  voice: 'from-purple-500 to-pink-500',
  iris: 'from-orange-500 to-red-500',
};

export default function BiometricAuth({ 
  userId, 
  onSuccess, 
  onError, 
  mode, 
  className 
}: BiometricAuthProps) {
  const [supportedTypes, setSupportedTypes] = useState<BiometricType[]>([]);
  const [selectedType, setSelectedType] = useState<BiometricType | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [status, setStatus] = useState<'idle' | 'scanning' | 'success' | 'error'>('idle');
  const [statusMessage, setStatusMessage] = useState('');
  const [registeredBiometrics, setRegisteredBiometrics] = useState<BiometricType[]>([]);
  
  const { toast } = useToast();

  // Check biometric support on mount
  useEffect(() => {
    const checkSupport = async () => {
      try {
        const support = await biometricService.isSupported();
        setSupportedTypes(support.types);
        
        if (support.types.length === 0) {
          setStatusMessage('No biometric authentication methods available on this device');
          setStatus('error');
        }

        // Get registered biometrics for this user
        const userBiometrics = await biometricService.getUserBiometrics(userId);
        setRegisteredBiometrics(userBiometrics.map(b => b.type as BiometricType));
      } catch (error) {
        console.error('Error checking biometric support:', error);
        setStatusMessage('Failed to check biometric support');
        setStatus('error');
      }
    };

    checkSupport();
  }, [userId]);

  // Handle biometric action (register or authenticate)
  const handleBiometricAction = async (type: BiometricType) => {
    if (isProcessing) return;

    setSelectedType(type);
    setIsProcessing(true);
    setStatus('scanning');
    setProgress(0);
    setStatusMessage(`Preparing ${biometricLabels[type].toLowerCase()}...`);

    try {
      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      let result: BiometricAuthResult;

      if (mode === 'register') {
        setStatusMessage(`Place your ${type === 'voice' ? 'voice' : type} on the sensor...`);
        result = await biometricService.registerBiometric(userId, type, {
          displayName: `User ${userId}`,
          timeout: 30000
        });
      } else {
        setStatusMessage(`Authenticating with ${biometricLabels[type].toLowerCase()}...`);
        result = await biometricService.authenticateBiometric(userId, type);
      }

      clearInterval(progressInterval);
      setProgress(100);

      if (result.success) {
        setStatus('success');
        setStatusMessage(
          mode === 'register' 
            ? `${biometricLabels[type]} registered successfully!`
            : `Authentication successful! Confidence: ${Math.round(result.confidence * 100)}%`
        );
        
        toast({
          title: "Success",
          description: statusMessage,
        });

        // Update registered biometrics if registration was successful
        if (mode === 'register') {
          setRegisteredBiometrics(prev => [...prev, type]);
        }

        onSuccess(result);
      } else {
        throw new Error(result.error || 'Biometric operation failed');
      }
    } catch (error) {
      setStatus('error');
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      setStatusMessage(errorMessage);
      
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });

      onError(errorMessage);
    } finally {
      setIsProcessing(false);
      
      // Reset after 3 seconds
      setTimeout(() => {
        setStatus('idle');
        setSelectedType(null);
        setProgress(0);
        setStatusMessage('');
      }, 3000);
    }
  };

  // Remove biometric registration
  const handleRemoveBiometric = async (type: BiometricType) => {
    try {
      const success = await biometricService.deleteBiometricRegistration(userId, type);
      if (success) {
        setRegisteredBiometrics(prev => prev.filter(t => t !== type));
        toast({
          title: "Success",
          description: `${biometricLabels[type]} removed successfully`,
        });
      } else {
        throw new Error('Failed to remove biometric');
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to remove biometric registration",
        variant: "destructive",
      });
    }
  };

  if (supportedTypes.length === 0 && status !== 'error') {
    return (
      <Card className={cn("w-full max-w-md mx-auto", className)}>
        <CardContent className="flex items-center justify-center p-8">
          <div className="text-center">
            <Shield className="w-12 h-12 mx-auto text-gray-400 mb-4" />
            <p className="text-gray-600">Checking biometric support...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("w-full max-w-md mx-auto overflow-hidden", className)}>
      <CardHeader className="text-center bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <CardTitle className="flex items-center justify-center gap-2">
          <Shield className="w-6 h-6" />
          {mode === 'register' ? 'Register Biometric' : 'Biometric Authentication'}
        </CardTitle>
      </CardHeader>
      
      <CardContent className="p-6 space-y-6">
        {/* Status Display */}
        {status !== 'idle' && (
          <div className="text-center space-y-4">
            <div className={cn(
              "w-20 h-20 mx-auto rounded-full flex items-center justify-center",
              status === 'scanning' && "bg-gradient-to-r from-blue-500 to-purple-500 animate-pulse",
              status === 'success' && "bg-gradient-to-r from-green-500 to-emerald-500",
              status === 'error' && "bg-gradient-to-r from-red-500 to-pink-500"
            )}>
              {status === 'scanning' && selectedType && (
                <div className="text-white animate-bounce">
                  {biometricIcons[selectedType] && 
                    React.createElement(biometricIcons[selectedType], { size: 32 })
                  }
                </div>
              )}
              {status === 'success' && <CheckCircle className="w-8 h-8 text-white" />}
              {status === 'error' && <XCircle className="w-8 h-8 text-white" />}
            </div>
            
            {isProcessing && (
              <div className="space-y-2">
                <Progress value={progress} className="w-full" />
                <p className="text-sm text-gray-600">{statusMessage}</p>
              </div>
            )}
            
            {!isProcessing && (
              <p className={cn(
                "text-sm font-medium",
                status === 'success' && "text-green-600",
                status === 'error' && "text-red-600"
              )}>
                {statusMessage}
              </p>
            )}
          </div>
        )}

        {/* Biometric Options */}
        {status === 'idle' && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-center">
              {mode === 'register' ? 'Choose Biometric Method' : 'Select Authentication Method'}
            </h3>
            
            <div className="grid grid-cols-2 gap-3">
              {supportedTypes.map((type) => {
                const Icon = biometricIcons[type];
                const isRegistered = registeredBiometrics.includes(type);
                const canUse = mode === 'authenticate' ? isRegistered : true;
                
                return (
                  <div key={type} className="relative">
                    <Button
                      onClick={() => handleBiometricAction(type)}
                      disabled={!canUse || isProcessing}
                      className={cn(
                        "w-full h-24 flex flex-col items-center justify-center gap-2 relative overflow-hidden",
                        "bg-gradient-to-r text-white border-0 transition-all duration-300",
                        "hover:scale-105 hover:shadow-lg",
                        biometricColors[type],
                        !canUse && "opacity-50 cursor-not-allowed"
                      )}
                    >
                      <div className="absolute inset-0 bg-white/10 backdrop-blur-sm" />
                      <Icon className="w-6 h-6 relative z-10" />
                      <span className="text-xs font-medium relative z-10">
                        {biometricLabels[type]}
                      </span>
                      
                      {isRegistered && (
                        <Badge className="absolute top-1 right-1 bg-green-500 text-white text-xs">
                          <CheckCircle className="w-3 h-3" />
                        </Badge>
                      )}
                    </Button>
                    
                    {/* Remove button for registered biometrics */}
                    {mode === 'register' && isRegistered && (
                      <Button
                        onClick={() => handleRemoveBiometric(type)}
                        variant="outline"
                        size="sm"
                        className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 bg-white border-red-300 text-red-600 hover:bg-red-50"
                      >
                        Remove
                      </Button>
                    )}
                  </div>
                );
              })}
            </div>

            {/* Instructions */}
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="flex items-start gap-3">
                <Zap className="w-5 h-5 text-blue-600 mt-0.5" />
                <div className="text-sm text-blue-800">
                  <p className="font-medium mb-1">
                    {mode === 'register' ? 'Registration Tips:' : 'Authentication Tips:'}
                  </p>
                  <ul className="list-disc list-inside space-y-1 text-xs">
                    {mode === 'register' ? (
                      <>
                        <li>Ensure good lighting for face recognition</li>
                        <li>Keep your finger clean for fingerprint</li>
                        <li>Speak clearly for voice recognition</li>
                        <li>You can register multiple methods</li>
                      </>
                    ) : (
                      <>
                        <li>Use the same method you registered with</li>
                        <li>Ensure stable positioning</li>
                        <li>Follow the on-screen instructions</li>
                        <li>Try again if authentication fails</li>
                      </>
                    )}
                  </ul>
                </div>
              </div>
            </div>

            {/* Security Notice */}
            <div className="flex items-center gap-2 text-xs text-gray-500 justify-center">
              <Lock className="w-4 h-4" />
              <span>Your biometric data is encrypted and stored securely</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
