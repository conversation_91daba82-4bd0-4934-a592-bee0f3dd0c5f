// Follow this setup guide to integrate the Deno runtime into your application:
// https://deno.land/manual/examples/supabase

import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

interface RequestBody {
  to: string;
  message: string;
}

serve(async (req) => {
  // CORS headers
  if (req.method === "OPTIONS") {
    return new Response("ok", {
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "POST",
        "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
      },
    });
  }

  try {
    // Get the request body
    const body: RequestBody = await req.json();
    const { to, message } = body;

    // Validate request
    if (!to || !message) {
      return new Response(
        JSON.stringify({ error: "Missing required fields: to, message" }),
        {
          status: 400,
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
          },
        }
      );
    }

    // Create a Supabase client
    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? ""
    );

    // Get Twilio configuration from system_settings
    const { data: configData, error: configError } = await supabaseClient
      .from("system_settings")
      .select("setting_value")
      .eq("setting_name", "sms_service_config")
      .single();

    if (configError || !configData) {
      return new Response(
        JSON.stringify({ error: "Failed to retrieve SMS configuration" }),
        {
          status: 500,
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
          },
        }
      );
    }

    const config = configData.setting_value;
    const { accountSid, authToken, phoneNumber } = config;

    if (!accountSid || !authToken || !phoneNumber) {
      return new Response(
        JSON.stringify({ error: "Incomplete SMS configuration" }),
        {
          status: 500,
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
          },
        }
      );
    }

    // Send SMS using Twilio API
    const twilioApiUrl = `https://api.twilio.com/2010-04-01/Accounts/${accountSid}/Messages.json`;
    const formData = new URLSearchParams();
    formData.append("To", to);
    formData.append("From", phoneNumber);
    formData.append("Body", message);

    const auth = btoa(`${accountSid}:${authToken}`);
    const twilioResponse = await fetch(twilioApiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        "Authorization": `Basic ${auth}`,
      },
      body: formData,
    });

    const twilioData = await twilioResponse.json();

    if (!twilioResponse.ok) {
      return new Response(
        JSON.stringify({ error: "Failed to send SMS", details: twilioData }),
        {
          status: 500,
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
          },
        }
      );
    }

    // Log the SMS in the database
    await supabaseClient.from("notification_logs").insert({
      notification_type: "sms",
      recipient: to,
      success: true,
      created_at: new Date().toISOString(),
    });

    return new Response(
      JSON.stringify({ success: true, sid: twilioData.sid }),
      {
        status: 200,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
        },
      }
    );
  } catch (error) {
    return new Response(
      JSON.stringify({ error: "Internal server error", details: error.message }),
      {
        status: 500,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
        },
      }
    );
  }
});
