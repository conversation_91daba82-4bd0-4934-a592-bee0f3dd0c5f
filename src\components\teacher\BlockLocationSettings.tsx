import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";
import { Loader2, MapPin, AlertCircle } from "lucide-react";
import { supabase } from "@/lib/supabase";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Block } from "@/lib/types";
import { createBlockLocationsTable } from "@/lib/migrations.new";
import { useTranslation } from "react-i18next";

interface BlockLocationSettingsProps {
  blockId: string;
  blockName: string;
}

export function BlockLocationSettings({
  blockId,
  blockName,
}: BlockLocationSettingsProps) {
  const [loading, setLoading] = useState(false);
  const [updating, setUpdating] = useState(false);
  const [locationPermission, setLocationPermission] =
    useState<PermissionState | null>(null);
  const [location, setLocation] = useState<{
    latitude: number;
    longitude: number;
    radius_meters: number;
  } | null>(null);
  const { toast } = useToast();
  const { t } = useTranslation();

  useEffect(() => {
    checkLocationPermission();
    ensureTableExists();
    fetchBlockLocation();
  }, [blockId]);

  const checkLocationPermission = async () => {
    try {
      // Check if the Permissions API is supported
      if ("permissions" in navigator) {
        const permission = await navigator.permissions.query({
          name: "geolocation",
        });
        setLocationPermission(permission.state);

        // Listen for permission changes
        permission.addEventListener("change", () => {
          setLocationPermission(permission.state);
        });
      } else {
        // Fallback for browsers that don't support the Permissions API
        setLocationPermission("prompt");
      }
    } catch (error) {
      console.error("Error checking location permission:", error);
      setLocationPermission("prompt");
    }
  };

  const getCurrentLocation = (): Promise<GeolocationPosition> => {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error("Geolocation is not supported by your browser"));
        return;
      }

      navigator.geolocation.getCurrentPosition(resolve, reject, {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 0,
      });
    });
  };

  const ensureTableExists = async (): Promise<boolean> => {
    try {
      // Check if block_locations table exists
      const { data, error } = await supabase
        .from("block_locations")
        .select("id")
        .limit(1);

      if (error && error.code === "PGRST204") {
        // Table doesn't exist, create it
        console.log("Block locations table does not exist, creating it...");
        const result = await createBlockLocationsTable();
        return result;
      } else if (error) {
        console.error("Error checking block_locations table:", error);
        // Try to create the table anyway
        console.log("Attempting to create block_locations table...");
        const result = await createBlockLocationsTable();
        return result;
      }

      return true;
    } catch (error) {
      console.error("Error ensuring table exists:", error);
      return false;
    }
  };

  const fetchBlockLocation = async () => {
    if (!blockId) return;

    setLoading(true);
    try {
      const { data, error } = await supabase
        .from("block_locations")
        .select("*")
        .eq("block_id", blockId)
        .single();

      if (error) {
        if (error.code === "PGRST116") {
          // No location set yet, this is fine
          setLocation(null);
        } else if (error.code === "PGRST204") {
          // Table doesn't exist yet
          console.warn("Block locations table does not exist");
          setLocation(null);
        } else {
          throw error;
        }
      } else if (data) {
        setLocation({
          latitude: Number(data.latitude),
          longitude: Number(data.longitude),
          radius_meters: data.radius_meters,
        });
      }
    } catch (error) {
      console.error("Error fetching block location:", error);
      toast({
        title: "Error",
        description: "Failed to fetch block location. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSetCurrentLocation = async () => {
    try {
      setUpdating(true);
      console.log("Getting current location...");
      const position = await getCurrentLocation();
      console.log("Current position:", position.coords);

      // Check if table exists first
      console.log("Verifying table and permissions...");
      const tableExists = await ensureTableExists();
      if (!tableExists) {
        throw new Error("Block locations table is not properly configured");
      }
      console.log("Table exists and teacher has access");

      const locationData = {
        block_id: blockId,
        latitude: position.coords.latitude,
        longitude: position.coords.longitude,
        radius_meters: location?.radius_meters || 50,
      };

      console.log("Saving location data:", locationData);

      // Upsert the location data
      const { data: savedLocation, error } = await supabase
        .from("block_locations")
        .upsert(locationData, {
          onConflict: "block_id",
        })
        .select()
        .single();

      if (error) {
        console.error("Error saving location:", error);
        throw new Error("Failed to save location. Please try again.");
      }

      if (!savedLocation) {
        console.error("Location not found after saving");
        throw new Error("Location was not saved correctly. Please try again.");
      }

      console.log("Location saved successfully:", savedLocation);

      setLocation({
        latitude: position.coords.latitude,
        longitude: position.coords.longitude,
        radius_meters: location?.radius_meters || 50,
      });

      toast({
        title: t("teacher.settings.locationUpdated"),
        description: t("teacher.settings.blockLocationUpdatedMessage", {
          blockName,
        }),
      });

      // Update permission state after successful location access
      checkLocationPermission();
    } catch (error) {
      console.error("Error setting location:", error);
      let errorMessage = "Failed to set block location";

      if (error instanceof GeolocationPositionError) {
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage =
              "Please allow location access in your browser settings to set block location";
            setLocationPermission("denied");
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage =
              "Location information is unavailable. Please try again";
            break;
          case error.TIMEOUT:
            errorMessage = "Location request timed out. Please try again";
            break;
        }
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      toast({
        title: t("common.error"),
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setUpdating(false);
    }
  };

  const handleRadiusChange = async (newRadius: number) => {
    if (!location) return;

    try {
      setUpdating(true);

      const { error } = await supabase
        .from("block_locations")
        .update({ radius_meters: newRadius })
        .eq("block_id", blockId);

      if (error) throw error;

      setLocation({
        ...location,
        radius_meters: newRadius,
      });

      toast({
        title: t("teacher.settings.radiusUpdated"),
        description: t("teacher.settings.blockRadiusUpdatedMessage", {
          blockName,
        }),
      });
    } catch (error) {
      console.error("Error updating radius:", error);
      toast({
        title: t("common.error"),
        description: t("teacher.settings.errorUpdatingRadius"),
        variant: "destructive",
      });
    } finally {
      setUpdating(false);
    }
  };

  const renderLocationPermissionMessage = () => {
    if (locationPermission === "denied") {
      return (
        <Alert variant="destructive" className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {t("teacher.settings.locationPermissionDenied")}
          </AlertDescription>
        </Alert>
      );
    }
    return null;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("teacher.settings.blockLocationSettings")}</CardTitle>
        <CardDescription>
          {t("teacher.settings.blockLocationDescription").replace(
            "{blockName}",
            blockName
          )}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {renderLocationPermissionMessage()}

        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <Button
              onClick={handleSetCurrentLocation}
              disabled={updating || locationPermission === "denied"}
              className="flex items-center gap-2"
            >
              {updating ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <MapPin className="h-4 w-4" />
              )}
              {location
                ? t("teacher.settings.updateCurrentLocation")
                : t("teacher.settings.setCurrentLocation")}
            </Button>
          </div>

          {location && (
            <>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>{t("teacher.settings.latitude")}</Label>
                  <Input type="text" value={location.latitude} readOnly />
                </div>
                <div className="space-y-2">
                  <Label>{t("teacher.settings.longitude")}</Label>
                  <Input type="text" value={location.longitude} readOnly />
                </div>
              </div>
              <div className="space-y-2">
                <Label>{t("teacher.settings.attendanceRadius")}</Label>
                <Input
                  type="number"
                  value={location.radius_meters}
                  onChange={(e) => handleRadiusChange(Number(e.target.value))}
                  min={10}
                  max={1000}
                />
                <p className="text-sm text-muted-foreground">
                  {t("teacher.settings.studentsWithinRadiusBlock", {
                    blockName,
                  })}
                </p>
              </div>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
