interface LoadingSpinnerProps {
  message?: string;
  size?: "sm" | "md" | "lg";
  className?: string;
}

export function LoadingSpinner({
  message = "Loading...",
  size = "md",
  className = "",
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: "h-6 w-6",
    md: "h-8 w-8",
    lg: "h-12 w-12",
  };

  return (
    <div className={`flex flex-col items-center ${className}`}>
      <div
        className={`animate-spin rounded-full border-t-2 border-b-2 border-primary mb-4 ${sizeClasses[size]}`}
      ></div>
      <p className="text-muted-foreground">{message}</p>
    </div>
  );
}
