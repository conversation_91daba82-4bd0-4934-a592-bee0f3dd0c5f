import { useState, useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/context/AuthContext";
import { AlertCircle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { supabase } from "@/lib/supabase";
import { DevLoginHelper } from "./DevLoginHelper";

export default function LoginForm() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [loginError, setLoginError] = useState<string | null>(null);
  const [customLoginMessage, setCustomLoginMessage] = useState<string | null>(
    null
  );
  const { toast } = useToast();
  const { signIn } = useAuth();

  // Function to fetch custom login message
  const fetchCustomLoginMessage = async () => {
    try {
      // Get the school ID from URL if available
      const urlParams = new URLSearchParams(window.location.search);
      const schoolId = urlParams.get("school");

      // Set up headers with school ID if available
      const headers: Record<string, string> = {};
      if (schoolId) {
        headers["x-school-id"] = schoolId;
      }

      // Try the public function that allows anonymous access
      const { data, error } = await supabase.rpc(
        "get_public_login_message",
        {},
        { headers }
      );

      if (!error && data) {
        // Handle different formats of data
        let messageToSet = null;

        if (typeof data === "string") {
          messageToSet = data.trim();
        } else if (typeof data === "object") {
          // It could be { value: "message" } or just the value itself
          if (data.value !== undefined) {
            messageToSet = data.value.trim();
          } else {
            try {
              messageToSet = JSON.stringify(data).trim();
            } catch (e) {
              console.error("Error processing message:", e);
            }
          }
        }

        // Only set the message if it's not empty
        if (messageToSet && messageToSet.length > 0) {
          setCustomLoginMessage(messageToSet);
        } else {
          // If message is empty, set to null to hide the container
          setCustomLoginMessage(null);
        }
        return;
      }

      // No message found or error occurred, don't show any message
      setCustomLoginMessage(null);
    } catch (error) {
      console.error("Error fetching custom login message:", error);
      // Don't show any message on error
      setCustomLoginMessage(null);
    }
  };

  // Fetch custom login message on component mount
  useEffect(() => {
    fetchCustomLoginMessage();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setLoginError(null);

    try {
      await signIn(email, password);
      // Navigation is handled in AuthContext
    } catch (error: any) {
      console.error("Login error:", error);

      // Format error message for better user experience
      let errorMessage = "Login failed. Please check your credentials.";

      if (error.message) {
        if (error.message.includes("Invalid login credentials")) {
          errorMessage = "Invalid email or password. Please try again.";
        } else if (
          error.message.includes("Failed to fetch") ||
          error.message.includes("NetworkError") ||
          error.message.includes("Unexpected end of input")
        ) {
          errorMessage =
            "Network error. Please check your internet connection and try again.";
        } else {
          // Use the original error message
          errorMessage = error.message;
        }
      }

      // Show specific error in the form
      setLoginError(errorMessage);

      // If it's a network error, suggest refreshing the page
      if (errorMessage.includes("Network error")) {
        setTimeout(() => {
          if (confirm("Would you like to refresh the page and try again?")) {
            window.location.reload();
          }
        }, 1500);
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="text-2xl text-primary">
          Attendance Tracking System
        </CardTitle>
        <CardDescription>Login to access the attendance system</CardDescription>
      </CardHeader>
      <CardContent>
        {loginError && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{loginError}</AlertDescription>
          </Alert>
        )}

        {customLoginMessage && (
          <div className="mb-6 p-5 bg-gradient-to-r from-primary/10 to-primary/5 rounded-lg text-sm border-l-4 border-primary shadow-md animate-fadeIn">
            <p className="text-primary font-medium leading-relaxed">
              {customLoginMessage}
            </p>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              disabled={isLoading}
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="password">Password</Label>
            <Input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              disabled={isLoading}
              required
            />
          </div>
          <Button
            type="submit"
            className="w-full bg-primary hover:bg-primary-light"
            disabled={isLoading}
          >
            {isLoading ? "Logging in..." : "Login"}
          </Button>
        </form>
      </CardContent>
      <CardFooter className="flex flex-col gap-2">
        <div className="text-sm text-center">
          <span className="text-muted-foreground">Don't have an account? </span>
          <Link to="/signup" className="text-primary font-medium">
            Sign up
          </Link>
        </div>

        {/* Development login helper - only shown in dev mode */}
        <DevLoginHelper />
      </CardFooter>
    </Card>
  );
}
