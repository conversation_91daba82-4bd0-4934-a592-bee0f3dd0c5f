import { supabase } from "@/integrations/supabase/client";
import CryptoJ<PERSON> from "crypto-js";

// Biometric types supported
export type BiometricType = 'fingerprint' | 'face' | 'voice' | 'iris';

// Biometric authentication result
export interface BiometricAuthResult {
  success: boolean;
  type: BiometricType;
  confidence: number;
  template_hash?: string;
  error?: string;
  device_info?: string;
}

// Biometric registration data
export interface BiometricRegistration {
  user_id: string;
  type: BiometricType;
  template_hash: string;
  confidence_threshold: number;
  device_info: string;
  created_at: string;
}

// WebAuthn credential for modern biometric auth
export interface WebAuthnCredential {
  id: string;
  rawId: ArrayBuffer;
  response: AuthenticatorAttestationResponse | AuthenticatorAssertionResponse;
  type: 'public-key';
}

class BiometricService {
  private readonly ENCRYPTION_KEY = 'biometric_encryption_key_2024';
  private readonly CONFIDENCE_THRESHOLD = 0.85;

  /**
   * Check if biometric authentication is supported
   */
  async isSupported(): Promise<{ supported: boolean; types: BiometricType[] }> {
    const supportedTypes: BiometricType[] = [];

    try {
      // Check WebAuthn support (modern biometric API)
      if (window.PublicKeyCredential) {
        const available = await PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable();
        if (available) {
          supportedTypes.push('fingerprint', 'face');
        }
      }

      // Check Web Authentication API
      if ('credentials' in navigator) {
        supportedTypes.push('fingerprint');
      }

      // Check MediaDevices for camera (face recognition)
      if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        supportedTypes.push('face');
      }

      // Check Web Speech API (voice recognition)
      if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
        supportedTypes.push('voice');
      }

      return {
        supported: supportedTypes.length > 0,
        types: supportedTypes
      };
    } catch (error) {
      console.error('Error checking biometric support:', error);
      return { supported: false, types: [] };
    }
  }

  /**
   * Register biometric authentication for a user
   */
  async registerBiometric(
    userId: string, 
    type: BiometricType,
    options?: { displayName?: string; timeout?: number }
  ): Promise<BiometricAuthResult> {
    try {
      let result: BiometricAuthResult;

      switch (type) {
        case 'fingerprint':
        case 'face':
          result = await this.registerWebAuthn(userId, type, options);
          break;
        case 'voice':
          result = await this.registerVoice(userId, options);
          break;
        default:
          throw new Error(`Biometric type ${type} not supported`);
      }

      if (result.success && result.template_hash) {
        // Store biometric registration in database
        await this.storeBiometricRegistration({
          user_id: userId,
          type: type,
          template_hash: result.template_hash,
          confidence_threshold: this.CONFIDENCE_THRESHOLD,
          device_info: result.device_info || navigator.userAgent,
          created_at: new Date().toISOString()
        });
      }

      return result;
    } catch (error) {
      console.error('Biometric registration error:', error);
      return {
        success: false,
        type: type,
        confidence: 0,
        error: error instanceof Error ? error.message : 'Registration failed'
      };
    }
  }

  /**
   * Authenticate using biometrics
   */
  async authenticateBiometric(
    userId: string, 
    type: BiometricType
  ): Promise<BiometricAuthResult> {
    try {
      // Get stored biometric data
      const storedBiometric = await this.getBiometricRegistration(userId, type);
      if (!storedBiometric) {
        throw new Error('No biometric registration found');
      }

      let result: BiometricAuthResult;

      switch (type) {
        case 'fingerprint':
        case 'face':
          result = await this.authenticateWebAuthn(userId, type);
          break;
        case 'voice':
          result = await this.authenticateVoice(userId);
          break;
        default:
          throw new Error(`Biometric type ${type} not supported`);
      }

      // Verify confidence threshold
      if (result.success && result.confidence < storedBiometric.confidence_threshold) {
        result.success = false;
        result.error = 'Biometric confidence below threshold';
      }

      return result;
    } catch (error) {
      console.error('Biometric authentication error:', error);
      return {
        success: false,
        type: type,
        confidence: 0,
        error: error instanceof Error ? error.message : 'Authentication failed'
      };
    }
  }

  /**
   * Register WebAuthn biometric (fingerprint/face)
   */
  private async registerWebAuthn(
    userId: string, 
    type: BiometricType,
    options?: { displayName?: string; timeout?: number }
  ): Promise<BiometricAuthResult> {
    if (!window.PublicKeyCredential) {
      throw new Error('WebAuthn not supported');
    }

    const challenge = crypto.getRandomValues(new Uint8Array(32));
    const userIdBytes = new TextEncoder().encode(userId);

    const createCredentialOptions: CredentialCreationOptions = {
      publicKey: {
        challenge: challenge,
        rp: {
          name: "Attendance Tracking System",
          id: window.location.hostname,
        },
        user: {
          id: userIdBytes,
          name: userId,
          displayName: options?.displayName || `User ${userId}`,
        },
        pubKeyCredParams: [
          { alg: -7, type: "public-key" }, // ES256
          { alg: -257, type: "public-key" }, // RS256
        ],
        authenticatorSelection: {
          authenticatorAttachment: "platform",
          userVerification: "required",
          requireResidentKey: false,
        },
        timeout: options?.timeout || 60000,
        attestation: "direct",
      },
    };

    const credential = await navigator.credentials.create(createCredentialOptions) as PublicKeyCredential;
    
    if (!credential) {
      throw new Error('Failed to create credential');
    }

    // Create template hash from credential
    const credentialId = Array.from(new Uint8Array(credential.rawId))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');
    
    const templateHash = this.encryptData(credentialId);

    return {
      success: true,
      type: type,
      confidence: 0.95, // WebAuthn provides high confidence
      template_hash: templateHash,
      device_info: navigator.userAgent
    };
  }

  /**
   * Authenticate WebAuthn biometric
   */
  private async authenticateWebAuthn(
    userId: string, 
    type: BiometricType
  ): Promise<BiometricAuthResult> {
    if (!window.PublicKeyCredential) {
      throw new Error('WebAuthn not supported');
    }

    const challenge = crypto.getRandomValues(new Uint8Array(32));

    const getCredentialOptions: CredentialRequestOptions = {
      publicKey: {
        challenge: challenge,
        timeout: 60000,
        userVerification: "required",
        rpId: window.location.hostname,
      },
    };

    const credential = await navigator.credentials.get(getCredentialOptions) as PublicKeyCredential;
    
    if (!credential) {
      throw new Error('Authentication failed');
    }

    return {
      success: true,
      type: type,
      confidence: 0.95,
      device_info: navigator.userAgent
    };
  }

  /**
   * Register voice biometric
   */
  private async registerVoice(
    userId: string,
    options?: { timeout?: number }
  ): Promise<BiometricAuthResult> {
    return new Promise((resolve, reject) => {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      
      if (!SpeechRecognition) {
        reject(new Error('Speech recognition not supported'));
        return;
      }

      const recognition = new SpeechRecognition();
      recognition.continuous = false;
      recognition.interimResults = false;
      recognition.lang = 'en-US';

      const timeout = setTimeout(() => {
        recognition.stop();
        reject(new Error('Voice registration timeout'));
      }, options?.timeout || 10000);

      recognition.onresult = (event) => {
        clearTimeout(timeout);
        const transcript = event.results[0][0].transcript;
        const confidence = event.results[0][0].confidence;
        
        // Create voice template hash
        const voiceTemplate = this.createVoiceTemplate(transcript, confidence);
        const templateHash = this.encryptData(voiceTemplate);

        resolve({
          success: true,
          type: 'voice',
          confidence: confidence,
          template_hash: templateHash,
          device_info: navigator.userAgent
        });
      };

      recognition.onerror = (event) => {
        clearTimeout(timeout);
        reject(new Error(`Voice recognition error: ${event.error}`));
      };

      recognition.start();
    });
  }

  /**
   * Authenticate voice biometric
   */
  private async authenticateVoice(userId: string): Promise<BiometricAuthResult> {
    return new Promise((resolve, reject) => {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      
      if (!SpeechRecognition) {
        reject(new Error('Speech recognition not supported'));
        return;
      }

      const recognition = new SpeechRecognition();
      recognition.continuous = false;
      recognition.interimResults = false;
      recognition.lang = 'en-US';

      const timeout = setTimeout(() => {
        recognition.stop();
        reject(new Error('Voice authentication timeout'));
      }, 10000);

      recognition.onresult = (event) => {
        clearTimeout(timeout);
        const confidence = event.results[0][0].confidence;

        resolve({
          success: confidence > this.CONFIDENCE_THRESHOLD,
          type: 'voice',
          confidence: confidence,
          device_info: navigator.userAgent
        });
      };

      recognition.onerror = (event) => {
        clearTimeout(timeout);
        reject(new Error(`Voice authentication error: ${event.error}`));
      };

      recognition.start();
    });
  }

  /**
   * Create voice template from transcript and confidence
   */
  private createVoiceTemplate(transcript: string, confidence: number): string {
    const template = {
      transcript: transcript.toLowerCase().trim(),
      confidence: confidence,
      length: transcript.length,
      words: transcript.split(' ').length,
      timestamp: Date.now()
    };
    
    return JSON.stringify(template);
  }

  /**
   * Encrypt sensitive biometric data
   */
  private encryptData(data: string): string {
    return CryptoJS.AES.encrypt(data, this.ENCRYPTION_KEY).toString();
  }

  /**
   * Decrypt sensitive biometric data
   */
  private decryptData(encryptedData: string): string {
    const bytes = CryptoJS.AES.decrypt(encryptedData, this.ENCRYPTION_KEY);
    return bytes.toString(CryptoJS.enc.Utf8);
  }

  /**
   * Store biometric registration in database
   */
  private async storeBiometricRegistration(registration: BiometricRegistration): Promise<void> {
    const { error } = await supabase
      .from('biometric_registrations')
      .upsert({
        user_id: registration.user_id,
        type: registration.type,
        template_hash: registration.template_hash,
        confidence_threshold: registration.confidence_threshold,
        device_info: registration.device_info,
        created_at: registration.created_at,
        is_active: true
      });

    if (error) {
      throw new Error(`Failed to store biometric registration: ${error.message}`);
    }
  }

  /**
   * Get biometric registration from database
   */
  private async getBiometricRegistration(
    userId: string, 
    type: BiometricType
  ): Promise<BiometricRegistration | null> {
    const { data, error } = await supabase
      .from('biometric_registrations')
      .select('*')
      .eq('user_id', userId)
      .eq('type', type)
      .eq('is_active', true)
      .single();

    if (error || !data) {
      return null;
    }

    return data as BiometricRegistration;
  }

  /**
   * Delete biometric registration
   */
  async deleteBiometricRegistration(userId: string, type: BiometricType): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('biometric_registrations')
        .update({ is_active: false })
        .eq('user_id', userId)
        .eq('type', type);

      return !error;
    } catch (error) {
      console.error('Error deleting biometric registration:', error);
      return false;
    }
  }

  /**
   * Get all biometric registrations for a user
   */
  async getUserBiometrics(userId: string): Promise<BiometricRegistration[]> {
    try {
      const { data, error } = await supabase
        .from('biometric_registrations')
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true);

      if (error) {
        throw error;
      }

      return data as BiometricRegistration[];
    } catch (error) {
      console.error('Error fetching user biometrics:', error);
      return [];
    }
  }
}

// Global biometric service instance
export const biometricService = new BiometricService();

// Type declarations for browser APIs
declare global {
  interface Window {
    SpeechRecognition: any;
    webkitSpeechRecognition: any;
  }
}
