{"name": "attendance-tracking-system", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^3.3.4", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@sendgrid/mail": "^8.1.5", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "^5.17.19", "@types/file-saver": "^2.0.7", "@types/jspdf": "^1.3.3", "@types/ua-parser-js": "^0.7.39", "@zxing/library": "^0.21.3", "base64url": "^3.0.1", "caniuse-lite": "^1.0.30001583", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "cmdk": "^0.2.1", "crypto-js": "^4.2.0", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.0.0", "file-saver": "^2.0.5", "framer-motion": "^11.0.5", "html5-qrcode": "^2.3.8", "i18next": "^25.1.2", "i18next-browser-languagedetector": "^8.1.0", "input-otp": "^1.1.0", "jspdf": "^2.5.1", "jspdf-autotable": "^3.8.2", "lucide-react": "^0.330.0", "next-themes": "^0.2.1", "qr-scanner": "^1.4.2", "qrcode": "^1.5.4", "qrcode.react": "^3.1.0", "react": "^18.2.0", "react-day-picker": "^8.10.0", "react-dom": "^18.2.0", "react-hook-form": "^7.50.1", "react-i18next": "^15.5.1", "react-phone-number-input": "^3.4.12", "react-qr-code": "^2.0.15", "react-resizable-panels": "^1.0.9", "react-router-dom": "^6.22.0", "recharts": "^2.12.0", "sonner": "^1.4.0", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "ua-parser-js": "^2.0.0", "vaul": "^0.8.0", "zod": "^3.22.4"}, "devDependencies": {"@eslint/js": "^8.56.0", "@tailwindcss/typography": "^0.5.10", "@types/node": "^20.11.17", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.17", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "globals": "^13.24.0", "postcss": "^8.4.35", "supabase": "^2.22.6", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "typescript": "^5.8.3", "typescript-eslint": "^7.0.1", "vite": "^5.1.1", "vite-plugin-mkcert": "^1.17.1"}}