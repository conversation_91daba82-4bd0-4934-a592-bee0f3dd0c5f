import { useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import Navbar from "@/components/shared/Navbar";
import Footer from "@/components/shared/Footer";
import {
  <PERSON><PERSON>print,
  ShieldCheck,
  Users,
  BarChart4,
  CheckCircle,
  Clock,
} from "lucide-react";

export default function Index() {
  const navigate = useNavigate();

  useEffect(() => {
    // Check if user is already logged in and redirect to their dashboard
    const storedUser = localStorage.getItem("user");
    if (storedUser) {
      const user = JSON.parse(storedUser);
      navigate(`/${user.role}`);
    }
  }, [navigate]);

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <main className="flex-1">
        {/* Hero Section */}
        <section className="bg-primary text-white py-16 md:py-24 dark:bg-[#F39228]">
          <div className="container mx-auto px-4 text-center">
            <h1 className="text-3xl md:text-5xl font-bold mb-4">
              Attendance Tracking System
            </h1>
            <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
              The most secure and efficient attendance tracking system for
              educational institutions
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Button
                size="lg"
                className="bg-white text-primary hover:bg-gray-100"
                asChild
              >
                <Link to="/login">Get Started</Link>
              </Button>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">
              Key Features
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="bg-white p-6 rounded-lg shadow-md">
                <div className="flex items-center justify-center h-12 w-12 bg-primary/10 text-primary rounded-full mb-4 mx-auto">
                  <Fingerprint size={24} />
                </div>
                <h3 className="text-xl font-bold text-center mb-2">
                  Biometric Verification
                </h3>
                <p className="text-center text-gray-600">
                  Secure attendance tracking with biometric authentication or
                  PIN verification for foolproof identity confirmation.
                </p>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-md">
                <div className="flex items-center justify-center h-12 w-12 bg-primary/10 text-primary rounded-full mb-4 mx-auto">
                  <ShieldCheck size={24} />
                </div>
                <h3 className="text-xl font-bold text-center mb-2">
                  Anti-Fraud Measures
                </h3>
                <p className="text-center text-gray-600">
                  Advanced fraud prevention with location validation, device
                  fingerprinting, and behavioral pattern analysis.
                </p>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-md">
                <div className="flex items-center justify-center h-12 w-12 bg-primary/10 text-primary rounded-full mb-4 mx-auto">
                  <BarChart4 size={24} />
                </div>
                <h3 className="text-xl font-bold text-center mb-2">
                  Real-time Analytics
                </h3>
                <p className="text-center text-gray-600">
                  Comprehensive dashboards for teachers and administrators with
                  real-time attendance tracking and reporting.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* How it Works */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">
              How It Works
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="flex flex-col items-center">
                <div className="flex items-center justify-center h-12 w-12 bg-secondary/10 text-secondary rounded-full mb-4">
                  <span className="font-bold">1</span>
                </div>
                <h3 className="text-xl font-bold text-center mb-2">
                  Scan QR Code
                </h3>
                <p className="text-center text-gray-600">
                  Students scan the dynamically generated QR code displayed in
                  their classroom.
                </p>
              </div>

              <div className="flex flex-col items-center">
                <div className="flex items-center justify-center h-12 w-12 bg-secondary/10 text-secondary rounded-full mb-4">
                  <span className="font-bold">2</span>
                </div>
                <h3 className="text-xl font-bold text-center mb-2">
                  Verify Identity
                </h3>
                <p className="text-center text-gray-600">
                  Confirm identity using biometric authentication or a secure
                  6-digit PIN.
                </p>
              </div>

              <div className="flex flex-col items-center">
                <div className="flex items-center justify-center h-12 w-12 bg-secondary/10 text-secondary rounded-full mb-4">
                  <span className="font-bold">3</span>
                </div>
                <h3 className="text-xl font-bold text-center mb-2">
                  Attendance Recorded
                </h3>
                <p className="text-center text-gray-600">
                  Attendance is securely recorded and immediately visible to
                  instructors.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* User Types */}
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">
              For Every User
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="bg-white p-6 rounded-lg shadow-md">
                <div className="flex items-center justify-center h-12 w-12 bg-blue-100 text-blue-600 rounded-full mb-4 mx-auto">
                  <Users size={24} />
                </div>
                <h3 className="text-xl font-bold text-center mb-2">Students</h3>
                <ul className="space-y-2">
                  <li className="flex items-center">
                    <CheckCircle size={16} className="text-green-600 mr-2" />
                    <span>Quick and easy QR scanning</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle size={16} className="text-green-600 mr-2" />
                    <span>View personal attendance history</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle size={16} className="text-green-600 mr-2" />
                    <span>Secure biometric verification</span>
                  </li>
                </ul>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-md">
                <div className="flex items-center justify-center h-12 w-12 bg-purple-100 text-purple-600 rounded-full mb-4 mx-auto">
                  <BarChart4 size={24} />
                </div>
                <h3 className="text-xl font-bold text-center mb-2">Teachers</h3>
                <ul className="space-y-2">
                  <li className="flex items-center">
                    <CheckCircle size={16} className="text-green-600 mr-2" />
                    <span>Real-time attendance dashboard</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle size={16} className="text-green-600 mr-2" />
                    <span>Absence alerts and notifications</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle size={16} className="text-green-600 mr-2" />
                    <span>Track patterns and statistics</span>
                  </li>
                </ul>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-md">
                <div className="flex items-center justify-center h-12 w-12 bg-teal-100 text-teal-600 rounded-full mb-4 mx-auto">
                  <ShieldCheck size={24} />
                </div>
                <h3 className="text-xl font-bold text-center mb-2">
                  Administrators
                </h3>
                <ul className="space-y-2">
                  <li className="flex items-center">
                    <CheckCircle size={16} className="text-green-600 mr-2" />
                    <span>Generate and manage QR codes</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle size={16} className="text-green-600 mr-2" />
                    <span>Monitor fraud detection alerts</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle size={16} className="text-green-600 mr-2" />
                    <span>System-wide reporting and analytics</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-primary text-white">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl font-bold mb-4">
              Ready to Modernize Attendance Tracking?
            </h2>
            <p className="text-xl mb-8 max-w-2xl mx-auto">
              Join schools and universities that have eliminated attendance
              fraud and simplified their tracking process.
            </p>
            <Button
              size="lg"
              className="bg-white text-primary hover:bg-gray-100"
              asChild
            >
              <Link to="/login">Get Started</Link>
            </Button>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}
