import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useSupabaseClient } from "@supabase/auth-helpers-react";
import { toast } from "sonner";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import {
  Check,
  CheckCircle,
  Clock,
  AlertCircle,
  Calendar,
  Search,
  User,
  UserCircle,
  X,
} from "lucide-react";
import { useDebounce } from "@/hooks/useDebounce";
import { LoadingSkeleton } from "@/components/shared/LoadingSkeleton";
import { useProfile } from "@/hooks/useProfile";

// Types
interface Student {
  id: string;
  name: string;
  email: string;
  studentId?: string;
  course?: string;
  blockId?: string;
  roomId?: string;
  blocks?: {
    id: string;
    name: string;
  };
  rooms?: {
    id: string;
    name: string;
  };
  biometricRegistered?: boolean;
  photoUrl?: string;
  status?: "present" | "absent" | "late" | "excused";
}

interface Block {
  id: string;
  name: string;
}

interface Room {
  id: string;
  name: string;
  blockId: string;
}

interface AttendanceRecord {
  studentId: string;
  status: "present" | "absent" | "late" | "excused";
  timestamp: string;
  verificationMethod: string;
  deviceInfo: string;
}

export default function AdminStudentDirectory() {
  const { t } = useTranslation();
  const supabase = useSupabaseClient();
  const { profile } = useProfile();

  // State
  const [students, setStudents] = useState<Student[]>([]);
  const [blocks, setBlocks] = useState<Block[]>([]);
  const [rooms, setRooms] = useState<Room[]>([]);
  const [selectedBlock, setSelectedBlock] = useState<string>("all");
  const [selectedRoom, setSelectedRoom] = useState<string>("none");
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(true);
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
  const [attendanceRecords, setAttendanceRecords] = useState<
    Record<string, AttendanceRecord>
  >({});

  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  // Fetch students, blocks, and rooms
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch blocks
        const { data: blocksData, error: blocksError } = await supabase
          .from("blocks")
          .select("*")
          .order("name");

        if (blocksError) throw blocksError;
        setBlocks(blocksData || []);

        // Fetch rooms
        const { data: roomsData, error: roomsError } = await supabase
          .from("rooms")
          .select("*")
          .order("name");

        if (roomsError) throw roomsError;
        setRooms(roomsData || []);

        // Fetch students with their block and room info
        const { data: studentsData, error: studentsError } = await supabase
          .from("profiles")
          .select(
            "id, name, email, student_id, course, block_id, room_id, biometric_registered, photo_url, blocks(id, name), rooms(id, name)"
          )
          .eq("role", "student");

        if (studentsError) throw studentsError;

        // Transform data to match our Student interface
        const transformedStudents: Student[] = (studentsData || []).map(
          (student: any) => ({
            id: student.id,
            name: student.name || "Unknown",
            email: student.email,
            studentId: student.student_id,
            course: student.course,
            blockId: student.block_id,
            roomId: student.room_id,
            blocks: student.blocks,
            rooms: student.rooms,
            biometricRegistered: student.biometric_registered,
            photoUrl: student.photo_url,
          })
        );

        setStudents(transformedStudents);

        // Fetch today's attendance records
        const today = new Date().toISOString().split("T")[0];
        const { data: attendanceData, error: attendanceError } = await supabase
          .from("attendance_records")
          .select("*")
          .gte("created_at", `${today}T00:00:00`)
          .lte("created_at", `${today}T23:59:59`);

        if (attendanceError) throw attendanceError;

        // Transform attendance data into a record for easy lookup
        const attendanceMap: Record<string, AttendanceRecord> = {};
        (attendanceData || []).forEach((record: any) => {
          attendanceMap[record.student_id] = {
            studentId: record.student_id,
            status: record.status || "absent",
            timestamp: record.created_at,
            verificationMethod: record.verification_method || "unknown",
            deviceInfo: record.device_info || "unknown",
          };
        });

        setAttendanceRecords(attendanceMap);
      } catch (error) {
        console.error("Error fetching data:", error);
        toast.error(t("students.directory.fetchBlocksRoomsError"));
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [supabase, t]);

  // Filter students based on selected block, room, and search query
  const filteredStudents = students.filter((student) => {
    // Filter by block
    if (selectedBlock !== "all" && student.blockId !== selectedBlock) {
      return false;
    }

    // Filter by room
    if (
      selectedRoom !== "none" &&
      selectedRoom !== "all" &&
      student.roomId !== selectedRoom
    ) {
      return false;
    }

    // Filter by search query
    if (debouncedSearchQuery) {
      const query = debouncedSearchQuery.toLowerCase();
      return (
        student.name?.toLowerCase().includes(query) ||
        student.email?.toLowerCase().includes(query) ||
        student.studentId?.toLowerCase().includes(query)
      );
    }

    return true;
  });

  // Get filtered rooms based on selected block
  const filteredRooms = rooms.filter(
    (room) => selectedBlock === "all" || room.blockId === selectedBlock
  );

  // Toggle student attendance status
  const toggleAttendanceStatus = async (student: Student) => {
    try {
      const now = new Date();
      const currentStatus = getAttendanceStatus(student.id);

      // Determine the new status based on current status
      const newStatus = currentStatus === "present" ? "absent" : "present";

      // Update local state immediately for better UX
      setAttendanceRecords((prev) => ({
        ...prev,
        [student.id]: {
          ...(prev[student.id] || {}),
          studentId: student.id,
          status: newStatus,
          timestamp: now.toISOString(),
          verificationMethod: "manual",
          deviceInfo: "Manual update by admin",
        },
      }));

      // Show success message immediately
      toast({
        title: "Status Updated",
        description: `${student.name} has been marked ${newStatus}`,
      });

      // Update the database
      const { error } = await supabase.from("attendance_records").upsert({
        student_id: student.id,
        status: newStatus,
        verification_method: "manual",
        device_info: "Manual update by admin",
        created_at: now.toISOString(),
        updated_at: now.toISOString(),
      });

      if (error) throw error;
    } catch (error) {
      console.error("Error updating attendance status:", error);
      toast.error("Failed to update attendance status");
    }
  };

  // Helper function to get attendance status for a student
  const getAttendanceStatus = (studentId: string): "present" | "absent" => {
    return attendanceRecords[studentId]?.status === "present"
      ? "present"
      : "absent";
  };

  // Helper function to get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "present":
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case "late":
        return <Clock className="w-4 h-4 text-amber-500" />;
      case "absent":
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      case "excused":
        return <Calendar className="w-4 h-4 text-blue-500" />;
      default:
        return null;
    }
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>{t("admin.studentDirectory.title")}</CardTitle>
          <CardDescription>
            {t("admin.studentDirectory.description")}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col space-y-4">
            <div className="flex flex-col md:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <Input
                  placeholder={t("students.directory.searchPlaceholder")}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full"
                />
              </div>

              {/* Block Filter */}
              <div className="w-full md:w-48">
                <Select
                  value={selectedBlock}
                  onValueChange={(value) => {
                    setSelectedBlock(value);
                    setSelectedRoom("none");
                  }}
                >
                  <SelectTrigger>
                    <SelectValue
                      placeholder={t("students.directory.selectBlock")}
                    />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">
                      {t("students.directory.allBlocks")}
                    </SelectItem>
                    {blocks.map((block) => (
                      <SelectItem key={block.id} value={block.id}>
                        {block.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Room Filter */}
              <div className="w-full md:w-48">
                <Select
                  value={selectedRoom}
                  onValueChange={setSelectedRoom}
                  disabled={selectedBlock === "all"}
                >
                  <SelectTrigger>
                    <SelectValue
                      placeholder={t("students.directory.selectRoom")}
                    />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">
                      {t("students.directory.selectRoom")}
                    </SelectItem>
                    <SelectItem value="all">
                      {t("students.directory.allRooms")}
                    </SelectItem>
                    {filteredRooms.map((room) => (
                      <SelectItem key={room.id} value={room.id}>
                        {room.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {loading ? (
            <LoadingSkeleton />
          ) : (
            <div className="rounded-md border mt-4">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t("students.directory.name")}</TableHead>
                    <TableHead>{t("students.directory.studentId")}</TableHead>
                    <TableHead>{t("students.directory.course")}</TableHead>
                    <TableHead>{t("students.directory.block")}</TableHead>
                    <TableHead>{t("students.directory.room")}</TableHead>
                    <TableHead>{t("students.directory.status")}</TableHead>
                    <TableHead className="text-right">
                      {t("students.directory.actions")}
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredStudents.length > 0 ? (
                    filteredStudents.map((student) => {
                      const status = getAttendanceStatus(student.id);
                      return (
                        <TableRow key={student.id}>
                          <TableCell className="font-medium">
                            {student.name}
                          </TableCell>
                          <TableCell>
                            {student.studentId ||
                              t("admin.studentDirectory.notSet")}
                          </TableCell>
                          <TableCell>
                            {student.course ||
                              t("admin.studentDirectory.notSet")}
                          </TableCell>
                          <TableCell>
                            {student.blocks
                              ? student.blocks.name
                              : t("admin.studentDirectory.notAssigned")}
                          </TableCell>
                          <TableCell>
                            {student.rooms
                              ? student.rooms.name
                              : t("admin.studentDirectory.notAssigned")}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              {getStatusIcon(status)}
                              <span className="capitalize">
                                {t(`attendance.${status}`)}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end space-x-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setSelectedStudent(student)}
                              >
                                {t("common.view")}
                              </Button>
                              <Button
                                variant={
                                  status === "present"
                                    ? "destructive"
                                    : "default"
                                }
                                size="sm"
                                onClick={() => toggleAttendanceStatus(student)}
                              >
                                {status === "present" ? (
                                  <>
                                    <X className="w-4 h-4 mr-1" />
                                    {t("teacher.dashboard.absent")}
                                  </>
                                ) : (
                                  <>
                                    <Check className="w-4 h-4 mr-1" />
                                    {t("teacher.dashboard.present")}
                                  </>
                                )}
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })
                  ) : (
                    <TableRow>
                      <TableCell colSpan={8} className="h-24 text-center">
                        <div className="flex flex-col items-center justify-center text-muted-foreground">
                          {searchQuery ? (
                            <>
                              <Search className="w-10 h-10 opacity-20 mb-2" />
                              <p>
                                {t("admin.studentDirectory.noStudentsFound")}
                              </p>
                              <p className="text-sm mt-1">
                                {t("students.directory.trySearching")}
                              </p>
                            </>
                          ) : selectedBlock !== "all" ||
                            selectedRoom !== "none" ? (
                            <>
                              <UserCircle className="w-10 h-10 opacity-20 mb-2" />
                              <p>{t("teacher.dashboard.noStudentsInRoom")}</p>
                            </>
                          ) : (
                            <>
                              <UserCircle className="w-10 h-10 opacity-20 mb-2" />
                              <p>
                                {t("admin.studentDirectory.noStudentsFound")}
                              </p>
                            </>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Student Profile Dialog */}
      {selectedStudent && (
        <Dialog
          open={!!selectedStudent}
          onOpenChange={(open) => !open && setSelectedStudent(null)}
        >
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>{t("students.profile.title")}</DialogTitle>
              <DialogDescription>
                {t("students.profile.detailedInfo", {
                  name: selectedStudent.name,
                })}
              </DialogDescription>
            </DialogHeader>

            <div className="flex flex-col items-center space-y-4 pt-4">
              {/* Student Photo */}
              <div className="relative">
                {selectedStudent.photoUrl ? (
                  <img
                    src={selectedStudent.photoUrl}
                    alt={selectedStudent.name}
                    className="w-24 h-24 rounded-full object-cover border-2 border-primary"
                  />
                ) : (
                  <div className="w-24 h-24 rounded-full bg-muted flex items-center justify-center border-2 border-primary">
                    <User size={32} />
                  </div>
                )}
              </div>

              {/* Student Name */}
              <h2 className="text-xl font-bold">{selectedStudent.name}</h2>

              {/* Student Details */}
              <div className="w-full space-y-3">
                <div className="grid grid-cols-2 gap-2">
                  <div className="font-medium">
                    {t("students.profile.studentId")}:
                  </div>
                  <div>
                    {selectedStudent.studentId ||
                      t("admin.studentDirectory.notSet")}
                  </div>

                  <div className="font-medium">
                    {t("students.profile.email")}:
                  </div>
                  <div className="break-all">{selectedStudent.email}</div>

                  <div className="font-medium">
                    {t("students.profile.course")}:
                  </div>
                  <div>
                    {selectedStudent.course ||
                      t("admin.studentDirectory.notSet")}
                  </div>

                  <div className="font-medium">
                    {t("students.profile.block")}:
                  </div>
                  <div>
                    {selectedStudent.blocks
                      ? `${t("common.block")} ${selectedStudent.blocks.name}`
                      : t("admin.studentDirectory.notAssigned")}
                  </div>

                  <div className="font-medium">
                    {t("students.profile.room")}:
                  </div>
                  <div>
                    {selectedStudent.rooms
                      ? `${t("common.room")} ${selectedStudent.rooms.name}`
                      : t("admin.studentDirectory.notAssigned")}
                  </div>

                  <div className="font-medium">
                    {t("students.profile.biometric")}:
                  </div>
                  <div>
                    {selectedStudent.biometricRegistered ? (
                      <Badge className="bg-green-100 text-green-800">
                        {t("students.profile.registered")}
                      </Badge>
                    ) : (
                      <Badge
                        variant="outline"
                        className="text-muted-foreground"
                      >
                        {t("students.profile.notRegistered")}
                      </Badge>
                    )}
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex w-full space-x-2 pt-4">
                <Button
                  onClick={() => toggleAttendanceStatus(selectedStudent)}
                  className="flex-1"
                  variant={
                    getAttendanceStatus(selectedStudent.id) === "present"
                      ? "destructive"
                      : "default"
                  }
                >
                  {getAttendanceStatus(selectedStudent.id) === "present" ? (
                    <>
                      <X className="mr-2 h-4 w-4" />
                      {t("teacher.attendance.markAbsent")}
                    </>
                  ) : (
                    <>
                      <Check className="mr-2 h-4 w-4" />
                      {t("teacher.attendance.markPresent")}
                    </>
                  )}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
