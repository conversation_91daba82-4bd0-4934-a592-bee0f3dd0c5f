import { useState, useEffect, useMemo } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell,
} from "@/components/ui/table";
import {
  User,
  Check,
  X,
  Search,
  Calendar,
  Building2,
  DoorOpen,
  UserCircle,
  MoreVertical,
  Edit,
  Trash2,
  LayoutGrid,
  LayoutList,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { toast as sonnerToast } from "sonner";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Student, AttendanceRecord, Block, Room } from "@/lib/types";
import { supabase } from "@/lib/supabase";
import { format } from "date-fns";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { getStatusIcon } from "./dashboard/utils/statusIcons";
import { useAuth } from "@/context/AuthContext";
import { useTranslation } from "react-i18next";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { Label } from "@/components/ui/label";
import { LoadingSpinner } from "@/components/ui/loading-spinner";

export default function StudentDirectory() {
  const { t } = useTranslation();
  const [students, setStudents] = useState<Student[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [attendanceRecords, setAttendanceRecords] = useState<
    Record<string, AttendanceRecord>
  >({});
  const [selectedRoom, setSelectedRoom] = useState<string>(() => {
    const savedRoom = localStorage.getItem("selectedRoom");
    return savedRoom || "none";
  });
  const [blocks, setBlocks] = useState<Block[]>([]);
  const [rooms, setRooms] = useState<Room[]>([]);
  const [selectedBlock, setSelectedBlock] = useState<string>(() => {
    const savedBlock = localStorage.getItem("selectedBlock");
    return savedBlock || "all";
  });
  const [viewMode, setViewMode] = useState<"list" | "grid">(() => {
    const savedViewMode = localStorage.getItem("studentDirectoryViewMode");
    return (savedViewMode as "list" | "grid") || "list";
  });
  const [editingBlock, setEditingBlock] = useState<Block | null>(null);
  const [editingRoom, setEditingRoom] = useState<Room | null>(null);
  const [isAddingBlock, setIsAddingBlock] = useState(false);
  const [isAddingRoom, setIsAddingRoom] = useState(false);
  const [newBlockName, setNewBlockName] = useState("");
  const [newRoomName, setNewRoomName] = useState("");
  const { toast } = useToast();
  const { profile } = useAuth();

  // Fetch blocks and rooms
  useEffect(() => {
    const fetchBlocksAndRooms = async () => {
      try {
        // Fetch blocks for the current school
        let blocksQuery = supabase.from("blocks").select("*").order("name");

        // Filter by school_id if available
        if (profile?.school_id) {
          blocksQuery = blocksQuery.eq("school_id", profile.school_id);
        }

        const { data: blocksData, error: blocksError } = await blocksQuery;

        if (blocksError) throw blocksError;
        console.log("Fetched blocks:", blocksData);
        setBlocks(blocksData || []);

        // After fetching blocks, check localStorage and set selected block
        const savedBlock = localStorage.getItem("selectedBlock");
        console.log("Saved block:", savedBlock);

        if (savedBlock) {
          // Check if the saved block exists in the fetched blocks
          const blockExists = blocksData?.some(
            (block) => block.id === savedBlock
          );
          console.log("Block exists:", blockExists);

          if (blockExists) {
            setSelectedBlock(savedBlock);
          } else {
            // If saved block doesn't exist anymore, reset to "all"
            localStorage.setItem("selectedBlock", "all");
            setSelectedBlock("all");
          }
        }

        // Fetch rooms for the current school
        let roomsQuery = supabase.from("rooms").select("*").order("name");

        // Filter by school_id if available
        if (profile?.school_id) {
          roomsQuery = roomsQuery.eq("school_id", profile.school_id);
        }

        const { data: roomsData, error: roomsError } = await roomsQuery;

        if (roomsError) throw roomsError;
        console.log("Fetched rooms:", roomsData);
        setRooms(roomsData || []);

        // Check saved room
        const savedRoom = localStorage.getItem("selectedRoom");
        console.log("Saved room:", savedRoom);

        if (savedRoom === "none") {
          // If explicitly saved as "none", keep it as "none"
          setSelectedRoom("none");
        } else if (savedRoom) {
          const roomExists = roomsData?.some((room) => room.id === savedRoom);
          if (roomExists) {
            setSelectedRoom(savedRoom);
          } else {
            // If saved room doesn't exist anymore, reset to "none"
            localStorage.setItem("selectedRoom", "none");
            setSelectedRoom("none");
          }
        } else {
          // If no saved room, default to "none"
          localStorage.setItem("selectedRoom", "none");
          setSelectedRoom("none");
        }
      } catch (error) {
        console.error("Error fetching blocks and rooms:", error);
        toast({
          title: t("common.error"),
          description: t("students.directory.fetchBlocksRoomsError"),
          variant: "destructive",
        });
      }
    };

    fetchBlocksAndRooms();
  }, []);

  // Fetch teacher's room
  useEffect(() => {
    const fetchRoom = async () => {
      if (!profile?.id) return;

      try {
        // Query for rooms associated with this teacher
        let query = supabase
          .from("rooms")
          .select("*")
          .eq("teacher_id", profile.id)
          .limit(1);

        // Filter by school_id if available
        if (profile?.school_id) {
          query = query.eq("school_id", profile.school_id);
        }

        const { data: rooms, error } = await query;

        if (error) throw error;

        if (rooms && rooms.length > 0) {
          setSelectedRoom(rooms[0].id);
        } else {
          // First, get or create a default block
          let blockId = null;

          // Try to find an existing block
          let blocksQuery = supabase
            .from("blocks")
            .select("id")
            .order("name")
            .limit(1);

          // Filter by school_id if available
          if (profile?.school_id) {
            blocksQuery = blocksQuery.eq("school_id", profile.school_id);
          }

          const { data: existingBlocks, error: blockError } = await blocksQuery;

          if (blockError) {
            console.error("Error fetching blocks:", blockError);
            throw blockError;
          }

          if (existingBlocks && existingBlocks.length > 0) {
            // Use the first available block
            blockId = existingBlocks[0].id;
          } else {
            // Create a new default block
            const blockData: any = {
              name: "Default Block",
            };

            // Add school_id if available
            if (profile?.school_id) {
              blockData.school_id = profile.school_id;
            }

            const { data: newBlock, error: createBlockError } = await supabase
              .from("blocks")
              .insert([blockData])
              .select()
              .single();

            if (createBlockError) {
              console.error("Error creating default block:", createBlockError);
              throw createBlockError;
            }

            blockId = newBlock.id;
          }

          // Now create a default room with the block_id
          const roomData: any = {
            name: "Default Room",
            building: "Main Building",
            floor: 1,
            capacity: 30,
            teacher_id: profile.id,
            block_id: blockId, // Add the block_id
          };

          // Add school_id if available
          if (profile?.school_id) {
            roomData.school_id = profile.school_id;
          }

          const { data: newRoom, error: createError } = await supabase
            .from("rooms")
            .insert([roomData])
            .select()
            .single();

          if (createError) throw createError;
          if (newRoom) {
            setSelectedRoom(newRoom.id);
          }
        }
      } catch (error) {
        console.error("Error fetching room:", error);
        toast({
          title: t("common.error"),
          description: t("students.directory.fetchRoomError"),
          variant: "destructive",
        });
      }
    };

    fetchRoom();
  }, [profile?.id]);

  // Fetch students data
  const fetchStudents = async () => {
    setLoading(true);
    try {
      console.log("Fetching students...");

      // Get student profiles with their assigned block and room information
      let query = supabase.from("profiles").select("*").eq("role", "student");

      // Filter by school_id if available
      if (profile?.school_id) {
        query = query.eq("school_id", profile.school_id);
      }

      const { data: profileData, error: profileError } = await query;

      if (profileError) {
        console.error("Error fetching students:", profileError.message);
        toast({
          title: t("common.error"),
          description:
            profileError.message || t("students.directory.loadError"),
          variant: "destructive",
        });
        return;
      }

      console.log("Raw student profiles:", profileData);

      if (profileData) {
        const studentProfiles: Student[] = profileData.map((profile) => {
          // Create the student object
          const student = {
            id: profile.id,
            user_id: profile.user_id || "",
            name: profile.name || "Unknown",
            email: profile.email || "",
            role: "student",
            studentId: profile.student_id || "",
            course: profile.course || "",
            biometricRegistered: profile.biometric_registered || false,
            block_id: profile.block_id || "",
            room_id: profile.room_id || "",
            blockName: profile.block_name || "Not Assigned",
            roomNumber: profile.room_number || "Not Assigned",
            pin: profile.pin || "",
            photoUrl: profile.photo_url || "",
            blocks: profile.block_name
              ? {
                  id: profile.block_id || "",
                  name: profile.block_name,
                }
              : undefined,
            rooms: profile.room_number
              ? {
                  id: profile.room_id || "",
                  name: profile.room_number,
                  block_id: profile.block_id || "",
                }
              : undefined,
          };

          console.log("Processing student:", {
            name: student.name,
            block_name: profile.block_name,
            room_number: profile.room_number,
            block_id: profile.block_id,
            room_id: profile.room_id,
          });

          return student;
        });

        console.log("Processed student profiles:", studentProfiles);
        setStudents(studentProfiles);
      }
    } catch (error) {
      console.error("Error in fetchStudents:", error);
      toast({
        title: t("common.error"),
        description: t("students.directory.loadErrorTryAgain"),
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch attendance records
  const fetchAttendanceRecords = async () => {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const todayStr = today.toISOString();
      const tomorrowStr = new Date(
        today.getTime() + 24 * 60 * 60 * 1000
      ).toISOString();

      console.log(
        "StudentDirectory: Fetching attendance records for date range:",
        {
          from: todayStr,
          to: tomorrowStr,
        }
      );

      // Get all student IDs for the query
      let studentIds = students.map((student) => student.id);

      // If we don't have any students yet, fetch all student IDs from the database
      if (studentIds.length === 0) {
        console.log(
          "No students in state, fetching all student IDs from database"
        );
        const { data: profileData, error: profileError } = await supabase
          .from("profiles")
          .select("id")
          .eq("role", "student");

        if (profileError) {
          console.error("Error fetching student IDs:", profileError);
          return;
        }

        if (profileData && profileData.length > 0) {
          studentIds = profileData.map((profile) => profile.id);
        } else {
          console.log("No students found in database");
          return;
        }
      }

      // Fetch attendance records for today for students in the teacher's school
      let recordsQuery = supabase
        .from("attendance_records")
        .select("*")
        .in("student_id", studentIds)
        .gte("timestamp", todayStr)
        .lt("timestamp", tomorrowStr);

      // Filter by school_id if available
      if (profile?.school_id) {
        recordsQuery = recordsQuery.eq("school_id", profile.school_id);
      }

      const { data: recordsData, error: recordsError } = await recordsQuery;

      if (recordsError) {
        console.error("Error fetching attendance records:", recordsError);
        throw recordsError;
      }

      console.log(
        `StudentDirectory: Fetched ${
          recordsData?.length || 0
        } attendance records`
      );

      if (recordsData) {
        // Group records by student_id
        const recordsByStudent: Record<string, any[]> = {};
        recordsData.forEach((record) => {
          if (!recordsByStudent[record.student_id]) {
            recordsByStudent[record.student_id] = [];
          }
          recordsByStudent[record.student_id].push(record);
        });

        // For each student, get the most recent record
        const records: Record<string, AttendanceRecord> = {};
        Object.entries(recordsByStudent).forEach(
          ([studentId, studentRecords]) => {
            // Sort records by timestamp in descending order (newest first)
            const sortedRecords = studentRecords.sort(
              (a, b) =>
                new Date(b.timestamp).getTime() -
                new Date(a.timestamp).getTime()
            );

            // Use the most recent record
            const mostRecent = sortedRecords[0];

            records[studentId] = {
              id: mostRecent.id,
              studentId: mostRecent.student_id,
              roomId: mostRecent.room_id,
              timestamp: mostRecent.timestamp,
              deviceInfo: mostRecent.device_info || "",
              location: mostRecent.location || null,
              verificationMethod: (mostRecent.verification_method ||
                "manual") as "biometric" | "pin" | "manual",
              status: (mostRecent.status || "absent") as
                | "present"
                | "absent"
                | "late"
                | "excused",
            };
          }
        );

        // Set default status for students without records
        studentIds.forEach((studentId) => {
          if (!records[studentId]) {
            records[studentId] = {
              id: "",
              studentId: studentId,
              roomId: null,
              timestamp: todayStr,
              deviceInfo: "",
              location: null,
              verificationMethod: "manual",
              status: "absent",
            };
          }
        });

        console.log("StudentDirectory: Setting attendance records:", records);
        setAttendanceRecords(records);

        // Dispatch an event to notify the Dashboard component
        try {
          const event = new CustomEvent("attendance-updated", {
            detail: { source: "StudentDirectory", records },
          });
          window.dispatchEvent(event);
          console.log("StudentDirectory: Dispatched attendance-updated event");
        } catch (eventError) {
          console.error("Error dispatching event:", eventError);
        }
      }
    } catch (error) {
      console.error("Error fetching attendance records:", error);
    }
  };

  // Filter assigned students based on selected block and room
  const assignedStudents = useMemo(() => {
    console.log("Filtering students with:", {
      selectedBlock,
      selectedRoom,
      totalStudents: students.length,
    });

    return students.filter((student) => {
      // First filter by search query if it exists
      const query = searchQuery.toLowerCase().trim();
      const matchesSearch =
        query === "" ||
        student.name.toLowerCase().includes(query) ||
        (student.studentId &&
          student.studentId.toLowerCase().includes(query)) ||
        (student.email && student.email.toLowerCase().includes(query));

      // Get the block name from the blocks list
      const selectedBlockName = blocks.find(
        (b) => b.id === selectedBlock
      )?.name;
      const selectedRoomNumber = rooms.find((r) => r.id === selectedRoom)?.name;

      // Then filter by block and room using names
      const matchesBlock =
        selectedBlock === "all" || student.blockName === selectedBlockName;
      const matchesRoom =
        selectedRoom === "none" || student.roomNumber === selectedRoomNumber;

      console.log("Filtering student:", {
        name: student.name,
        blockName: student.blockName,
        roomNumber: student.roomNumber,
        selectedBlockName,
        selectedRoomNumber,
        matchesBlock,
        matchesRoom,
      });

      return matchesSearch && matchesBlock && matchesRoom;
    });
  }, [students, selectedBlock, selectedRoom, searchQuery, blocks, rooms]);

  // Initialize data and restore selections
  useEffect(() => {
    const initializeData = async () => {
      try {
        // Set loading state
        setLoading(true);

        // Fetch blocks first
        let blocksQuery = supabase
          .from("blocks")
          .select("id, name, school_id")
          .order("name");

        // Filter by school_id if available
        if (profile?.school_id) {
          blocksQuery = blocksQuery.eq("school_id", profile.school_id);
        }

        const { data: blocksData, error: blocksError } = await blocksQuery;

        if (blocksError) {
          console.error("Error fetching blocks:", blocksError);
          throw blocksError;
        }

        console.log("Fetched blocks:", blocksData);
        setBlocks(blocksData || []);

        // Restore saved block selection
        const savedBlock = localStorage.getItem("selectedBlock");
        if (
          savedBlock &&
          blocksData?.some((block) => block.id === savedBlock)
        ) {
          setSelectedBlock(savedBlock);
        }

        // Fetch rooms
        let roomsQuery = supabase
          .from("rooms")
          .select(
            `
            id,
            name,
            building,
            floor,
            capacity,
            teacher_id,
            block_id,
            school_id,
            blocks (
              id,
              name
            )
          `
          )
          .order("name");

        // Filter by school_id if available
        if (profile?.school_id) {
          roomsQuery = roomsQuery.eq("school_id", profile.school_id);
        }

        const { data: roomsData, error: roomsError } = await roomsQuery;

        if (roomsError) {
          console.error("Error fetching rooms:", roomsError);
          throw roomsError;
        }

        console.log("Fetched rooms:", roomsData);
        setRooms(roomsData || []);

        // Restore saved room selection
        const savedRoom = localStorage.getItem("selectedRoom");
        if (savedRoom && roomsData?.some((room) => room.id === savedRoom)) {
          setSelectedRoom(savedRoom);
        }

        // First fetch attendance records to ensure we have the latest status
        await fetchAttendanceRecords();

        // Then fetch students
        await fetchStudents();

        // Set loading to false
        setLoading(false);
      } catch (error) {
        console.error("Error initializing data:", error);
        toast({
          title: "Error",
          description: "Failed to load initial data. Please refresh the page.",
          variant: "destructive",
        });
        setLoading(false);
      }
    };

    initializeData();

    // Set up a polling interval to refresh attendance data periodically (every 5 seconds)
    const pollingInterval = setInterval(() => {
      console.log("StudentDirectory: Polling for attendance updates");
      fetchAttendanceRecords();
    }, 5000);

    // Listen for custom attendance-updated events from the Teacher Dashboard
    const handleAttendanceUpdated = (event: Event) => {
      const customEvent = event as CustomEvent<{
        studentId: string;
        status: "present" | "absent" | "late" | "excused";
        source?: string;
        records?: Record<string, any>;
      }>;

      console.log(
        "StudentDirectory: Received attendance-updated event:",
        customEvent.detail
      );

      // Only refresh if the event is from the Teacher Dashboard (not from this component)
      if (
        customEvent.detail.source !== "StudentDirectory" &&
        customEvent.detail.source !== "toggleAttendanceStatus"
      ) {
        console.log(
          "StudentDirectory: Refreshing attendance data due to Teacher Dashboard event"
        );
        fetchAttendanceRecords();
      }
    };

    // Add event listener
    window.addEventListener("attendance-updated", handleAttendanceUpdated);

    // Clean up the interval and event listener when the component unmounts
    return () => {
      clearInterval(pollingInterval);
      window.removeEventListener("attendance-updated", handleAttendanceUpdated);
    };
  }, []);

  // Handle block selection
  const handleBlockChange = async (value: string) => {
    console.log("Block changed to:", value);
    localStorage.setItem("selectedBlock", value);
    setSelectedBlock(value);

    // Reset room selection when changing blocks
    if (value === "all") {
      setSelectedRoom("none");
      localStorage.removeItem("selectedRoom");
    } else {
      // If changing to a specific block, check if current room is in that block
      const currentRoom = rooms.find((r) => r.id === selectedRoom);
      if (currentRoom && currentRoom.block_id !== value) {
        setSelectedRoom("none");
        localStorage.removeItem("selectedRoom");
      }
    }
  };

  // Handle room selection
  const handleRoomChange = (value: string) => {
    console.log("Room changed to:", value);
    if (value === "none") {
      localStorage.setItem("selectedRoom", "none");
      setSelectedRoom("none");
    } else {
      localStorage.setItem("selectedRoom", value);
      setSelectedRoom(value);

      // If selecting a room, also select its block
      const selectedRoomData = rooms.find((r) => r.id === value);
      if (selectedRoomData && selectedRoomData.block_id !== selectedBlock) {
        setSelectedBlock(selectedRoomData.block_id);
        localStorage.setItem("selectedBlock", selectedRoomData.block_id);
      }
    }
  };

  // Get current attendance status for a student
  const getAttendanceStatus = (
    studentId: string
  ): "present" | "absent" | "late" | "excused" => {
    return attendanceRecords[studentId]?.status || "absent";
  };

  // Helper function to get or create a default block
  const getOrCreateDefaultBlock = async (): Promise<string | null> => {
    // First check if we already have blocks
    if (blocks.length > 0) {
      return blocks[0].id;
    }

    try {
      // Try to create a default block
      const blockData: any = {
        name: "Default Block",
        start_time: "08:00:00",
        end_time: "09:00:00",
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      // Add school_id if available
      if (profile?.school_id) {
        blockData.school_id = profile.school_id;
      }

      const { data, error } = await supabase
        .from("blocks")
        .insert(blockData)
        .select()
        .single();

      if (error) {
        console.error("Error creating default block:", error);
        return null;
      }

      // Add the new block to the blocks array
      setBlocks([...blocks, data]);
      return data.id;
    } catch (error) {
      console.error("Error creating default block:", error);
      return null;
    }
  };

  // Toggle student attendance status
  const toggleAttendanceStatus = async (student: Student) => {
    try {
      const now = new Date();
      const currentStatus = getAttendanceStatus(student.id);

      // Determine the new status based on current status
      const newStatus = currentStatus === "present" ? "absent" : "present";

      // Update local state immediately for better UX
      setAttendanceRecords((prev) => ({
        ...prev,
        [student.id]: {
          ...(prev[student.id] || {}),
          studentId: student.id,
          status: newStatus,
          timestamp: now.toISOString(),
          verificationMethod: "manual",
          deviceInfo: "Manual update by teacher",
        },
      }));

      // Show success message immediately
      toast({
        title: "Status Updated",
        description: `${student.name} has been marked ${newStatus}`,
      });

      // Show additional notification
      sonnerToast.success(`${student.name} marked ${newStatus}`, {
        description: "Attendance record updated successfully",
      });

      // Get today's date at midnight for comparison
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // Find a valid room ID to use
      let roomId = student.room_id;
      if (!roomId && selectedRoom !== "none") {
        roomId = selectedRoom;
      } else if (!roomId && rooms.length > 0) {
        roomId = rooms[0].id;
      }

      // If we still don't have a room ID, try to find a teacher's room
      if (!roomId && profile?.id) {
        try {
          let roomsQuery = supabase
            .from("rooms")
            .select("id")
            .eq("teacher_id", profile.id)
            .limit(1);

          // Filter by school_id if available
          if (profile?.school_id) {
            roomsQuery = roomsQuery.eq("school_id", profile.school_id);
          }

          const { data: teacherRooms } = await roomsQuery;

          if (teacherRooms && teacherRooms.length > 0) {
            roomId = teacherRooms[0].id;
          }
        } catch (roomError) {
          console.error("Error finding teacher's room:", roomError);
        }
      }

      // If we still don't have a room ID, we can't proceed with database update
      if (!roomId) {
        console.error("No room ID available for attendance record");
        toast({
          title: "Warning",
          description:
            "Attendance status updated in UI only. No room available for database record.",
          variant: "warning",
        });
        return;
      }

      // Check for existing record today
      const { data: existingRecords, error: fetchError } = await supabase
        .from("attendance_records")
        .select("*")
        .eq("student_id", student.id)
        .gte("timestamp", today.toISOString())
        .lt(
          "timestamp",
          new Date(today.getTime() + 24 * 60 * 60 * 1000).toISOString()
        );

      if (fetchError) {
        console.error("Error fetching existing records:", fetchError);
        throw fetchError;
      }

      // Get room information for the notification
      let roomName = "Unknown";
      let notificationRoomId = null;

      if (selectedRoom) {
        notificationRoomId = selectedRoom.id;
        roomName = selectedRoom.name;
      }

      if (existingRecords && existingRecords.length > 0) {
        // Update existing record
        const existingRecord = existingRecords[0];
        const { data, error } = await supabase
          .from("attendance_records")
          .update({
            status: newStatus,
            verification_method: "manual",
            device_info: "Manual update by teacher",
            timestamp: now.toISOString(),
          })
          .eq("id", existingRecord.id)
          .select()
          .single();

        if (error) {
          console.error("Error updating attendance record:", error);
          throw error;
        }

        console.log("Successfully updated attendance record:", data);
      } else {
        // Create new record
        const recordData: any = {
          student_id: student.id,
          room_id: roomId,
          timestamp: now.toISOString(),
          device_info: "Manual update by teacher",
          verification_method: "manual",
          status: newStatus,
          location: null,
          created_at: now.toISOString(),
        };

        // Add school_id if available
        if (profile?.school_id) {
          recordData.school_id = profile.school_id;
        }

        const { data, error } = await supabase
          .from("attendance_records")
          .insert(recordData)
          .select()
          .single();

        if (error) {
          console.error("Error creating attendance record:", error);
          throw error;
        }

        console.log("Successfully created attendance record:", data);
      }

      // Create a notification for the student
      try {
        const teacherName = profile?.name || "Teacher";

        const { data: translationData } = await supabase
          .from("profiles")
          .select("preferred_language")
          .eq("id", student.id)
          .single();

        const studentLanguage = translationData?.preferred_language || "en";

        // Determine notification title and message based on status and language
        let title, message;

        if (studentLanguage === "en") {
          if (newStatus === "present") {
            title = "✅ Marked Present";
            message = `You were marked present in Room ${roomName} by ${teacherName}`;
          } else if (newStatus === "absent") {
            title = "❌ Marked Absent";
            message = `You were marked absent in Room ${roomName} by ${teacherName}. If this is incorrect, please contact your teacher.`;
          } else if (newStatus === "late") {
            title = "⏰ Marked Late";
            message = `You were marked late for Room ${roomName} by ${teacherName}`;
          } else if (newStatus === "excused") {
            title = "📝 Marked Excused";
            message = `Your absence in Room ${roomName} has been excused by ${teacherName}`;
          }
        } else if (studentLanguage === "tr") {
          if (newStatus === "present") {
            title = "✅ Mevcut Olarak İşaretlendi";
            message = `${roomName} sınıfında ${teacherName} tarafından mevcut olarak işaretlendiniz`;
          } else if (newStatus === "absent") {
            title = "❌ Yok Olarak İşaretlendi";
            message = `${roomName} sınıfında ${teacherName} tarafından yok olarak işaretlendiniz. Bu yanlışsa, lütfen öğretmeninizle iletişime geçin.`;
          } else if (newStatus === "late") {
            title = "⏰ Geç Olarak İşaretlendi";
            message = `${roomName} sınıfına ${teacherName} tarafından geç olarak işaretlendiniz`;
          } else if (newStatus === "excused") {
            title = "📝 Mazeretli Olarak İşaretlendi";
            message = `${roomName} sınıfındaki yokluğunuz ${teacherName} tarafından mazeretli olarak işaretlendi`;
          }
        }

        await supabase.from("notifications").insert({
          student_id: student.id,
          teacher_id: profile?.id,
          title: title,
          message: message,
          type: newStatus === "present" ? "attendance" : newStatus,
          read: false,
          timestamp: now.toISOString(),
          metadata: JSON.stringify({
            status: newStatus,
            previous_status: currentStatus,
            updated_by: "teacher",
            room_id: notificationRoomId,
            room_name: roomName,
          }),
        });

        console.log("Notification created for student:", student.id);
      } catch (notificationError) {
        console.error("Error creating notification:", notificationError);
        // Continue execution even if notification creation fails
      }

      // Fetch attendance records again to ensure UI is up to date
      await fetchAttendanceRecords();

      // Add a small delay before dispatching the event to ensure data is fully updated
      setTimeout(() => {
        // Force a refresh of the dashboard statistics
        try {
          // Dispatch a custom event that the dashboard can listen for
          const event = new CustomEvent("attendance-updated", {
            detail: {
              studentId: student.id,
              status: newStatus,
              source: "toggleAttendanceStatus",
            },
          });
          window.dispatchEvent(event);
          console.log("Dispatched attendance-updated event with delay");
        } catch (eventError) {
          console.error("Error dispatching event:", eventError);
        }
      }, 500);
    } catch (error) {
      console.error("Error updating attendance:", error);
      toast({
        title: "Error",
        description: "Failed to update attendance status. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Get available rooms for selected block
  const availableRooms = useMemo(() => {
    if (selectedBlock === "all") {
      return rooms;
    }
    return rooms.filter((room) => room.block_id === selectedBlock);
  }, [selectedBlock, rooms]);

  // Log current state for debugging
  useEffect(() => {
    console.log("Current state:", {
      totalStudents: students.length,
      filteredStudents: assignedStudents.length,
      selectedBlock,
      selectedRoom,
      availableRooms: availableRooms.length,
    });
  }, [students, assignedStudents, selectedBlock, selectedRoom, availableRooms]);

  // Handle block deletion
  const handleDeleteBlock = async (blockId: string) => {
    try {
      // Check if block has assigned students
      const hasStudents = students.some(
        (student) => student.block_id === blockId
      );
      if (hasStudents) {
        toast({
          title: "Cannot Delete Block",
          description:
            "This block has students assigned to it. Please reassign them first.",
          variant: "destructive",
        });
        return;
      }

      // Check if block has rooms assigned to it
      const hasRooms = rooms.some((room) => room.block_id === blockId);
      if (hasRooms) {
        toast({
          title: "Cannot Delete Block",
          description:
            "This block has rooms assigned to it. Please delete or reassign the rooms first.",
          variant: "destructive",
        });
        return;
      }

      // Delete the block
      const { error } = await supabase
        .from("blocks")
        .delete()
        .eq("id", blockId);

      if (error) {
        console.error("Supabase error:", error);
        throw new Error("Failed to delete block due to database constraints");
      }

      // Update local state
      setBlocks(blocks.filter((b) => b.id !== blockId));
      toast({
        title: "Block Deleted",
        description: "The block has been deleted successfully.",
      });

      // Reset selections if needed
      if (selectedBlock === blockId) {
        setSelectedBlock("all");
        localStorage.setItem("selectedBlock", "all");
      }
    } catch (error) {
      console.error("Error deleting block:", error);
      toast({
        title: "Error",
        description:
          "Failed to delete block. Make sure there are no rooms or students assigned to it.",
        variant: "destructive",
      });
    }
  };

  // Handle room deletion
  const handleDeleteRoom = async (roomId: string) => {
    try {
      // Check if room has assigned students
      const hasStudents = students.some(
        (student) => student.room_id === roomId
      );
      if (hasStudents) {
        toast({
          title: "Cannot Delete Room",
          description:
            "This room has students assigned to it. Please reassign them first.",
          variant: "destructive",
        });
        return;
      }

      // Check if room has attendance records
      const { data: attendanceRecords, error: attendanceError } = await supabase
        .from("attendance_records")
        .select("id")
        .eq("room_id", roomId)
        .limit(1);

      if (attendanceError) {
        console.error("Error checking attendance records:", attendanceError);
        throw new Error("Failed to check attendance records");
      }

      if (attendanceRecords && attendanceRecords.length > 0) {
        toast({
          title: "Cannot Delete Room",
          description:
            "This room has attendance records. Please archive or reassign them first.",
          variant: "destructive",
        });
        return;
      }

      // Delete the room
      const { error } = await supabase.from("rooms").delete().eq("id", roomId);

      if (error) {
        console.error("Supabase error:", error);
        throw new Error("Failed to delete room due to database constraints");
      }

      // Update local state
      setRooms(rooms.filter((r) => r.id !== roomId));
      toast({
        title: "Room Deleted",
        description: "The room has been deleted successfully.",
      });

      // Reset selections if needed
      if (selectedRoom === roomId) {
        setSelectedRoom("none");
        localStorage.removeItem("selectedRoom");
      }
    } catch (error) {
      console.error("Error deleting room:", error);
      toast({
        title: "Error",
        description:
          "Failed to delete room. Make sure there are no attendance records or students assigned to it.",
        variant: "destructive",
      });
    }
  };

  // Handle block edit
  const handleEditBlock = async (blockId: string, newName: string) => {
    try {
      const { error } = await supabase
        .from("blocks")
        .update({ name: newName })
        .eq("id", blockId);

      if (error) throw error;

      // Update local state
      setBlocks(
        blocks.map((b) => (b.id === blockId ? { ...b, name: newName } : b))
      );

      toast({
        title: "Block Updated",
        description: "The block has been updated successfully.",
      });

      setEditingBlock(null);
    } catch (error) {
      console.error("Error updating block:", error);
      toast({
        title: "Error",
        description: "Failed to update block. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Handle room edit
  const handleEditRoom = async (roomId: string, newName: string) => {
    try {
      const { error } = await supabase
        .from("rooms")
        .update({ name: newName })
        .eq("id", roomId);

      if (error) throw error;

      // Update local state
      setRooms(
        rooms.map((r) => (r.id === roomId ? { ...r, name: newName } : r))
      );

      toast({
        title: "Room Updated",
        description: "The room has been updated successfully.",
      });

      setEditingRoom(null);
    } catch (error) {
      console.error("Error updating room:", error);
      toast({
        title: "Error",
        description: "Failed to update room. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Toggle view mode between list and grid
  const toggleViewMode = () => {
    const newMode = viewMode === "list" ? "grid" : "list";
    setViewMode(newMode);
    localStorage.setItem("studentDirectoryViewMode", newMode);
  };

  // Add new handlers for creating blocks and rooms
  const handleAddBlock = async () => {
    try {
      if (!newBlockName.trim()) {
        toast({
          title: "Error",
          description: "Block name cannot be empty",
          variant: "destructive",
        });
        return;
      }

      const { data, error } = await supabase
        .from("blocks")
        .insert([{ name: newBlockName }])
        .select()
        .single();

      if (error) throw error;

      // Update local state
      setBlocks([...blocks, data]);
      setNewBlockName("");
      setIsAddingBlock(false);

      toast({
        title: "Success",
        description: "Block added successfully",
      });
    } catch (error) {
      console.error("Error adding block:", error);
      toast({
        title: "Error",
        description: "Failed to add block. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleAddRoom = async () => {
    try {
      if (!newRoomName.trim()) {
        toast({
          title: "Error",
          description: "Room name cannot be empty",
          variant: "destructive",
        });
        return;
      }

      if (selectedBlock === "all") {
        toast({
          title: "Error",
          description: "Please select a block to add the room to",
          variant: "destructive",
        });
        return;
      }

      const { data, error } = await supabase
        .from("rooms")
        .insert([
          {
            name: newRoomName,
            block_id: selectedBlock,
            teacher_id: profile?.id,
          },
        ])
        .select()
        .single();

      if (error) throw error;

      // Update local state
      setRooms([...rooms, data]);
      setNewRoomName("");
      setIsAddingRoom(false);

      toast({
        title: "Success",
        description: "Room added successfully",
      });
    } catch (error) {
      console.error("Error adding room:", error);
      toast({
        title: "Error",
        description: "Failed to add room. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="space-y-6">
      {/* Main Student Directory Table */}
      <Card>
        <CardHeader>
          <div className="flex flex-col md:flex-row justify-between md:items-center gap-4">
            <div>
              <CardTitle>{t("students.directory.title")}</CardTitle>
              <CardDescription>
                {t("students.directory.description")}
              </CardDescription>
            </div>
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex flex-col md:flex-row gap-4 mb-4">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder={t("students.directory.searchPlaceholder")}
                    className="pl-8 pr-8 w-full md:w-[280px]"
                    value={searchQuery}
                    onChange={(e) => {
                      // Update in real-time
                      setSearchQuery(e.target.value);
                    }}
                  />
                  {searchQuery && (
                    <button
                      onClick={() => setSearchQuery("")}
                      className="absolute right-2.5 top-2.5 h-4 w-4 text-muted-foreground hover:text-foreground"
                      aria-label="Clear search"
                    >
                      <X size={16} />
                    </button>
                  )}
                </div>
                {searchQuery && (
                  <Badge
                    variant="outline"
                    className="flex items-center gap-1 h-9 px-3"
                  >
                    <Search className="h-3 w-3" />
                    <span>
                      {searchQuery.length > 15
                        ? `${searchQuery.substring(0, 15)}...`
                        : searchQuery}
                    </span>
                  </Badge>
                )}
              </div>
              <div className="flex flex-col md:flex-row gap-4">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={toggleViewMode}
                  className="h-9 w-9"
                  title={
                    viewMode === "list"
                      ? t("students.directory.switchToGridView")
                      : t("students.directory.switchToListView")
                  }
                >
                  {viewMode === "list" ? (
                    <LayoutGrid className="h-4 w-4" />
                  ) : (
                    <LayoutList className="h-4 w-4" />
                  )}
                </Button>
                <div className="flex items-center gap-2">
                  <Building2 className="h-4 w-4 text-muted-foreground" />
                  <Select
                    value={selectedBlock}
                    onValueChange={handleBlockChange}
                  >
                    <SelectTrigger className="w-[180px]">
                      <SelectValue
                        placeholder={t("students.directory.selectBlock")}
                      />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">
                        {t("students.directory.allBlocks")}
                      </SelectItem>
                      {blocks.map((block) => (
                        <div
                          key={block.id}
                          className="flex items-center justify-between"
                        >
                          <SelectItem value={block.id}>
                            {t("common.block")} {block.name}
                          </SelectItem>
                          <DropdownMenu>
                            <DropdownMenuTrigger className="p-2 hover:bg-accent hover:text-accent-foreground rounded-md">
                              <MoreVertical className="h-4 w-4" />
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem
                                onClick={() => setEditingBlock(block)}
                              >
                                <Edit className="mr-2 h-4 w-4" />
                                {t("students.directory.editBlock")}
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                className="text-red-600"
                                onClick={() => handleDeleteBlock(block.id)}
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                {t("students.directory.deleteBlock")}
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      ))}
                      <div
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          setIsAddingBlock(true);
                        }}
                        className="relative flex w-full cursor-pointer select-none items-center rounded-sm py-1.5 px-2 text-sm outline-none border-t mt-2 pt-2 text-primary hover:bg-accent hover:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50"
                      >
                        + {t("students.directory.addNewBlock")}
                      </div>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-center gap-2">
                  <DoorOpen className="h-4 w-4 text-muted-foreground" />
                  <Select value={selectedRoom} onValueChange={handleRoomChange}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue
                        placeholder={t("students.directory.selectRoom")}
                      />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">
                        {t("students.directory.allRooms")}
                      </SelectItem>
                      {availableRooms.map((room) => (
                        <div
                          key={room.id}
                          className="flex items-center justify-between"
                        >
                          <SelectItem value={room.id}>
                            {t("common.room")} {room.name}
                          </SelectItem>
                          <DropdownMenu>
                            <DropdownMenuTrigger className="p-2 hover:bg-accent hover:text-accent-foreground rounded-md">
                              <MoreVertical className="h-4 w-4" />
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem
                                onClick={() => setEditingRoom(room)}
                              >
                                <Edit className="mr-2 h-4 w-4" />
                                {t("students.directory.editRoom")}
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                className="text-red-600"
                                onClick={() => handleDeleteRoom(room.id)}
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                {t("students.directory.deleteRoom")}
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      ))}
                      <div
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          setIsAddingRoom(true);
                        }}
                        className="relative flex w-full cursor-pointer select-none items-center rounded-sm py-1.5 px-2 text-sm outline-none border-t mt-2 pt-2 text-primary hover:bg-accent hover:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50"
                      >
                        + {t("students.directory.addNewRoom")}
                      </div>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center items-center h-32">
              <LoadingSpinner message="Loading students..." />
            </div>
          ) : viewMode === "grid" ? (
            // Grid View
            <div className="p-2">
              {assignedStudents.length > 0 ? (
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                  {assignedStudents.map((student) => {
                    const status = getAttendanceStatus(student.id);
                    return (
                      <div
                        key={student.id}
                        className="border rounded-md p-4 flex flex-col items-center hover:shadow-md transition-shadow"
                      >
                        <div className="relative mb-2">
                          {student.photoUrl ? (
                            <img
                              src={student.photoUrl}
                              alt={student.name}
                              className="w-24 h-24 rounded-full object-cover ring-2 ring-primary/10"
                            />
                          ) : (
                            <div className="w-24 h-24 rounded-full bg-muted flex items-center justify-center ring-2 ring-primary/10">
                              <UserCircle className="w-14 h-14 text-muted-foreground" />
                            </div>
                          )}
                          <div className="absolute -bottom-1 -right-1">
                            {getStatusIcon(status)}
                          </div>
                        </div>

                        <div className="font-medium text-center truncate w-full mb-1">
                          {student.name}
                        </div>

                        <div className="flex gap-2 mt-2 w-full">
                          <Badge
                            variant="outline"
                            className="flex-1 justify-center cursor-pointer hover:bg-secondary"
                            onClick={() => setSelectedStudent(student)}
                          >
                            View
                          </Badge>

                          <Badge
                            variant={
                              status === "present" ? "destructive" : "default"
                            }
                            className={`flex-1 justify-center cursor-pointer ${
                              status === "present"
                                ? ""
                                : "bg-teal-500 hover:bg-teal-600 text-white border-teal-500"
                            }`}
                            onClick={() => toggleAttendanceStatus(student)}
                          >
                            {status === "present" ? "Absent" : "Present"}
                          </Badge>
                        </div>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center h-32 text-center text-muted-foreground">
                  {searchQuery ? (
                    <>
                      <Search className="w-10 h-10 opacity-20 mb-2" />
                      <p>No students match your search criteria</p>
                    </>
                  ) : (
                    <>
                      <User className="w-10 h-10 opacity-20 mb-2" />
                      <p>No students found in this selection</p>
                    </>
                  )}
                </div>
              )}
            </div>
          ) : (
            // List View
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow className="bg-muted/50">
                    <TableHead className="w-[80px]">
                      {t("students.directory.profile")}
                    </TableHead>
                    <TableHead>{t("students.directory.name")}</TableHead>
                    <TableHead>{t("students.directory.studentId")}</TableHead>
                    <TableHead>{t("students.directory.course")}</TableHead>
                    <TableHead>{t("students.directory.block")}</TableHead>
                    <TableHead>{t("students.directory.room")}</TableHead>
                    <TableHead>{t("students.directory.status")}</TableHead>
                    <TableHead className="text-right">
                      {t("students.directory.actions")}
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {assignedStudents.length > 0 ? (
                    assignedStudents.map((student) => {
                      const status = getAttendanceStatus(student.id);
                      return (
                        <TableRow key={student.id}>
                          <TableCell>
                            {student.photoUrl ? (
                              <img
                                src={student.photoUrl}
                                alt={student.name}
                                className="w-10 h-10 rounded-full object-cover ring-2 ring-primary/10"
                              />
                            ) : (
                              <div className="w-10 h-10 rounded-full bg-muted flex items-center justify-center ring-2 ring-primary/10">
                                <UserCircle className="w-6 h-6 text-muted-foreground" />
                              </div>
                            )}
                          </TableCell>
                          <TableCell className="font-medium">
                            {student.name}
                          </TableCell>
                          <TableCell>
                            {student.studentId ||
                              t("students.directory.notSet")}
                          </TableCell>
                          <TableCell>
                            {student.course || t("students.directory.notSet")}
                          </TableCell>
                          <TableCell>
                            <Badge variant="secondary">
                              {student.blocks
                                ? `Block ${student.blocks.name}`
                                : t("students.directory.notAssigned")}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">
                              {student.rooms
                                ? `Room ${student.rooms.name}`
                                : t("students.directory.notAssigned")}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              {getStatusIcon(status)}
                              <span className="capitalize">
                                {t(`attendance.${status}`)}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end space-x-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setSelectedStudent(student)}
                              >
                                {t("students.directory.view")}
                              </Button>
                              <Button
                                variant={
                                  status === "present"
                                    ? "destructive"
                                    : "default"
                                }
                                size="sm"
                                className={
                                  status === "present"
                                    ? ""
                                    : "bg-teal-500 hover:bg-teal-600 text-white border-teal-500"
                                }
                                onClick={() => toggleAttendanceStatus(student)}
                              >
                                {status === "present" ? (
                                  <>
                                    <X className="w-4 h-4 mr-1" />
                                    {t("attendance.status.absent")}
                                  </>
                                ) : (
                                  <>
                                    <Check className="w-4 h-4 mr-1" />
                                    {t("attendance.status.present")}
                                  </>
                                )}
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })
                  ) : (
                    <TableRow>
                      <TableCell colSpan={8} className="h-24 text-center">
                        <div className="flex flex-col items-center justify-center text-muted-foreground">
                          {searchQuery ? (
                            <>
                              <Search className="w-10 h-10 opacity-20 mb-2" />
                              <p>
                                {t("students.directory.noMatchingStudents")}
                              </p>
                              <p className="text-sm mt-1">
                                {t("students.directory.trySearchingBy")}
                              </p>
                            </>
                          ) : selectedBlock !== "all" ||
                            selectedRoom !== "none" ? (
                            <>
                              <UserCircle className="w-10 h-10 opacity-20 mb-2" />
                              <p>
                                {t("students.directory.noStudentsAssignedTo", {
                                  type:
                                    selectedRoom !== "none"
                                      ? t("common.room").toLowerCase()
                                      : t("common.block").toLowerCase(),
                                })}
                              </p>
                            </>
                          ) : (
                            <>
                              <UserCircle className="w-10 h-10 opacity-20 mb-2" />
                              <p>
                                {t("students.directory.noStudentsInSystem")}
                              </p>
                            </>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Student Profile Dialog */}
      {selectedStudent && (
        <Dialog
          open={!!selectedStudent}
          onOpenChange={(open) => !open && setSelectedStudent(null)}
        >
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>{t("students.profile.title")}</DialogTitle>
              <DialogDescription>
                {t("students.profile.detailedInfo", {
                  name: selectedStudent.name,
                })}
              </DialogDescription>
            </DialogHeader>

            <div className="flex flex-col items-center space-y-4 pt-4">
              {/* Student Photo */}
              <div className="relative">
                {selectedStudent.photoUrl ? (
                  <img
                    src={selectedStudent.photoUrl}
                    alt={selectedStudent.name}
                    className="w-24 h-24 rounded-full object-cover border-2 border-primary"
                  />
                ) : (
                  <div className="w-24 h-24 rounded-full bg-muted flex items-center justify-center border-2 border-primary">
                    <User size={32} />
                  </div>
                )}
              </div>

              {/* Student Name */}
              <h2 className="text-xl font-bold">{selectedStudent.name}</h2>

              {/* Student Details */}
              <div className="w-full space-y-3">
                <div className="grid grid-cols-2 gap-2">
                  <div className="font-medium">
                    {t("students.profile.studentId")}:
                  </div>
                  <div>
                    {selectedStudent.studentId ||
                      t("students.directory.notSet")}
                  </div>

                  <div className="font-medium">
                    {t("students.profile.email")}:
                  </div>
                  <div className="break-all">{selectedStudent.email}</div>

                  <div className="font-medium">
                    {t("students.profile.course")}:
                  </div>
                  <div>
                    {selectedStudent.course || t("students.directory.notSet")}
                  </div>

                  <div className="font-medium">
                    {t("students.profile.block")}:
                  </div>
                  <div>
                    {selectedStudent.blocks
                      ? `${t("common.block")} ${selectedStudent.blocks.name}`
                      : t("students.directory.notAssigned")}
                  </div>

                  <div className="font-medium">
                    {t("students.profile.room")}:
                  </div>
                  <div>
                    {selectedStudent.rooms
                      ? `${t("common.room")} ${selectedStudent.rooms.name}`
                      : t("students.directory.notAssigned")}
                  </div>

                  <div className="font-medium">
                    {t("students.profile.biometric")}:
                  </div>
                  <div>
                    {selectedStudent.biometricRegistered ? (
                      <Badge className="bg-green-100 text-green-800">
                        {t("students.profile.registered")}
                      </Badge>
                    ) : (
                      <Badge
                        variant="outline"
                        className="text-muted-foreground"
                      >
                        {t("students.profile.notRegistered")}
                      </Badge>
                    )}
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex w-full space-x-2 pt-4">
                <Button
                  onClick={() => toggleAttendanceStatus(selectedStudent)}
                  className={`flex-1 ${
                    getAttendanceStatus(selectedStudent.id) === "present"
                      ? ""
                      : "bg-teal-500 hover:bg-teal-600 text-white border-teal-500"
                  }`}
                  variant={
                    getAttendanceStatus(selectedStudent.id) === "present"
                      ? "destructive"
                      : "default"
                  }
                >
                  {getAttendanceStatus(selectedStudent.id) === "present" ? (
                    <>
                      <X className="mr-2 h-4 w-4" />
                      {t("attendance.actions.markAsAbsent")}
                    </>
                  ) : (
                    <>
                      <Check className="mr-2 h-4 w-4" />
                      {t("attendance.actions.markAsPresent")}
                    </>
                  )}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}

      {/* Edit Block Dialog */}
      {editingBlock && (
        <Dialog
          open={!!editingBlock}
          onOpenChange={(open) => !open && setEditingBlock(null)}
        >
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                {t("students.directory.editBlockTitle")}
              </DialogTitle>
              <DialogDescription>
                {t("students.directory.editBlockDescription")}
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="blockName">
                  {t("students.directory.blockName")}
                </Label>
                <Input
                  id="blockName"
                  defaultValue={editingBlock.name}
                  onChange={(e) => {
                    const newBlock = { ...editingBlock, name: e.target.value };
                    setEditingBlock(newBlock);
                  }}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setEditingBlock(null)}>
                {t("common.cancel")}
              </Button>
              <Button
                onClick={() =>
                  handleEditBlock(editingBlock.id, editingBlock.name)
                }
              >
                {t("common.saveChanges")}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Edit Room Dialog */}
      {editingRoom && (
        <Dialog
          open={!!editingRoom}
          onOpenChange={(open) => !open && setEditingRoom(null)}
        >
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{t("students.directory.editRoomTitle")}</DialogTitle>
              <DialogDescription>
                {t("students.directory.editRoomDescription")}
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="roomName">
                  {t("students.directory.roomName")}
                </Label>
                <Input
                  id="roomName"
                  defaultValue={editingRoom.name}
                  onChange={(e) => {
                    const newRoom = { ...editingRoom, name: e.target.value };
                    setEditingRoom(newRoom);
                  }}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setEditingRoom(null)}>
                {t("common.cancel")}
              </Button>
              <Button
                onClick={() => handleEditRoom(editingRoom.id, editingRoom.name)}
              >
                {t("common.saveChanges")}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Add Block Dialog */}
      <Dialog
        open={isAddingBlock}
        onOpenChange={(open) => !open && setIsAddingBlock(false)}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {t("students.directory.addNewBlockTitle")}
            </DialogTitle>
            <DialogDescription>
              {t("students.directory.addNewBlockDescription")}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="newBlockName">
                {t("students.directory.blockName")}
              </Label>
              <Input
                id="newBlockName"
                value={newBlockName}
                onChange={(e) => setNewBlockName(e.target.value)}
                placeholder={t("students.directory.enterBlockName")}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setIsAddingBlock(false);
                setNewBlockName("");
              }}
            >
              {t("common.cancel")}
            </Button>
            <Button onClick={handleAddBlock}>
              {t("students.directory.addBlock")}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add Room Dialog */}
      <Dialog
        open={isAddingRoom}
        onOpenChange={(open) => !open && setIsAddingRoom(false)}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t("students.directory.addNewRoomTitle")}</DialogTitle>
            <DialogDescription>
              {t("students.directory.addNewRoomDescription")}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="newRoomName">
                {t("students.directory.roomName")}
              </Label>
              <Input
                id="newRoomName"
                value={newRoomName}
                onChange={(e) => setNewRoomName(e.target.value)}
                placeholder={t("students.directory.enterRoomName")}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setIsAddingRoom(false);
                setNewRoomName("");
              }}
            >
              {t("common.cancel")}
            </Button>
            <Button onClick={handleAddRoom}>
              {t("students.directory.addRoom")}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
