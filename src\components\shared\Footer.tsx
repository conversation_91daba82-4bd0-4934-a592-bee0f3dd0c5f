import { useState, useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import { Link, useNavigate } from "react-router-dom";
import { getFooterSettings, FooterSettings } from "@/lib/api/footer-settings";
import { useTheme } from "@/components/providers/ThemeProvider";
import { useLanguage } from "@/context/SimpleLanguageContext";
import { useAuth } from "@/context/AuthContext";
import LanguageToggle from "@/components/shared/LanguageToggle";
import FeedbackForm from "@/components/shared/FeedbackForm";
import { useToast } from "@/hooks/use-toast";
import {
  Github,
  Linkedin,
  Twitter,
  Facebook,
  Instagram,
  Youtube,
  Globe,
  Heart,
  ExternalLink,
  Moon,
  Sun,
  Mail,
  MessageSquare,
  FileText,
  Cookie,
  Phone,
  Smartphone,
} from "lucide-react";
import { FooterLogo } from "@/components/ui/logo";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import {
  Toolt<PERSON>,
  Toolt<PERSON><PERSON>ontent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface FooterProps {
  className?: string;
}

export default function Footer({ className }: FooterProps) {
  const { t } = useTranslation();
  const { theme, setTheme } = useTheme();
  const { currentLanguage } = useLanguage();
  const { profile } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [settings, setSettings] = useState<FooterSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const currentYear = new Date().getFullYear();

  // Long-press state for system admin setup
  const [isLongPressing, setIsLongPressing] = useState(false);
  const [longPressProgress, setLongPressProgress] = useState(0);
  const longPressTimerRef = useRef<NodeJS.Timeout | null>(null);
  const progressTimerRef = useRef<NodeJS.Timeout | null>(null);
  const startTimeRef = useRef<number | null>(null);
  const isMouseDownRef = useRef<boolean>(false);
  const LONG_PRESS_DURATION = 60000; // 60 seconds in milliseconds

  // Fetch footer settings
  useEffect(() => {
    const fetchSettings = async () => {
      try {
        // Log the fetch attempt
        console.log(
          "Attempting to fetch footer settings for Footer component..."
        );

        const data = await getFooterSettings();
        console.log("Footer settings fetched for Footer component:", data);

        if (data) {
          setSettings(data);
        } else {
          console.error("No footer settings returned from getFooterSettings");
        }
      } catch (error) {
        console.error("Error fetching footer settings:", error);
        console.error(
          "Error details:",
          error instanceof Error ? error.message : JSON.stringify(error)
        );
      } finally {
        setLoading(false);
      }
    };

    fetchSettings();
  }, []);

  // Toggle theme
  const toggleTheme = () => {
    setTheme(theme === "dark" ? "light" : "dark");
  };

  // Long-press handlers for system admin setup
  const handleLongPressStart = (e: React.MouseEvent | React.TouchEvent) => {
    // Only allow long-press when not logged in (on landing page)
    if (profile) {
      return;
    }

    // Clear any existing timers first
    if (longPressTimerRef.current) {
      clearTimeout(longPressTimerRef.current);
      longPressTimerRef.current = null;
    }
    if (progressTimerRef.current) {
      clearInterval(progressTimerRef.current);
      progressTimerRef.current = null;
    }

    // Prevent default behavior
    e.preventDefault();

    startTimeRef.current = Date.now();
    isMouseDownRef.current = true;
    setIsLongPressing(true);
    setLongPressProgress(0);

    // Start progress timer (updates every 100ms for smooth progress)
    progressTimerRef.current = setInterval(() => {
      setLongPressProgress((prev) => {
        const newProgress = prev + (100 / LONG_PRESS_DURATION) * 100;
        if (newProgress >= 100) {
          // Clear timers immediately when reaching 100%
          if (longPressTimerRef.current) {
            clearTimeout(longPressTimerRef.current);
            longPressTimerRef.current = null;
          }
          if (progressTimerRef.current) {
            clearInterval(progressTimerRef.current);
            progressTimerRef.current = null;
          }

          // Success! Navigate to system admin setup immediately
          setIsLongPressing(false);
          setLongPressProgress(0);

          toast({
            title: "System Administrator Access",
            description: "Redirecting to system admin setup...",
            duration: 2000,
          });

          setTimeout(() => {
            navigate("/system-admin-signup");
          }, 500);

          return 100;
        }
        return newProgress;
      });
    }, 100);

    // Backup timer for the full duration (in case progress timer fails)
    longPressTimerRef.current = setTimeout(() => {
      // Clear progress timer
      if (progressTimerRef.current) {
        clearInterval(progressTimerRef.current);
        progressTimerRef.current = null;
      }

      // Success! Navigate to system admin setup
      setIsLongPressing(false);
      setLongPressProgress(0);

      toast({
        title: "System Administrator Access",
        description: "Redirecting to system admin setup...",
        duration: 2000,
      });

      setTimeout(() => {
        navigate("/system-admin-signup");
      }, 500);
    }, LONG_PRESS_DURATION);
  };

  const handleLongPressEnd = () => {
    // Always clear timers when mouse/touch is released
    if (longPressTimerRef.current) {
      clearTimeout(longPressTimerRef.current);
      longPressTimerRef.current = null;
    }
    if (progressTimerRef.current) {
      clearInterval(progressTimerRef.current);
      progressTimerRef.current = null;
    }

    startTimeRef.current = null;
    isMouseDownRef.current = false;
    setIsLongPressing(false);
    setLongPressProgress(0);
  };

  // Global mouse up listener to catch missed mouse up events
  useEffect(() => {
    const handleGlobalMouseUp = () => {
      if (isMouseDownRef.current && isLongPressing) {
        handleLongPressEnd();
      }
    };

    const handleGlobalTouchEnd = () => {
      if (isMouseDownRef.current && isLongPressing) {
        handleLongPressEnd();
      }
    };

    // Add global listeners
    document.addEventListener("mouseup", handleGlobalMouseUp);
    document.addEventListener("touchend", handleGlobalTouchEnd);

    return () => {
      // Remove global listeners
      document.removeEventListener("mouseup", handleGlobalMouseUp);
      document.removeEventListener("touchend", handleGlobalTouchEnd);
    };
  }, [isLongPressing]);

  // Cleanup timers on unmount
  useEffect(() => {
    return () => {
      if (longPressTimerRef.current) {
        clearTimeout(longPressTimerRef.current);
      }
      if (progressTimerRef.current) {
        clearInterval(progressTimerRef.current);
      }
    };
  }, []);

  // If still loading, show a simple footer
  if (loading) {
    return (
      <footer className={cn("border-t py-4 md:py-6", className)}>
        <div className="container flex flex-col items-center justify-between gap-2 md:h-16 md:flex-row">
          <p className="text-center text-xs sm:text-sm leading-loose text-muted-foreground md:text-left">
            &copy; {currentYear} {t("app.name")}
          </p>
        </div>
      </footer>
    );
  }

  return (
    <footer className={cn("border-t py-6 md:py-0 bg-muted/50", className)}>
      <div className="container">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 md:gap-8 py-6 md:grid-cols-4">
          {/* App Info */}
          {settings?.show_app_info && (
            <div className="space-y-3">
              <FooterLogo />
              {settings?.app_tagline && (
                <p className="text-sm text-muted-foreground line-clamp-3 sm:line-clamp-none">
                  {settings.app_tagline}
                </p>
              )}
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="icon"
                  className="h-8 w-8"
                  onClick={toggleTheme}
                >
                  {theme === "dark" ? (
                    <Sun className="h-4 w-4" />
                  ) : (
                    <Moon className="h-4 w-4" />
                  )}
                  <span className="sr-only">Toggle theme</span>
                </Button>
                <LanguageToggle variant="compact" />
              </div>
            </div>
          )}

          {/* Legal Links */}
          {settings?.show_legal && (
            <div className="space-y-3">
              <h4 className="text-sm font-medium">{t("footer.legal")}</h4>
              <ul className="space-y-2 text-xs sm:text-sm">
                <li>
                  <Link
                    to="/"
                    className="text-muted-foreground hover:text-foreground transition-colors inline-flex items-center"
                    onClick={(e) => {
                      e.preventDefault();
                      const { role } = profile || { role: "" };
                      const path = role ? `/${role}` : "/";

                      // Navigate to the home page based on user role
                      navigate(path);
                    }}
                  >
                    {t("footer.home")}
                  </Link>
                </li>
                {settings?.privacy_policy_url && (
                  <li>
                    <a
                      href={settings.privacy_policy_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-muted-foreground hover:text-foreground transition-colors inline-flex items-center gap-1"
                    >
                      <FileText className="h-3 w-3 flex-shrink-0" />
                      <span className="truncate">
                        {t("footer.privacyPolicy")}
                      </span>
                      <ExternalLink className="h-3 w-3 flex-shrink-0" />
                    </a>
                  </li>
                )}
                {settings?.terms_of_service_url && (
                  <li>
                    <a
                      href={settings.terms_of_service_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-muted-foreground hover:text-foreground transition-colors inline-flex items-center gap-1"
                    >
                      <FileText className="h-3 w-3 flex-shrink-0" />
                      <span className="truncate">
                        {t("footer.termsOfService")}
                      </span>
                      <ExternalLink className="h-3 w-3 flex-shrink-0" />
                    </a>
                  </li>
                )}
                {settings?.cookie_policy_url && (
                  <li>
                    <a
                      href={settings.cookie_policy_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-muted-foreground hover:text-foreground transition-colors inline-flex items-center gap-1"
                    >
                      <Cookie className="h-3 w-3 flex-shrink-0" />
                      <span className="truncate">
                        {t("footer.cookiePolicy")}
                      </span>
                      <ExternalLink className="h-3 w-3 flex-shrink-0" />
                    </a>
                  </li>
                )}
              </ul>
            </div>
          )}

          {/* Contact */}
          {settings?.show_contact && (
            <div className="space-y-3">
              <h4 className="text-sm font-medium">{t("footer.contactUs")}</h4>
              <ul className="space-y-2 text-xs sm:text-sm">
                {settings?.contact_email && (
                  <li className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                    <a
                      href={`mailto:${settings.contact_email}`}
                      className="text-muted-foreground hover:text-foreground transition-colors truncate"
                    >
                      {settings.contact_email}
                    </a>
                  </li>
                )}
                <li>
                  <FeedbackForm variant="button" />
                </li>
                {settings?.developer_website && (
                  <li className="flex items-center gap-2">
                    <Globe className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                    <a
                      href={settings.developer_website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-muted-foreground hover:text-foreground transition-colors truncate"
                    >
                      {t("footer.visitOurWebsite")}
                    </a>
                  </li>
                )}
              </ul>
            </div>
          )}

          {/* Social */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium">{t("footer.connectWithUs")}</h4>
            <div className="flex flex-wrap gap-3">
              <TooltipProvider delayDuration={300}>
                {/* GitHub */}
                {settings && settings.github_url && settings.show_github && (
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <a
                        href={settings.github_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="rounded-md p-2 hover:bg-accent inline-flex items-center justify-center h-9 w-9"
                      >
                        <Github className="h-5 w-5" />
                        <span className="sr-only">GitHub</span>
                      </a>
                    </TooltipTrigger>
                    <TooltipContent>GitHub</TooltipContent>
                  </Tooltip>
                )}

                {/* LinkedIn */}
                {settings &&
                  settings.linkedin_url &&
                  settings.show_linkedin && (
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <a
                          href={settings.linkedin_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="rounded-md p-2 hover:bg-accent inline-flex items-center justify-center h-9 w-9"
                        >
                          <Linkedin className="h-5 w-5" />
                          <span className="sr-only">LinkedIn</span>
                        </a>
                      </TooltipTrigger>
                      <TooltipContent>LinkedIn</TooltipContent>
                    </Tooltip>
                  )}

                {/* Twitter */}
                {settings && settings.twitter_url && settings.show_twitter && (
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <a
                        href={settings.twitter_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="rounded-md p-2 hover:bg-accent inline-flex items-center justify-center h-9 w-9"
                      >
                        <Twitter className="h-5 w-5" />
                        <span className="sr-only">Twitter</span>
                      </a>
                    </TooltipTrigger>
                    <TooltipContent>Twitter</TooltipContent>
                  </Tooltip>
                )}

                {/* Facebook */}
                {settings &&
                  settings.facebook_url &&
                  settings.show_facebook && (
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <a
                          href={settings.facebook_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="rounded-md p-2 hover:bg-accent inline-flex items-center justify-center h-9 w-9"
                        >
                          <Facebook className="h-5 w-5" />
                          <span className="sr-only">Facebook</span>
                        </a>
                      </TooltipTrigger>
                      <TooltipContent>Facebook</TooltipContent>
                    </Tooltip>
                  )}

                {/* Instagram */}
                {settings &&
                  settings.instagram_url &&
                  settings.show_instagram && (
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <a
                          href={settings.instagram_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="rounded-md p-2 hover:bg-accent inline-flex items-center justify-center h-9 w-9"
                        >
                          <Instagram className="h-5 w-5" />
                          <span className="sr-only">Instagram</span>
                        </a>
                      </TooltipTrigger>
                      <TooltipContent>Instagram</TooltipContent>
                    </Tooltip>
                  )}

                {/* YouTube */}
                {settings && settings.youtube_url && settings.show_youtube && (
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <a
                        href={settings.youtube_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="rounded-md p-2 hover:bg-accent inline-flex items-center justify-center h-9 w-9"
                      >
                        <Youtube className="h-5 w-5" />
                        <span className="sr-only">YouTube</span>
                      </a>
                    </TooltipTrigger>
                    <TooltipContent>YouTube</TooltipContent>
                  </Tooltip>
                )}

                {/* WhatsApp */}
                {settings &&
                  settings.whatsapp_url &&
                  settings.show_whatsapp && (
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <a
                          href={settings.whatsapp_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="rounded-md p-2 hover:bg-accent inline-flex items-center justify-center h-9 w-9"
                        >
                          <Smartphone className="h-5 w-5" />
                          <span className="sr-only">WhatsApp</span>
                        </a>
                      </TooltipTrigger>
                      <TooltipContent>WhatsApp</TooltipContent>
                    </Tooltip>
                  )}

                {/* Show a message if no social media icons are displayed */}
                {settings &&
                  (!settings.github_url || !settings.show_github) &&
                  (!settings.linkedin_url || !settings.show_linkedin) &&
                  (!settings.twitter_url || !settings.show_twitter) &&
                  (!settings.facebook_url || !settings.show_facebook) &&
                  (!settings.instagram_url || !settings.show_instagram) &&
                  (!settings.youtube_url || !settings.show_youtube) &&
                  (!settings.whatsapp_url || !settings.show_whatsapp) && (
                    <p className="text-sm text-muted-foreground">
                      {t("footer.noSocialMediaLinks")}
                    </p>
                  )}
              </TooltipProvider>
            </div>
          </div>
        </div>

        <div className="flex flex-col items-center justify-between gap-4 border-t py-4 md:py-6 md:h-16 md:flex-row">
          {/* Copyright with long-press for system admin setup */}
          {settings?.show_copyright && (
            <div className="relative">
              <div
                className="text-center text-xs sm:text-sm leading-loose text-muted-foreground md:text-left cursor-default select-none p-4 -m-4"
                onMouseDown={(e) => {
                  if (!profile) {
                    e.preventDefault();
                    e.stopPropagation();
                    handleLongPressStart(e);
                  }
                }}
                onMouseUp={(e) => {
                  if (!profile) {
                    e.preventDefault();
                    e.stopPropagation();
                    handleLongPressEnd();
                  }
                }}
                onTouchStart={(e) => {
                  if (!profile) {
                    e.preventDefault();
                    e.stopPropagation();
                    handleLongPressStart(e);
                  }
                }}
                onTouchEnd={(e) => {
                  if (!profile) {
                    e.preventDefault();
                    e.stopPropagation();
                    handleLongPressEnd();
                  }
                }}
                onTouchCancel={(e) => {
                  if (!profile) {
                    handleLongPressEnd();
                  }
                }}
                onContextMenu={(e) => e.preventDefault()} // Prevent right-click menu
                style={{
                  userSelect: "none",
                  WebkitUserSelect: "none",
                  MozUserSelect: "none",
                  msUserSelect: "none",
                  WebkitTouchCallout: "none",
                  WebkitTapHighlightColor: "transparent",
                }}
              >
                {settings?.custom_copyright_text ? (
                  settings.custom_copyright_text
                ) : (
                  <>
                    &copy; {currentYear} {t("app.name")}.{" "}
                    {t("footer.allRightsReserved")}
                  </>
                )}
              </div>
            </div>
          )}

          {/* Developer Info */}
          {settings?.show_developer_info && settings?.developer_name && (
            <div className="text-center md:text-right relative">
              <p className="text-xs sm:text-sm leading-loose text-muted-foreground flex flex-wrap items-center justify-center md:justify-end gap-1">
                {t("footer.developedWith")}{" "}
                <Heart className="h-3 w-3 text-red-500" /> {t("footer.by")}{" "}
                {settings?.developer_website ? (
                  <a
                    href={settings.developer_website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="font-medium hover:underline flex items-center gap-1"
                  >
                    {settings.developer_name}
                    <Globe className="h-3 w-3" />
                  </a>
                ) : (
                  <span className="font-medium">{settings.developer_name}</span>
                )}
              </p>

              {/* Secret progress bar - extremely subtle and positioned absolutely */}
              {!profile && isLongPressing && (
                <div className="absolute top-full left-1/2 md:left-auto md:right-0 transform -translate-x-1/2 md:translate-x-0 mt-1">
                  <div className="w-16 h-[1px] bg-muted-foreground/20 overflow-hidden">
                    <div
                      className="h-full bg-muted-foreground/30 transition-all duration-100 ease-linear"
                      style={{ width: `${longPressProgress}%` }}
                    />
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </footer>
  );
}
