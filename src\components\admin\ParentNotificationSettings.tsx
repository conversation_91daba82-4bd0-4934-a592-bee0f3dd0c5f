import { useState, useEffect } from "react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/lib/supabase";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Mail, MessageSquare, AlertTriangle, CheckCircle2 } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useTranslation } from "react-i18next";

interface Settings {
  notifications_enabled: boolean;
  default_method: "email" | "sms" | "both" | "none";
  email_template_new: string;
  email_template_approved: string;
  email_template_rejected: string;
  sms_template_new: string;
  sms_template_approved: string;
  sms_template_rejected: string;
}

const defaultSettings: Settings = {
  notifications_enabled: true,
  default_method: "email",
  email_template_new:
    "Dear Parent/Guardian,\n\nThis is to inform you that your child, {{studentName}}, has submitted a request for absence from school.\n\nRequest Details:\n- Start Date: {{startDate}}\n- End Date: {{endDate}}\n- Reason: {{reason}}\n\nThis request is currently pending approval from school administration. You will be notified once the request has been reviewed.\n\nThank you,\nAttendance Tracking System",
  email_template_approved:
    "Dear Parent/Guardian,\n\nThis is to inform you that your child's absence request has been APPROVED.\n\nRequest Details:\n- Student: {{studentName}}\n- Start Date: {{startDate}}\n- End Date: {{endDate}}\n- Reason: {{reason}}\n\nIf you have any questions, please contact the school administration.\n\nThank you,\nAttendance Tracking System",
  email_template_rejected:
    "Dear Parent/Guardian,\n\nThis is to inform you that your child's absence request has been REJECTED.\n\nRequest Details:\n- Student: {{studentName}}\n- Start Date: {{startDate}}\n- End Date: {{endDate}}\n- Reason: {{reason}}\n\nIf you have any questions, please contact the school administration.\n\nThank you,\nAttendance Tracking System",
  sms_template_new:
    "NOTICE: {{studentName}} has requested absence from {{startDate}} to {{endDate}}. Reason: {{reason}}. Contact school for questions.",
  sms_template_approved:
    "APPROVED: {{studentName}}'s absence request ({{startDate}} to {{endDate}}) has been approved.",
  sms_template_rejected:
    "REJECTED: {{studentName}}'s absence request ({{startDate}} to {{endDate}}) has been rejected. Please contact school.",
};

export default function ParentNotificationSettings() {
  const [settings, setSettings] = useState<Settings>(defaultSettings);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState("general");
  const [templateType, setTemplateType] = useState<
    "new" | "approved" | "rejected"
  >("new");
  const { toast } = useToast();
  const { t } = useTranslation();

  // Fetch settings on component mount
  useEffect(() => {
    const fetchSettings = async () => {
      try {
        setLoading(true);

        // In a real implementation, we would fetch settings from the database
        // For now, we'll use the default settings with a slight delay to simulate loading
        setTimeout(() => {
          setSettings(defaultSettings);
          setLoading(false);
        }, 500);
      } catch (error) {
        console.error("Error fetching notification settings:", error);
        toast({
          title: t("common.error"),
          description: t("admin.settings.failedToLoadNotificationSettings"),
          variant: "destructive",
        });
        setLoading(false);
      }
    };

    fetchSettings();
  }, [toast]);

  // Handle toggle changes
  const handleToggleChange = (field: keyof Settings, value: boolean) => {
    setSettings((prev) => ({ ...prev, [field]: value }));
  };

  // Handle radio changes
  const handleRadioChange = (field: keyof Settings, value: string) => {
    setSettings((prev) => ({ ...prev, [field]: value }));
  };

  // Handle text input changes
  const handleTextChange = (field: keyof Settings, value: string) => {
    setSettings((prev) => ({ ...prev, [field]: value }));
  };

  // Save settings
  const saveSettings = async () => {
    try {
      setSaving(true);

      // In a real implementation, we would save settings to the database
      // For now, we'll just simulate a delay and show a success message
      await new Promise((resolve) => setTimeout(resolve, 1000));

      toast({
        title: t("admin.settings.settingsSaved"),
        description: t("admin.settings.parentNotificationSettingsUpdated"),
      });
    } catch (error) {
      console.error("Error saving notification settings:", error);
      toast({
        title: t("common.error"),
        description: t("admin.settings.failedToSaveNotificationSettings"),
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  // Reset settings to default
  const resetSettings = () => {
    if (window.confirm(t("admin.settings.confirmResetNotificationSettings"))) {
      setSettings(defaultSettings);
      toast({
        title: t("admin.settings.settingsReset"),
        description: t("admin.settings.parentNotificationSettingsReset"),
      });
    }
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="h-8 w-1/3 bg-gray-200 animate-pulse rounded"></div>
        <div className="h-24 bg-gray-200 animate-pulse rounded"></div>
        <div className="h-24 bg-gray-200 animate-pulse rounded"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="general">
            {t("admin.settings.generalSettingsTab")}
          </TabsTrigger>
          <TabsTrigger value="email">
            {t("admin.settings.emailTemplatesTab")}
          </TabsTrigger>
          <TabsTrigger value="sms">
            {t("admin.settings.smsTemplatesTab")}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>
                {t("admin.settings.notificationPreferences")}
              </CardTitle>
              <CardDescription>
                {t("admin.settings.configureGlobalParentNotifications")}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="notifications_enabled">
                    {t("admin.settings.enableParentNotifications")}
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    {t("admin.settings.whenEnabledParentsNotified")}
                  </p>
                </div>
                <Switch
                  id="notifications_enabled"
                  checked={settings.notifications_enabled}
                  onCheckedChange={(checked) =>
                    handleToggleChange("notifications_enabled", checked)
                  }
                />
              </div>

              <Separator />

              <div className="space-y-3">
                <Label>{t("admin.settings.defaultNotificationMethod")}</Label>
                <RadioGroup
                  value={settings.default_method}
                  onValueChange={(value) =>
                    handleRadioChange(
                      "default_method",
                      value as "email" | "sms" | "both" | "none"
                    )
                  }
                  className="space-y-3"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="email" id="method-email" />
                    <Label htmlFor="method-email" className="flex items-center">
                      <Mail className="mr-2 h-4 w-4" />
                      {t("admin.settings.emailOnly")}
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="sms" id="method-sms" />
                    <Label htmlFor="method-sms" className="flex items-center">
                      <MessageSquare className="mr-2 h-4 w-4" />
                      {t("admin.settings.smsOnly")}
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="both" id="method-both" />
                    <Label htmlFor="method-both">
                      {t("admin.settings.bothEmailAndSMS")}
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="none" id="method-none" />
                    <Label htmlFor="method-none">
                      {t("admin.settings.noNotificationsDisabled")}
                    </Label>
                  </div>
                </RadioGroup>
              </div>

              <Alert className="bg-blue-50 border-blue-200">
                <AlertTriangle className="h-4 w-4 text-blue-600" />
                <AlertTitle className="text-blue-800">
                  {t("admin.settings.importantNote")}
                </AlertTitle>
                <AlertDescription className="text-blue-700">
                  {t("admin.settings.globalDefaultSettingsInfo")}
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="email" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>
                {t("admin.settings.emailNotificationTemplates")}
              </CardTitle>
              <CardDescription>
                {t("admin.settings.customizeEmailTemplates")}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label>{t("admin.settings.templateType")}</Label>
                <div className="flex space-x-2">
                  <Button
                    variant={templateType === "new" ? "default" : "outline"}
                    onClick={() => setTemplateType("new")}
                    size="sm"
                  >
                    {t("admin.settings.newRequest")}
                  </Button>
                  <Button
                    variant={
                      templateType === "approved" ? "default" : "outline"
                    }
                    onClick={() => setTemplateType("approved")}
                    size="sm"
                  >
                    {t("admin.settings.approved")}
                  </Button>
                  <Button
                    variant={
                      templateType === "rejected" ? "default" : "outline"
                    }
                    onClick={() => setTemplateType("rejected")}
                    size="sm"
                  >
                    {t("admin.settings.rejected")}
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label>{t("admin.settings.emailTemplate")}</Label>
                <div className="text-xs text-muted-foreground mb-2">
                  {t("admin.settings.availableVariables")}:{" "}
                  {`{{studentName}}, {{startDate}}, {{endDate}}, {{reason}}`}
                </div>
                <textarea
                  className="w-full min-h-[200px] p-2 border rounded-md"
                  value={
                    templateType === "new"
                      ? settings.email_template_new
                      : templateType === "approved"
                      ? settings.email_template_approved
                      : settings.email_template_rejected
                  }
                  onChange={(e) =>
                    handleTextChange(
                      templateType === "new"
                        ? "email_template_new"
                        : templateType === "approved"
                        ? "email_template_approved"
                        : "email_template_rejected",
                      e.target.value
                    )
                  }
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="sms" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>
                {t("admin.settings.smsNotificationTemplates")}
              </CardTitle>
              <CardDescription>
                {t("admin.settings.customizeSmsTemplates")}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label>{t("admin.settings.templateType")}</Label>
                <div className="flex space-x-2">
                  <Button
                    variant={templateType === "new" ? "default" : "outline"}
                    onClick={() => setTemplateType("new")}
                    size="sm"
                  >
                    {t("admin.settings.newRequest")}
                  </Button>
                  <Button
                    variant={
                      templateType === "approved" ? "default" : "outline"
                    }
                    onClick={() => setTemplateType("approved")}
                    size="sm"
                  >
                    {t("admin.settings.approved")}
                  </Button>
                  <Button
                    variant={
                      templateType === "rejected" ? "default" : "outline"
                    }
                    onClick={() => setTemplateType("rejected")}
                    size="sm"
                  >
                    {t("admin.settings.rejected")}
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label>{t("admin.settings.smsTemplate")}</Label>
                <div className="text-xs text-muted-foreground mb-2">
                  {t("admin.settings.availableVariables")}:{" "}
                  {`{{studentName}}, {{startDate}}, {{endDate}}, {{reason}}`}
                </div>
                <div className="p-4 border rounded-md bg-gray-50">
                  <p className="text-sm text-muted-foreground mb-2">
                    {t("admin.settings.keepSmsMessagesConcise")}
                  </p>
                  <textarea
                    className="w-full min-h-[100px] p-2 border rounded-md"
                    value={
                      templateType === "new"
                        ? settings.sms_template_new
                        : templateType === "approved"
                        ? settings.sms_template_approved
                        : settings.sms_template_rejected
                    }
                    onChange={(e) =>
                      handleTextChange(
                        templateType === "new"
                          ? "sms_template_new"
                          : templateType === "approved"
                          ? "sms_template_approved"
                          : "sms_template_rejected",
                        e.target.value
                      )
                    }
                  />
                  <div className="mt-2 text-right text-sm">
                    <span
                      className={
                        (templateType === "new"
                          ? settings.sms_template_new.length
                          : templateType === "approved"
                          ? settings.sms_template_approved.length
                          : settings.sms_template_rejected.length) > 160
                          ? "text-red-500"
                          : "text-green-600"
                      }
                    >
                      {templateType === "new"
                        ? settings.sms_template_new.length
                        : templateType === "approved"
                        ? settings.sms_template_approved.length
                        : settings.sms_template_rejected.length}{" "}
                      / {t("admin.settings.characters", { count: 160 })}
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="flex justify-between">
        <Button variant="outline" onClick={resetSettings}>
          {t("admin.settings.resetToDefault")}
        </Button>
        <Button onClick={saveSettings} disabled={saving}>
          {saving
            ? t("admin.settings.saving")
            : t("admin.settings.saveSettings")}
        </Button>
      </div>
    </div>
  );
}
