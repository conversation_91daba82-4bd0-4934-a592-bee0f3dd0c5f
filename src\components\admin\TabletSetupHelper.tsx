import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Copy, ExternalLink, Tablet, QrCode, CheckCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/context/AuthContext";
import QRCode from "react-qr-code";

interface Block {
  id: string;
  name: string;
}

interface Room {
  id: string;
  name: string;
  building?: string;
  floor?: number;
  block_id: string;
}

export default function TabletSetupHelper() {
  const [blocks, setBlocks] = useState<Block[]>([]);
  const [rooms, setRooms] = useState<Room[]>([]);
  const [selectedBlock, setSelectedBlock] = useState<string>("");
  const [selectedRoom, setSelectedRoom] = useState<string>("");
  const [tabletUrls, setTabletUrls] = useState<Array<{
    roomId: string;
    roomName: string;
    url: string;
    qrCode: string;
  }>>([]);
  
  const { profile } = useAuth();
  const { toast } = useToast();

  // Fetch blocks and rooms
  useEffect(() => {
    const fetchData = async () => {
      if (!profile?.school_id) return;

      try {
        // Fetch blocks
        const { data: blocksData, error: blocksError } = await supabase
          .from("blocks")
          .select("*")
          .eq("school_id", profile.school_id)
          .order("name");

        if (blocksError) throw blocksError;
        setBlocks(blocksData || []);

        // Fetch all rooms for this school
        const { data: roomsData, error: roomsError } = await supabase
          .from("rooms")
          .select(`
            id,
            name,
            building,
            floor,
            block_id,
            blocks (
              name
            )
          `)
          .eq("school_id", profile.school_id)
          .order("name");

        if (roomsError) throw roomsError;
        setRooms(roomsData || []);
      } catch (error) {
        console.error("Error fetching data:", error);
        toast({
          title: "Error",
          description: "Failed to load blocks and rooms",
          variant: "destructive",
        });
      }
    };

    fetchData();
  }, [profile?.school_id]);

  // Generate tablet URL
  const generateTabletUrl = (roomId: string) => {
    const baseUrl = window.location.origin;
    const params = new URLSearchParams({
      school: profile?.school_id || "",
      room: roomId,
    });
    return `${baseUrl}/tablet?${params.toString()}`;
  };

  // Generate URLs for selected block
  const generateBlockUrls = () => {
    if (!selectedBlock) return;

    const blockRooms = rooms.filter(room => room.block_id === selectedBlock);
    const urls = blockRooms.map(room => ({
      roomId: room.id,
      roomName: room.name,
      url: generateTabletUrl(room.id),
      qrCode: generateTabletUrl(room.id),
    }));

    setTabletUrls(urls);
  };

  // Generate URL for single room
  const generateSingleRoomUrl = () => {
    if (!selectedRoom) return;

    const room = rooms.find(r => r.id === selectedRoom);
    if (!room) return;

    const url = generateTabletUrl(room.id);
    setTabletUrls([{
      roomId: room.id,
      roomName: room.name,
      url: url,
      qrCode: url,
    }]);
  };

  // Copy URL to clipboard
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast({
        title: "Copied!",
        description: "URL copied to clipboard",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to copy URL",
        variant: "destructive",
      });
    }
  };

  // Open URL in new tab
  const openInNewTab = (url: string) => {
    window.open(url, '_blank');
  };

  // Get filtered rooms for selected block
  const filteredRooms = selectedBlock 
    ? rooms.filter(room => room.block_id === selectedBlock)
    : rooms;

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Tablet className="w-6 h-6" />
            Tablet Setup Helper
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Block Selection */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Generate URLs for entire block</Label>
              <div className="flex gap-2">
                <Select value={selectedBlock} onValueChange={setSelectedBlock}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a block" />
                  </SelectTrigger>
                  <SelectContent>
                    {blocks.map((block) => (
                      <SelectItem key={block.id} value={block.id}>
                        Block {block.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Button 
                  onClick={generateBlockUrls}
                  disabled={!selectedBlock}
                >
                  Generate
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Generate URL for single room</Label>
              <div className="flex gap-2">
                <Select value={selectedRoom} onValueChange={setSelectedRoom}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a room" />
                  </SelectTrigger>
                  <SelectContent>
                    {filteredRooms.map((room) => (
                      <SelectItem key={room.id} value={room.id}>
                        {room.name}
                        {room.building && ` - ${room.building}`}
                        {room.floor && ` Floor ${room.floor}`}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Button 
                  onClick={generateSingleRoomUrl}
                  disabled={!selectedRoom}
                >
                  Generate
                </Button>
              </div>
            </div>
          </div>

          {/* Instructions */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-medium text-blue-800 mb-2">Setup Instructions:</h4>
            <ol className="text-sm text-blue-700 list-decimal list-inside space-y-1">
              <li>Generate URLs for the rooms where you want to place tablets</li>
              <li>Open each URL on the corresponding room's tablet</li>
              <li>The tablet will automatically configure itself for that room</li>
              <li>QR codes will appear automatically when generated from admin dashboard</li>
            </ol>
          </div>
        </CardContent>
      </Card>

      {/* Generated URLs */}
      {tabletUrls.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Generated Tablet URLs</h3>
          
          {tabletUrls.map((item, index) => (
            <Card key={index}>
              <CardHeader>
                <CardTitle className="text-base flex items-center justify-between">
                  <span>{item.roomName}</span>
                  <Badge variant="outline">Room {item.roomId.slice(-8)}</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* URL */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Tablet URL:</Label>
                  <div className="flex gap-2">
                    <Input 
                      value={item.url} 
                      readOnly 
                      className="font-mono text-sm"
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyToClipboard(item.url)}
                    >
                      <Copy className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => openInNewTab(item.url)}
                    >
                      <ExternalLink className="w-4 h-4" />
                    </Button>
                  </div>
                </div>

                {/* QR Code for URL */}
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <Label className="text-sm font-medium">Setup QR Code:</Label>
                    <p className="text-xs text-gray-600 mb-2">
                      Scan this QR code with the tablet to automatically open the URL
                    </p>
                  </div>
                  <div className="flex justify-center">
                    <div className="bg-white p-3 rounded-lg border">
                      <QRCode
                        value={item.qrCode}
                        size={120}
                        style={{ height: "auto", maxWidth: "100%", width: "100%" }}
                      />
                    </div>
                  </div>
                </div>

                {/* Quick Actions */}
                <div className="flex gap-2 pt-2 border-t">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(item.url)}
                    className="flex-1"
                  >
                    <Copy className="w-4 h-4 mr-1" />
                    Copy URL
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => openInNewTab(item.url)}
                    className="flex-1"
                  >
                    <ExternalLink className="w-4 h-4 mr-1" />
                    Test Tablet
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}

          {/* Bulk Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Bulk Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    const allUrls = tabletUrls.map(item => 
                      `${item.roomName}: ${item.url}`
                    ).join('\n');
                    copyToClipboard(allUrls);
                  }}
                >
                  <Copy className="w-4 h-4 mr-2" />
                  Copy All URLs
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    tabletUrls.forEach(item => {
                      setTimeout(() => openInNewTab(item.url), 100);
                    });
                  }}
                >
                  <ExternalLink className="w-4 h-4 mr-2" />
                  Open All Tablets
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
