-- Create multi-modal biometric registrations table
CREATE TABLE IF NOT EXISTS user_biometric_registrations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    biometric_type TEXT NOT NULL CHECK (biometric_type IN ('fingerprint', 'face', 'voice', 'iris')),
    encrypted_template TEXT NOT NULL,
    confidence_threshold DECIMAL(3,2) DEFAULT 0.85,
    device_info TEXT,
    registration_data JSONB, -- Store additional registration metadata
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure one active registration per user per biometric type
    UNIQUE(user_id, biometric_type) WHERE is_active = true
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_user_biometric_registrations_user_type 
ON user_biometric_registrations(user_id, biometric_type) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_user_biometric_registrations_created_at 
ON user_biometric_registrations(created_at);

-- Create biometric authentication attempts table
CREATE TABLE IF NOT EXISTS biometric_auth_attempts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    biometric_type TEXT NOT NULL,
    success BOOLEAN NOT NULL,
    confidence_score DECIMAL(3,2),
    error_message TEXT,
    device_info TEXT,
    ip_address INET,
    user_agent TEXT,
    session_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for auth attempts
CREATE INDEX IF NOT EXISTS idx_biometric_auth_attempts_user_created 
ON biometric_auth_attempts(user_id, created_at);

CREATE INDEX IF NOT EXISTS idx_biometric_auth_attempts_success 
ON biometric_auth_attempts(success, created_at);

CREATE INDEX IF NOT EXISTS idx_biometric_auth_attempts_type 
ON biometric_auth_attempts(biometric_type, created_at);

-- Enable Row Level Security
ALTER TABLE user_biometric_registrations ENABLE ROW LEVEL SECURITY;
ALTER TABLE biometric_auth_attempts ENABLE ROW LEVEL SECURITY;

-- RLS Policies for user_biometric_registrations
CREATE POLICY "Users can view their own biometric registrations" 
ON user_biometric_registrations FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own biometric registrations" 
ON user_biometric_registrations FOR INSERT 
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own biometric registrations" 
ON user_biometric_registrations FOR UPDATE 
USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own biometric registrations" 
ON user_biometric_registrations FOR DELETE 
USING (auth.uid() = user_id);

-- Admin access to biometric registrations
CREATE POLICY "Admins can view all biometric registrations" 
ON user_biometric_registrations FOR SELECT 
USING (
    EXISTS (
        SELECT 1 FROM profiles 
        WHERE profiles.id = auth.uid() 
        AND profiles.role = 'admin'
    )
);

-- RLS Policies for biometric_auth_attempts
CREATE POLICY "Users can view their own biometric auth attempts" 
ON biometric_auth_attempts FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "System can insert biometric auth attempts" 
ON biometric_auth_attempts FOR INSERT 
WITH CHECK (true);

-- Admin access to auth attempts
CREATE POLICY "Admins can view all biometric auth attempts" 
ON biometric_auth_attempts FOR SELECT 
USING (
    EXISTS (
        SELECT 1 FROM profiles 
        WHERE profiles.id = auth.uid() 
        AND profiles.role = 'admin'
    )
);

-- Function to register a biometric
CREATE OR REPLACE FUNCTION register_biometric(
    p_user_id UUID,
    p_biometric_type TEXT,
    p_encrypted_template TEXT,
    p_confidence_threshold DECIMAL DEFAULT 0.85,
    p_device_info TEXT DEFAULT NULL,
    p_registration_data JSONB DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
    registration_id UUID;
BEGIN
    -- Deactivate any existing registration of the same type
    UPDATE user_biometric_registrations 
    SET is_active = false, updated_at = NOW()
    WHERE user_id = p_user_id 
    AND biometric_type = p_biometric_type 
    AND is_active = true;
    
    -- Insert new registration
    INSERT INTO user_biometric_registrations (
        user_id,
        biometric_type,
        encrypted_template,
        confidence_threshold,
        device_info,
        registration_data
    ) VALUES (
        p_user_id,
        p_biometric_type,
        p_encrypted_template,
        p_confidence_threshold,
        p_device_info,
        p_registration_data
    ) RETURNING id INTO registration_id;
    
    RETURN registration_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to authenticate with biometric
CREATE OR REPLACE FUNCTION authenticate_biometric(
    p_user_id UUID,
    p_biometric_type TEXT,
    p_confidence_score DECIMAL,
    p_device_info TEXT DEFAULT NULL,
    p_ip_address INET DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL,
    p_session_data JSONB DEFAULT NULL
) RETURNS BOOLEAN AS $$
DECLARE
    registration_record RECORD;
    auth_success BOOLEAN := false;
BEGIN
    -- Get the user's registration for this biometric type
    SELECT * INTO registration_record
    FROM user_biometric_registrations
    WHERE user_id = p_user_id 
    AND biometric_type = p_biometric_type 
    AND is_active = true;
    
    -- Check if registration exists and confidence meets threshold
    IF FOUND AND p_confidence_score >= registration_record.confidence_threshold THEN
        auth_success := true;
    END IF;
    
    -- Log the authentication attempt
    INSERT INTO biometric_auth_attempts (
        user_id,
        biometric_type,
        success,
        confidence_score,
        device_info,
        ip_address,
        user_agent,
        session_data
    ) VALUES (
        p_user_id,
        p_biometric_type,
        auth_success,
        p_confidence_score,
        p_device_info,
        p_ip_address,
        p_user_agent,
        p_session_data
    );
    
    RETURN auth_success;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's registered biometric types
CREATE OR REPLACE FUNCTION get_user_biometric_types(p_user_id UUID)
RETURNS TABLE (
    biometric_type TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    confidence_threshold DECIMAL,
    device_info TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ubr.biometric_type,
        ubr.created_at,
        ubr.confidence_threshold,
        ubr.device_info
    FROM user_biometric_registrations ubr
    WHERE ubr.user_id = p_user_id
    AND ubr.is_active = true
    ORDER BY ubr.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to remove biometric registration
CREATE OR REPLACE FUNCTION remove_biometric_registration(
    p_user_id UUID,
    p_biometric_type TEXT
) RETURNS BOOLEAN AS $$
BEGIN
    UPDATE user_biometric_registrations 
    SET is_active = false, updated_at = NOW()
    WHERE user_id = p_user_id 
    AND biometric_type = p_biometric_type 
    AND is_active = true;
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to cleanup old auth attempts (keep last 90 days)
CREATE OR REPLACE FUNCTION cleanup_biometric_auth_attempts()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM biometric_auth_attempts 
    WHERE created_at < NOW() - INTERVAL '90 days';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_user_biometric_registrations_updated_at
    BEFORE UPDATE ON user_biometric_registrations
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON user_biometric_registrations TO authenticated;
GRANT ALL ON biometric_auth_attempts TO authenticated;
GRANT EXECUTE ON FUNCTION register_biometric TO authenticated;
GRANT EXECUTE ON FUNCTION authenticate_biometric TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_biometric_types TO authenticated;
GRANT EXECUTE ON FUNCTION remove_biometric_registration TO authenticated;
GRANT EXECUTE ON FUNCTION cleanup_biometric_auth_attempts TO authenticated;
