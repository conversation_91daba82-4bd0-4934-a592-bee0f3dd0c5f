import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  Shield, 
  Plus, 
  Trash2, 
  CheckCircle, 
  XCircle,
  Fingerprint,
  Scan,
  Mic,
  Eye,
  Settings
} from "lucide-react";
import { useAuth } from "@/context/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { biometricService, type BiometricType, type BiometricRegistration } from "@/lib/services/biometric-service";
import BiometricAuth from "@/components/auth/BiometricAuth";

const biometricIcons = {
  fingerprint: Fingerprint,
  face: Scan,
  voice: Mic,
  iris: Eye,
};

const biometricLabels = {
  fingerprint: 'Fingerprint',
  face: 'Face Recognition',
  voice: 'Voice Recognition',
  iris: '<PERSON> Scan',
};

const biometricDescriptions = {
  fingerprint: 'Use your fingerprint to authenticate quickly and securely',
  face: 'Use facial recognition for hands-free authentication',
  voice: 'Use your voice pattern for authentication',
  iris: 'Use iris scanning for the highest level of security',
};

export default function BiometricSettings() {
  const [registrations, setRegistrations] = useState<BiometricRegistration[]>([]);
  const [supportedTypes, setSupportedTypes] = useState<BiometricType[]>([]);
  const [showRegistration, setShowRegistration] = useState(false);
  const [selectedType, setSelectedType] = useState<BiometricType | null>(null);
  const [loading, setLoading] = useState(true);
  
  const { user } = useAuth();
  const { toast } = useToast();

  // Load biometric data on mount
  useEffect(() => {
    const loadBiometricData = async () => {
      if (!user) return;

      try {
        setLoading(true);
        
        // Check supported biometric types
        const support = await biometricService.isSupported();
        setSupportedTypes(support.types);
        
        // Load existing registrations
        const userBiometrics = await biometricService.getUserBiometrics(user.id);
        setRegistrations(userBiometrics);
      } catch (error) {
        console.error('Error loading biometric data:', error);
        toast({
          title: "Error",
          description: "Failed to load biometric settings",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    loadBiometricData();
  }, [user, toast]);

  // Handle biometric registration
  const handleRegister = (type: BiometricType) => {
    setSelectedType(type);
    setShowRegistration(true);
  };

  // Handle registration success
  const handleRegistrationSuccess = () => {
    setShowRegistration(false);
    setSelectedType(null);
    
    // Reload registrations
    if (user) {
      biometricService.getUserBiometrics(user.id).then(setRegistrations);
    }
    
    toast({
      title: "Success",
      description: "Biometric authentication registered successfully!",
    });
  };

  // Handle registration error
  const handleRegistrationError = (error: string) => {
    toast({
      title: "Registration Failed",
      description: error,
      variant: "destructive",
    });
  };

  // Remove biometric registration
  const handleRemove = async (type: BiometricType) => {
    if (!user) return;

    try {
      const success = await biometricService.deleteBiometricRegistration(user.id, type);
      if (success) {
        setRegistrations(prev => prev.filter(r => r.type !== type));
        toast({
          title: "Success",
          description: `${biometricLabels[type]} removed successfully`,
        });
      } else {
        throw new Error('Failed to remove biometric');
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to remove biometric registration",
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-8">
          <div className="text-center">
            <Shield className="w-8 h-8 mx-auto text-gray-400 mb-2" />
            <p className="text-gray-600">Loading biometric settings...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (supportedTypes.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            Biometric Authentication
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <XCircle className="w-12 h-12 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Biometric Authentication Not Available
            </h3>
            <p className="text-gray-600">
              Your device doesn't support biometric authentication methods.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            Biometric Authentication
          </CardTitle>
          <p className="text-sm text-gray-600">
            Set up biometric authentication for quick and secure access to your account.
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Registered Biometrics */}
          {registrations.length > 0 && (
            <div>
              <h3 className="text-sm font-medium text-gray-900 mb-3">
                Active Biometric Methods
              </h3>
              <div className="space-y-3">
                {registrations.map((registration) => {
                  const Icon = biometricIcons[registration.type as BiometricType];
                  return (
                    <div
                      key={registration.id}
                      className="flex items-center justify-between p-3 border rounded-lg"
                    >
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-green-100 rounded-lg">
                          <Icon className="w-5 h-5 text-green-600" />
                        </div>
                        <div>
                          <p className="font-medium">
                            {biometricLabels[registration.type as BiometricType]}
                          </p>
                          <p className="text-sm text-gray-600">
                            Registered on {new Date(registration.created_at).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-green-600 border-green-200">
                          <CheckCircle className="w-3 h-3 mr-1" />
                          Active
                        </Badge>
                        <Button
                          onClick={() => handleRemove(registration.type as BiometricType)}
                          variant="outline"
                          size="sm"
                          className="text-red-600 border-red-200 hover:bg-red-50"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {/* Available Biometric Methods */}
          <div>
            <h3 className="text-sm font-medium text-gray-900 mb-3">
              Available Biometric Methods
            </h3>
            <div className="grid gap-4">
              {supportedTypes.map((type) => {
                const Icon = biometricIcons[type];
                const isRegistered = registrations.some(r => r.type === type);
                
                return (
                  <div
                    key={type}
                    className="flex items-center justify-between p-4 border rounded-lg"
                  >
                    <div className="flex items-center gap-3">
                      <div className={`p-2 rounded-lg ${
                        isRegistered ? 'bg-green-100' : 'bg-gray-100'
                      }`}>
                        <Icon className={`w-5 h-5 ${
                          isRegistered ? 'text-green-600' : 'text-gray-600'
                        }`} />
                      </div>
                      <div>
                        <p className="font-medium">{biometricLabels[type]}</p>
                        <p className="text-sm text-gray-600">
                          {biometricDescriptions[type]}
                        </p>
                      </div>
                    </div>
                    <div>
                      {isRegistered ? (
                        <Badge variant="outline" className="text-green-600 border-green-200">
                          <CheckCircle className="w-3 h-3 mr-1" />
                          Registered
                        </Badge>
                      ) : (
                        <Button
                          onClick={() => handleRegister(type)}
                          variant="outline"
                          size="sm"
                        >
                          <Plus className="w-4 h-4 mr-1" />
                          Register
                        </Button>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Security Notice */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="flex items-start gap-3">
              <Shield className="w-5 h-5 text-blue-600 mt-0.5" />
              <div className="text-sm">
                <p className="font-medium text-blue-800 mb-1">Security Information</p>
                <ul className="text-blue-700 space-y-1">
                  <li>• Your biometric data is encrypted and stored securely</li>
                  <li>• Biometric templates cannot be reverse-engineered</li>
                  <li>• You can always use your PIN as a backup method</li>
                  <li>• Remove biometric access anytime from this page</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Biometric Registration Modal */}
      {showRegistration && selectedType && user && (
        <Card>
          <CardHeader>
            <CardTitle>Register {biometricLabels[selectedType]}</CardTitle>
          </CardHeader>
          <CardContent>
            <BiometricAuth
              userId={user.id}
              mode="register"
              onSuccess={handleRegistrationSuccess}
              onError={handleRegistrationError}
              className="border-0 shadow-none"
            />
            <div className="mt-4">
              <Button
                onClick={() => {
                  setShowRegistration(false);
                  setSelectedType(null);
                }}
                variant="outline"
                className="w-full"
              >
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
