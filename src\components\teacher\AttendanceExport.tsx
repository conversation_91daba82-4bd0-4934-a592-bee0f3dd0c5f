import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { format as formatDate, subDays } from "date-fns";
import {
  Calendar as CalendarIcon,
  Download,
  FileDown,
  AlertCircle,
  Check,
  Filter,
} from "lucide-react";
import { cn } from "@/lib/utils";
import {
  downloadCSV,
  downloadPDF,
  filterRecordsByDateRange,
  filterRecordsByStatus,
} from "@/lib/reportUtils";
import { AttendanceRecord } from "@/lib/types";
import { useToast } from "@/hooks/use-toast";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { useTranslation } from "react-i18next";

interface AttendanceExportProps {
  records: AttendanceRecord[];
  roomName?: string;
}

// Define status type for filtering
type StatusFilter = "all" | "present" | "absent" | "late" | "excused";

export function AttendanceExport({ records, roomName }: AttendanceExportProps) {
  const { t } = useTranslation();

  // Status display information with translations
  const statusInfo = {
    all: {
      label: t("attendance.status.allStatuses"),
      color: "default",
      icon: "🔍",
    },
    present: {
      label: t("attendance.status.presentOnly"),
      color: "green",
      icon: "✅",
    },
    absent: {
      label: t("attendance.status.absentOnly"),
      color: "destructive",
      icon: "❌",
    },
    late: {
      label: t("attendance.status.lateOnly"),
      color: "yellow",
      icon: "⏰",
    },
    excused: {
      label: t("attendance.status.excusedOnly"),
      color: "blue",
      icon: "📝",
    },
  };

  const [dateRange, setDateRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({
    from: undefined,
    to: undefined,
  });
  const [recordsCount, setRecordsCount] = useState<number>(0);
  const [selectedStatus, setSelectedStatus] = useState<StatusFilter>("all");
  const { toast } = useToast();

  // Set default date range on component mount (last 7 days)
  useEffect(() => {
    const today = new Date();
    const lastWeek = subDays(today, 7);

    setDateRange({
      from: lastWeek,
      to: today,
    });
  }, []);

  // Update records count when records, date range, or status filter changes
  useEffect(() => {
    if (dateRange.from && dateRange.to && records.length > 0) {
      // First filter by date range
      let filteredRecords = filterRecordsByDateRange(
        records,
        dateRange.from,
        dateRange.to
      );

      // Then filter by status if not "all"
      if (selectedStatus !== "all") {
        filteredRecords = filterRecordsByStatus(
          filteredRecords,
          selectedStatus
        );
      }

      setRecordsCount(filteredRecords.length);
    } else {
      setRecordsCount(0);
    }
  }, [records, dateRange.from, dateRange.to, selectedStatus]);

  const handleExport = (format: "csv" | "pdf") => {
    if (!dateRange.from || !dateRange.to) {
      toast({
        title: t("attendance.export.dateRangeRequired"),
        description: t("attendance.export.selectDateRange"),
        variant: "destructive",
      });
      return;
    }

    console.log("Exporting records:", {
      totalRecords: records.length,
      dateRange: {
        from: dateRange.from.toISOString(),
        to: dateRange.to.toISOString(),
      },
    });

    // First filter by date range
    let filteredRecords = filterRecordsByDateRange(
      records,
      dateRange.from,
      dateRange.to
    );

    // Then filter by status if not "all"
    if (selectedStatus !== "all") {
      filteredRecords = filterRecordsByStatus(filteredRecords, selectedStatus);
    }

    if (filteredRecords.length === 0) {
      toast({
        title: t("attendance.export.noRecordsFound"),
        description:
          selectedStatus === "all"
            ? t("attendance.export.noRecordsForDateRange")
            : t("attendance.export.noStatusRecordsForDateRange", {
                status: t(`attendance.status.${selectedStatus}`),
              }),
        variant: "destructive",
      });
      return;
    }

    // Add room name to each record for better reporting
    const enhancedRecords = filteredRecords.map((record) => ({
      ...record,
      roomName: record.roomName || roomName || "Unknown Room",
    }));

    const dateStr = formatDate(new Date(), "yyyy-MM-dd");

    // Include status in the title if filtering
    const statusText =
      selectedStatus !== "all" ? ` (${statusInfo[selectedStatus].label})` : "";

    const title = `Attendance Report - ${roomName || "All Rooms"}${statusText}`;

    // Include status in the filename if filtering
    const statusSuffix = selectedStatus !== "all" ? `-${selectedStatus}` : "";

    const filename = `attendance-report-${
      roomName ? roomName.replace(/\s+/g, "-").toLowerCase() : "all"
    }${statusSuffix}-${dateStr}`;

    try {
      if (format === "csv") {
        downloadCSV(enhancedRecords, `${filename}.csv`, dateRange);
      } else {
        downloadPDF(enhancedRecords, title, `${filename}.pdf`, dateRange);
      }

      toast({
        title: t("attendance.export.exportSuccessful"),
        description: t("attendance.export.reportExportedAs", {
          format: format.toUpperCase(),
        }),
      });
    } catch (error) {
      console.error("Export error:", error);
      toast({
        title: t("attendance.export.exportFailed"),
        description: t("attendance.export.failedToGenerate"),
        variant: "destructive",
      });
    }
  };

  return (
    <div className="flex flex-col gap-4 border rounded-lg p-4 shadow-sm">
      {/* Mobile View - Only visible on small screens */}
      <div className="md:hidden">
        {/* Header Section */}
        <div className="flex flex-col justify-between items-start mb-3">
          <div className="space-y-1 mb-2">
            <h3 className="font-medium text-base">
              {t("attendance.export.exportRecords")}
            </h3>
            <p className="text-xs text-muted-foreground">
              {t("attendance.export.selectDateRangeAndFormat")}
            </p>
          </div>
        </div>

        {/* Mobile Date Range and Filter Controls */}
        <div className="grid grid-cols-2 gap-2">
          {/* Start Date - Mobile */}
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "justify-start text-left font-normal text-xs h-9 px-2",
                  !dateRange.from && "text-muted-foreground"
                )}
                size="sm"
              >
                <CalendarIcon className="mr-1 h-3 w-3 flex-shrink-0" />
                <span className="truncate">
                  {dateRange.from ? (
                    formatDate(dateRange.from, "MMM dd")
                  ) : (
                    <span>{t("attendance.export.start")}</span>
                  )}
                </span>
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={dateRange.from}
                onSelect={(date) =>
                  setDateRange((prev) => ({ ...prev, from: date }))
                }
                initialFocus
                className="rounded-md border shadow-sm text-xs"
              />
            </PopoverContent>
          </Popover>

          {/* End Date - Mobile */}
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "justify-start text-left font-normal text-xs h-9 px-2",
                  !dateRange.to && "text-muted-foreground"
                )}
                size="sm"
              >
                <CalendarIcon className="mr-1 h-3 w-3 flex-shrink-0" />
                <span className="truncate">
                  {dateRange.to ? (
                    formatDate(dateRange.to, "MMM dd")
                  ) : (
                    <span>{t("attendance.export.end")}</span>
                  )}
                </span>
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={dateRange.to}
                onSelect={(date) =>
                  setDateRange((prev) => ({ ...prev, to: date }))
                }
                initialFocus
                className="rounded-md border shadow-sm text-xs"
              />
            </PopoverContent>
          </Popover>

          {/* Status Filter Dropdown - Mobile */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className="col-span-2 h-9 px-2 text-xs"
              >
                <Filter className="mr-1 h-3 w-3 flex-shrink-0" />
                <span className="hidden xs:inline mr-1">
                  {t("attendance.export.filter")}:
                </span>
                <Badge
                  variant={
                    selectedStatus === "all"
                      ? "outline"
                      : (selectedStatus as any)
                  }
                  className="ml-0 text-[10px] px-1"
                >
                  {statusInfo[selectedStatus].icon}{" "}
                  <span className="truncate max-w-[80px]">
                    {statusInfo[selectedStatus].label}
                  </span>
                </Badge>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-44 p-1">
              <DropdownMenuLabel className="text-xs px-2 py-1">
                {t("attendance.export.filterByStatus")}
              </DropdownMenuLabel>
              <DropdownMenuSeparator className="my-1" />
              <DropdownMenuGroup>
                {(Object.keys(statusInfo) as StatusFilter[]).map((status) => (
                  <DropdownMenuItem
                    key={status}
                    onClick={() => setSelectedStatus(status)}
                    className={cn(
                      "cursor-pointer text-xs py-1.5 px-2",
                      selectedStatus === status && "bg-accent font-medium"
                    )}
                  >
                    <span className="mr-1 flex-shrink-0">
                      {statusInfo[status].icon}
                    </span>
                    <span className="truncate">{statusInfo[status].label}</span>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuGroup>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Export Buttons - Mobile */}
          <div className="flex gap-2 mt-1 col-span-2">
            <Button
              onClick={() => handleExport("csv")}
              variant="outline"
              size="sm"
              className="flex-1 h-9 text-xs"
              disabled={recordsCount === 0}
            >
              <FileDown className="mr-1 h-3 w-3" />
              CSV
            </Button>
            <Button
              onClick={() => handleExport("pdf")}
              size="sm"
              className="flex-1 h-9 text-xs"
              disabled={recordsCount === 0}
            >
              <Download className="mr-1 h-3 w-3" />
              PDF
            </Button>
          </div>
        </div>

        {/* Mobile Results */}
        {dateRange.from && dateRange.to && (
          <div className="text-xs mt-3">
            {recordsCount > 0 ? (
              <div className="text-green-600 flex items-center gap-1 flex-wrap">
                <Check className="h-3 w-3 flex-shrink-0" />
                <span>
                  {t("attendance.export.recordsFound", { count: recordsCount })}
                </span>
                {selectedStatus !== "all" && (
                  <span className="ml-1 flex items-center flex-wrap">
                    <span>{t("attendance.export.withStatus")}</span>{" "}
                    <Badge
                      variant={selectedStatus as any}
                      className="ml-1 text-[10px]"
                    >
                      {statusInfo[selectedStatus].icon}{" "}
                      {t(`attendance.status.${selectedStatus}`)}
                    </Badge>
                  </span>
                )}
              </div>
            ) : (
              <Alert variant="warning" className="py-1 text-xs">
                <AlertCircle className="h-3 w-3 flex-shrink-0" />
                <AlertDescription>
                  {t("attendance.export.noRecordsAvailable")}
                </AlertDescription>
              </Alert>
            )}
          </div>
        )}
      </div>

      {/* Desktop View - Original Layout (Preserved Exactly) */}
      <div className="hidden md:block">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
          <div className="flex-1 space-y-1">
            <h3 className="font-medium">
              {t("attendance.export.exportRecords")}
            </h3>
            <p className="text-sm text-muted-foreground">
              {t("attendance.export.selectDateRangeAndFormat")}
            </p>
          </div>
          <div className="flex flex-wrap gap-2">
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "justify-start text-left font-normal",
                    !dateRange.from && "text-muted-foreground"
                  )}
                  size="sm"
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {dateRange.from ? (
                    formatDate(dateRange.from, "MMM dd")
                  ) : (
                    <span>{t("attendance.export.start")}</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={dateRange.from}
                  onSelect={(date) =>
                    setDateRange((prev) => ({ ...prev, from: date }))
                  }
                  initialFocus
                />
              </PopoverContent>
            </Popover>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "justify-start text-left font-normal",
                    !dateRange.to && "text-muted-foreground"
                  )}
                  size="sm"
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {dateRange.to ? (
                    formatDate(dateRange.to, "MMM dd")
                  ) : (
                    <span>{t("attendance.export.end")}</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={dateRange.to}
                  onSelect={(date) =>
                    setDateRange((prev) => ({ ...prev, to: date }))
                  }
                  initialFocus
                />
              </PopoverContent>
            </Popover>

            {/* Status Filter Dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="gap-2">
                  <Filter className="h-4 w-4" />
                  <span className="hidden sm:inline">
                    {t("attendance.export.filter")}:
                  </span>
                  <Badge
                    variant={
                      selectedStatus === "all"
                        ? "outline"
                        : (selectedStatus as any)
                    }
                    className="ml-1"
                  >
                    {statusInfo[selectedStatus].icon}{" "}
                    {statusInfo[selectedStatus].label}
                  </Badge>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56">
                <DropdownMenuLabel>
                  {t("attendance.export.filterByStatus")}
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuGroup>
                  {(Object.keys(statusInfo) as StatusFilter[]).map((status) => (
                    <DropdownMenuItem
                      key={status}
                      onClick={() => setSelectedStatus(status)}
                      className={cn(
                        "cursor-pointer",
                        selectedStatus === status && "bg-accent font-medium"
                      )}
                    >
                      <span className="mr-2">{statusInfo[status].icon}</span>
                      {statusInfo[status].label}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuGroup>
              </DropdownMenuContent>
            </DropdownMenu>

            <Button
              onClick={() => handleExport("csv")}
              variant="outline"
              size="sm"
              className="gap-2"
              disabled={recordsCount === 0}
            >
              <FileDown className="h-4 w-4" />
              CSV
            </Button>
            <Button
              onClick={() => handleExport("pdf")}
              size="sm"
              className="gap-2"
              disabled={recordsCount === 0}
            >
              <Download className="h-4 w-4" />
              PDF
            </Button>
          </div>
        </div>

        {dateRange.from && dateRange.to && (
          <div className="text-sm mt-4">
            {recordsCount > 0 ? (
              <div className="text-green-600 flex items-center gap-1">
                <Check className="h-4 w-4" />
                {t("attendance.export.recordsFound", { count: recordsCount })}
                {selectedStatus !== "all" && (
                  <span className="ml-1">
                    {t("attendance.export.withStatus")}{" "}
                    <Badge variant={selectedStatus as any}>
                      {statusInfo[selectedStatus].icon}{" "}
                      {t(`attendance.status.${selectedStatus}`)}
                    </Badge>
                  </span>
                )}
              </div>
            ) : records.length > 0 ? (
              <Alert variant="warning" className="py-2">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  {selectedStatus === "all"
                    ? t("attendance.export.noRecordsForPeriod")
                    : t("attendance.export.noStatusRecordsForPeriod", {
                        status: t(`attendance.status.${selectedStatus}`),
                      })}
                </AlertDescription>
              </Alert>
            ) : (
              <Alert variant="warning" className="py-2">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  {t("attendance.export.noRecordsAvailable")}
                </AlertDescription>
              </Alert>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
