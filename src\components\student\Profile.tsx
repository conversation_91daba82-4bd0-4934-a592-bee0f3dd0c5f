import { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
} from "@/components/ui/form";
import { useToast } from "@/hooks/use-toast";
import { Camera, Fingerprint, Save, School, Globe } from "lucide-react";
import { Student, Block, Room } from "@/lib/types";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { useAuth } from "@/context/AuthContext";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/lib/supabase";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { fetchSchools } from "@/lib/api/schools";
import { useTranslation } from "react-i18next";
import { useLanguage } from "@/context/SimpleLanguageContext";
import LanguageToggle from "@/components/shared/LanguageToggle";
import LanguageTest from "@/components/shared/LanguageTest";
import SimpleBiometricSettings from "@/components/profile/SimpleBiometricSettings";

const profileSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters." }),
  studentId: z.string().min(2, { message: "Student ID is required." }),
  course: z.string().min(1, { message: "Class/Grade is required." }),
  block_id: z.string().min(1, { message: "Block selection is required." }),
  room_id: z.string().min(1, { message: "Room selection is required." }),
  pin: z
    .string()
    .length(6, { message: "PIN must be 6 digits." })
    .regex(/^\d+$/, { message: "PIN must contain only numbers." }),
  school: z.string().min(1, { message: "School selection is required." }),
});

type ProfileFormValues = z.infer<typeof profileSchema>;

export default function StudentProfile({
  isSetupMode = false,
}: {
  isSetupMode?: boolean;
}) {
  const [isEditing, setIsEditing] = useState(isSetupMode);
  const [submitAttempted, setSubmitAttempted] = useState(false);
  const [uploadingPhoto, setUploadingPhoto] = useState(false);
  const [photoPreview, setPhotoPreview] = useState<string | null>(null);
  const [blocks, setBlocks] = useState<Block[]>([]);
  const [rooms, setRooms] = useState<Room[]>([]);
  const [schools, setSchools] = useState<School[]>([]);
  const [selectedBlock, setSelectedBlock] = useState<string | null>(null);
  const [loadingSchools, setLoadingSchools] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  const { profile, updateProfile } = useAuth();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { currentLanguage, changeLanguage } = useLanguage();

  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      name: "",
      studentId: "",
      course: "",
      block_id: "",
      room_id: "",
      pin: "",
      school: "",
    },
  });

  // Fetch schools from schools table
  useEffect(() => {
    const getSchools = async () => {
      setLoadingSchools(true);
      try {
        const schoolsList = await fetchSchools();
        setSchools(schoolsList);

        if (schoolsList.length === 0) {
          toast({
            title: "No schools found",
            description:
              "No schools have been added by administrators yet. Please contact your administrator to add your school.",
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error("Error fetching schools:", error);
        toast({
          title: "Error",
          description: "Failed to fetch schools",
          variant: "destructive",
        });
      } finally {
        setLoadingSchools(false);
      }
    };

    getSchools();
  }, []);

  // Fetch blocks and rooms
  useEffect(() => {
    const fetchBlocks = async () => {
      try {
        const { data, error } = await supabase
          .from("blocks")
          .select("*")
          .order("name");

        if (error) throw error;
        setBlocks(data || []);
      } catch (error) {
        console.error("Error fetching blocks:", error);
        toast({
          title: "Error",
          description: "Failed to fetch blocks",
          variant: "destructive",
        });
      }
    };

    fetchBlocks();
  }, []);

  // Fetch rooms when block is selected
  useEffect(() => {
    const fetchRooms = async () => {
      if (!selectedBlock) {
        setRooms([]);
        return;
      }

      try {
        const { data, error } = await supabase
          .from("rooms")
          .select("*")
          .eq("block_id", selectedBlock)
          .order("name");

        if (error) throw error;
        setRooms(data || []);
      } catch (error) {
        console.error("Error fetching rooms:", error);
        toast({
          title: "Error",
          description: "Failed to fetch rooms",
          variant: "destructive",
        });
      }
    };

    fetchRooms();
  }, [selectedBlock]);

  useEffect(() => {
    // Always set to editing mode if in setup mode
    if (isSetupMode) {
      setIsEditing(true);
    }
  }, [isSetupMode]);

  useEffect(() => {
    if (profile && profile.role === "student") {
      const studentProfile = profile as Student;

      // Update form values
      form.reset({
        name: studentProfile.name || "",
        studentId: studentProfile.studentId || "",
        course: studentProfile.course || "",
        block_id: studentProfile.block_id || "",
        room_id: studentProfile.room_id || "",
        pin: studentProfile.pin || "",
        school: studentProfile.school || "",
      });

      // Set selected block for room filtering
      setSelectedBlock(studentProfile.block_id || null);

      // Set photo preview if exists
      if (studentProfile.photoUrl) {
        setPhotoPreview(studentProfile.photoUrl);
      }
    }
  }, [profile, form]);

  const handlePhotoClick = () => {
    fileInputRef.current?.click();
  };

  const handlePhotoChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file || !profile) return;

    try {
      setUploadingPhoto(true);

      // Create a temporary preview
      const objectUrl = URL.createObjectURL(file);
      setPhotoPreview(objectUrl);

      // Upload to Supabase Storage
      const fileExt = file.name.split(".").pop();
      const fileName = `${profile.id}-${Math.random()
        .toString(36)
        .substring(2)}.${fileExt}`;
      const filePath = `students/${fileName}`;

      // Upload the file to the images bucket
      const { error: uploadError, data: uploadData } = await supabase.storage
        .from("images")
        .upload(filePath, file);

      if (uploadError) {
        throw uploadError;
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from("images")
        .getPublicUrl(filePath);

      // Save URL to profile
      await updateProfile({
        photoUrl: urlData.publicUrl,
      });

      toast({
        title: "Photo uploaded",
        description: "Your profile photo has been updated.",
      });
    } catch (error: any) {
      console.error("Photo upload error:", error);
      toast({
        title: "Upload failed",
        description:
          error.message || "Failed to upload photo. Please try again.",
        variant: "destructive",
      });

      // Reset preview on error if no previous photo
      if (!profile?.photoUrl) {
        setPhotoPreview(null);
      }
    } finally {
      setUploadingPhoto(false);
    }
  };

  const onSubmit = async (data: ProfileFormValues) => {
    if (!profile) return;

    setSubmitAttempted(true);

    try {
      // Get block and room names for display
      const selectedBlock = blocks.find((b) => b.id === data.block_id);
      const selectedRoom = rooms.find((r) => r.id === data.room_id);

      // Get selected school name for display
      const selectedSchool = schools.find((s) => s.id === data.school);

      await updateProfile({
        name: data.name,
        studentId: data.studentId,
        course: data.course,
        block_id: data.block_id,
        room_id: data.room_id,
        blockName: selectedBlock?.name || "",
        roomNumber: selectedRoom?.name || "",
        pin: data.pin,
        school_id: data.school,
        school: data.school, // For backward compatibility
        schoolName: selectedSchool?.name || "",
      });

      if (!isSetupMode) {
        setIsEditing(false);
      }

      // If in setup mode, show success toast and redirect to dashboard
      if (isSetupMode) {
        toast({
          title: "Profile setup complete",
          description:
            "Your profile has been successfully set up. You can now access all features.",
        });
        setTimeout(() => navigate("/student"), 500);
      }
    } catch (error) {
      console.error("Error updating profile:", error);
      toast({
        title: "Error",
        description: "Failed to update profile. Please try again.",
        variant: "destructive",
      });
    } finally {
      setSubmitAttempted(false);
    }
  };

  if (!profile || profile.role !== "student") {
    return (
      <Card className="w-full max-w-3xl mx-auto">
        <CardContent className="p-6">
          <div className="flex justify-center items-center h-40">
            <p>Loading profile...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const studentProfile = profile as Student;

  // Check if required fields are missing for setup mode
  const isMissingRequiredFields =
    isSetupMode &&
    (!studentProfile.course ||
      !studentProfile.block_id ||
      !studentProfile.room_id ||
      !studentProfile.pin ||
      !studentProfile.school);

  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardHeader>
        <CardTitle>
          {isSetupMode
            ? t("profile.completeYourProfile")
            : t("profile.studentProfile")}
        </CardTitle>
        <CardDescription>
          {isEditing
            ? t("profile.updateYourInformation")
            : isSetupMode
            ? t("profile.completeProfileMessage")
            : t("profile.viewAndManage")}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {isMissingRequiredFields && (
          <Alert className="mb-4 bg-yellow-50 border-yellow-200">
            <AlertCircle className="h-4 w-4 text-amber-800" />
            <AlertDescription className="text-amber-800">
              {t("profile.fillRequiredFields")}
            </AlertDescription>
          </Alert>
        )}

        {/* Hidden file input */}
        <input
          type="file"
          ref={fileInputRef}
          onChange={handlePhotoChange}
          className="hidden"
          accept="image/*"
        />

        {isEditing ? (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <div className="flex flex-col sm:flex-row items-center gap-4 mb-6">
                <Avatar
                  className="w-24 h-24 border-4 border-primary/20 cursor-pointer relative"
                  onClick={handlePhotoClick}
                >
                  {photoPreview || studentProfile.photoUrl ? (
                    <AvatarImage
                      src={photoPreview || studentProfile.photoUrl}
                      alt={studentProfile.name}
                    />
                  ) : (
                    <AvatarFallback className="bg-secondary/30 flex items-center justify-center">
                      <Camera size={24} className="text-gray-400" />
                    </AvatarFallback>
                  )}
                </Avatar>
                <div>
                  <Button
                    type="button"
                    variant="outline"
                    className="flex items-center gap-2"
                    onClick={handlePhotoClick}
                    disabled={uploadingPhoto}
                  >
                    <Camera size={16} />{" "}
                    {uploadingPhoto
                      ? t("profile.uploading")
                      : t("profile.changePhoto")}
                  </Button>
                </div>
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("profile.fullName")}</FormLabel>
                      <FormControl>
                        <Input
                          placeholder={t("profile.enterFullName")}
                          {...field}
                          disabled={!isEditing}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="studentId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("profile.studentId")}</FormLabel>
                      <FormControl>
                        <Input
                          placeholder={t("profile.enterStudentId")}
                          {...field}
                          disabled={!isEditing}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="course"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("profile.course")}</FormLabel>
                      <FormControl>
                        <Input
                          placeholder={t("profile.enterCourse")}
                          {...field}
                          disabled={!isEditing}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="block_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("profile.block")}</FormLabel>
                      <Select
                        disabled={!isEditing}
                        value={field.value}
                        onValueChange={(value) => {
                          field.onChange(value);
                          setSelectedBlock(value);
                          // Reset room selection when block changes
                          form.setValue("room_id", "");
                        }}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue
                              placeholder={t("profile.selectBlock")}
                            />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {blocks.map((block) => (
                            <SelectItem key={block.id} value={block.id}>
                              {t("profile.block")} {block.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="room_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("profile.room")}</FormLabel>
                      <Select
                        disabled={!isEditing || !selectedBlock}
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue
                              placeholder={t("profile.selectRoom")}
                            />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {rooms.map((room) => (
                            <SelectItem key={room.id} value={room.id}>
                              {t("profile.room")} {room.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="pin"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("profile.pin")}</FormLabel>
                      <FormControl>
                        <Input
                          type="password"
                          placeholder={t("profile.enterPin")}
                          maxLength={6}
                          {...field}
                          disabled={!isEditing}
                        />
                      </FormControl>
                      <FormDescription>
                        {t("profile.pinDescription")}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="school"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <School className="h-4 w-4" /> {t("profile.school")}
                      </FormLabel>
                      <Select
                        disabled={
                          !isEditing || loadingSchools || schools.length === 0
                        }
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue
                              placeholder={
                                loadingSchools
                                  ? t("profile.loadingSchools")
                                  : schools.length === 0
                                  ? t("profile.noSchoolsAvailable")
                                  : t("profile.selectSchool")
                              }
                            />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {schools.map((school) => (
                            <SelectItem key={school.id} value={school.id}>
                              {school.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        {t("profile.schoolDescription")}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="pt-4 flex justify-end gap-2">
                {!isSetupMode && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsEditing(false)}
                  >
                    {t("common.cancel")}
                  </Button>
                )}
                <Button
                  type="submit"
                  className="gap-2"
                  disabled={submitAttempted}
                >
                  <Save size={16} />
                  {submitAttempted
                    ? t("profile.saving")
                    : isSetupMode
                    ? t("profile.completeSetup")
                    : t("profile.save")}
                </Button>
              </div>
            </form>
          </Form>
        ) : (
          <>
            <div className="flex flex-col sm:flex-row items-center sm:items-start gap-6">
              <Avatar
                className="w-24 h-24 border-4 border-primary/20 cursor-pointer"
                onClick={handlePhotoClick}
              >
                {studentProfile.photoUrl ? (
                  <AvatarImage
                    src={studentProfile.photoUrl}
                    alt={studentProfile.name}
                  />
                ) : (
                  <AvatarFallback className="bg-secondary/30 flex items-center justify-center">
                    <Camera size={24} className="text-gray-400" />
                  </AvatarFallback>
                )}
              </Avatar>

              <div className="space-y-4 flex-1">
                <div className="space-y-1">
                  <p className="text-2xl font-semibold">
                    {studentProfile.name}
                  </p>
                  <p className="text-muted-foreground">
                    {t("profile.studentId")}: {studentProfile.studentId}
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="text-muted-foreground">
                      {t("profile.course")}
                    </Label>
                    <p>{studentProfile.course || "-"}</p>
                  </div>
                  <div>
                    <Label className="text-muted-foreground">
                      {t("profile.email")}
                    </Label>
                    <p>{studentProfile.email}</p>
                  </div>
                  <div>
                    <Label className="text-muted-foreground">
                      {t("profile.block")}
                    </Label>
                    <p>{studentProfile.blockName || "-"}</p>
                  </div>
                  <div>
                    <Label className="text-muted-foreground">
                      {t("profile.room")}
                    </Label>
                    <p>{studentProfile.roomNumber || "-"}</p>
                  </div>
                  <div>
                    <Label className="text-muted-foreground">
                      {t("profile.school")}
                    </Label>
                    <p>{studentProfile.schoolName || "-"}</p>
                  </div>
                </div>

                {/* Language Settings Section */}
                <div className="mt-6 border-t pt-4">
                  <div className="flex items-center gap-2 mb-3">
                    <Globe size={18} className="text-primary" />
                    <h3 className="text-lg font-medium">
                      {t("settings.languageSettings")}
                    </h3>
                  </div>
                  <LanguageToggle variant="default" showLabel={true} />

                  {/* Language Test Component */}
                  <LanguageTest />
                </div>
              </div>
            </div>

            {/* Biometric Authentication Settings */}
            <div className="border-t pt-6">
              <SimpleBiometricSettings />
            </div>

            <div className="border-t pt-4 flex justify-end">
              <Button onClick={() => setIsEditing(true)}>
                {t("profile.edit")}
              </Button>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}
