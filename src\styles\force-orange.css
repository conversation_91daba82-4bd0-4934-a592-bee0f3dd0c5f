/* Force orange color in dark mode */

/* Direct element overrides */
.dark nav,
.dark header,
.dark .bg-primary,
.dark [class*="dark:bg-[#ff"],
html.dark nav,
html.dark header {
  background-color: #f39228 !important;
}

/* Button overrides */
.dark button.bg-primary,
.dark .bg-primary button,
.dark [class*="dark:bg-[#ff"] {
  background-color: #f39228 !important;
}

/* Text color overrides */
.dark .text-primary,
.dark [class*="dark:text-[#ff"] {
  color: #f39228 !important;
}

/* Border color overrides */
.dark .border-primary,
.dark [class*="dark:border-[#ff"] {
  border-color: #f39228 !important;
}

/* Hover state overrides */
.dark .hover\:bg-primary:hover,
.dark [class*="dark:hover:bg-[#ff"]:hover {
  background-color: #f5a54d !important;
}

.dark .hover\:text-primary:hover,
.dark [class*="dark:hover:text-[#ff"]:hover {
  color: #f5a54d !important;
}

.dark .hover\:border-primary:hover,
.dark [class*="dark:hover:border-[#ff"]:hover {
  border-color: #f5a54d !important;
}

/* CSS variable overrides */
.dark {
  --primary: 32 89% 56% !important;
  --primary-foreground: 0 0% 100% !important;
  --navbar-bg: #f39228 !important;
}

/* Force specific components */
.dark section.bg-primary {
  background-color: #f39228 !important;
}

/* Force specific selectors that might be using the old color */
.dark [style*="background-color: #ff0055"],
.dark [style*="background-color: #ff004f"],
.dark [style*="background-color: #ff3373"] {
  background-color: #f39228 !important;
}

.dark [style*="color: #ff0055"],
.dark [style*="color: #ff004f"],
.dark [style*="color: #ff3373"] {
  color: #f39228 !important;
}

.dark [style*="border-color: #ff0055"],
.dark [style*="border-color: #ff004f"],
.dark [style*="border-color: #ff3373"] {
  border-color: #f39228 !important;
}
