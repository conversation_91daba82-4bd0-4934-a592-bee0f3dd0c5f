import CryptoJS from 'crypto-js';

// Secret key for QR code signing (in production, this should be from environment variables)
const QR_SECRET_KEY = process.env.NEXT_PUBLIC_QR_SECRET_KEY || 'default-secret-key-change-in-production';

export interface QRCodePayload {
  room_id: string;
  session_id: string;
  timestamp: string;
  expires_at: string;
  school_id: string;
  block_id: string;
  nonce: string;
}

export interface SignedQRData extends QRCodePayload {
  signature: string;
}

/**
 * Generate a cryptographically secure QR code payload
 */
export function generateSecureQRCode(
  roomId: string,
  schoolId: string,
  blockId: string,
  expiryMinutes: number = 5
): SignedQRData {
  const now = new Date();
  const expiryTime = new Date(now.getTime() + expiryMinutes * 60 * 1000);
  
  // Generate unique session ID and nonce
  const sessionId = crypto.randomUUID();
  const nonce = generateNonce();
  
  const payload: QRCodePayload = {
    room_id: roomId,
    session_id: sessionId,
    timestamp: now.toISOString(),
    expires_at: expiryTime.toISOString(),
    school_id: schoolId,
    block_id: blockId,
    nonce: nonce,
  };
  
  // Create signature
  const signature = signPayload(payload);
  
  return {
    ...payload,
    signature,
  };
}

/**
 * Verify QR code signature and validity
 */
export function verifyQRCode(qrData: SignedQRData): {
  isValid: boolean;
  error?: string;
} {
  try {
    // Extract signature and payload
    const { signature, ...payload } = qrData;
    
    // Verify signature
    const expectedSignature = signPayload(payload);
    if (signature !== expectedSignature) {
      return {
        isValid: false,
        error: 'Invalid signature - QR code may be tampered with',
      };
    }
    
    // Check expiry
    const expiryTime = new Date(payload.expires_at);
    if (expiryTime.getTime() <= Date.now()) {
      return {
        isValid: false,
        error: 'QR code has expired',
      };
    }
    
    // Check timestamp is not too old (prevent replay attacks)
    const createdTime = new Date(payload.timestamp);
    const maxAge = 10 * 60 * 1000; // 10 minutes max age
    if (Date.now() - createdTime.getTime() > maxAge) {
      return {
        isValid: false,
        error: 'QR code is too old',
      };
    }
    
    // Check timestamp is not in the future (prevent time manipulation)
    if (createdTime.getTime() > Date.now() + 60000) { // Allow 1 minute clock skew
      return {
        isValid: false,
        error: 'QR code timestamp is in the future',
      };
    }
    
    return { isValid: true };
    
  } catch (error) {
    return {
      isValid: false,
      error: 'Failed to verify QR code',
    };
  }
}

/**
 * Create HMAC signature for payload
 */
function signPayload(payload: QRCodePayload): string {
  // Create canonical string representation
  const canonicalString = [
    payload.room_id,
    payload.session_id,
    payload.timestamp,
    payload.expires_at,
    payload.school_id,
    payload.block_id,
    payload.nonce,
  ].join('|');
  
  // Create HMAC-SHA256 signature
  const signature = CryptoJS.HmacSHA256(canonicalString, QR_SECRET_KEY);
  return signature.toString(CryptoJS.enc.Hex);
}

/**
 * Generate cryptographically secure nonce
 */
function generateNonce(): string {
  const array = new Uint8Array(16);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
}

/**
 * Encrypt QR code data (optional additional security layer)
 */
export function encryptQRData(data: SignedQRData): string {
  const jsonString = JSON.stringify(data);
  const encrypted = CryptoJS.AES.encrypt(jsonString, QR_SECRET_KEY);
  return encrypted.toString();
}

/**
 * Decrypt QR code data
 */
export function decryptQRData(encryptedData: string): SignedQRData | null {
  try {
    const decrypted = CryptoJS.AES.decrypt(encryptedData, QR_SECRET_KEY);
    const jsonString = decrypted.toString(CryptoJS.enc.Utf8);
    return JSON.parse(jsonString);
  } catch (error) {
    console.error('Failed to decrypt QR data:', error);
    return null;
  }
}

/**
 * Validate room assignment for student
 */
export function validateRoomAssignment(
  qrData: SignedQRData,
  studentRoomId: string | null,
  studentBlockId: string | null
): {
  isValid: boolean;
  level: 'perfect' | 'warning' | 'error';
  message: string;
} {
  if (studentRoomId === qrData.room_id) {
    return {
      isValid: true,
      level: 'perfect',
      message: 'Perfect match - you are in your assigned room',
    };
  }
  
  if (studentBlockId === qrData.block_id) {
    return {
      isValid: true,
      level: 'warning',
      message: 'You are in the correct block but not your assigned room',
    };
  }
  
  return {
    isValid: false,
    level: 'error',
    message: 'You are not in your assigned block or room',
  };
}

/**
 * Check for replay attacks by tracking used session IDs
 */
export async function checkReplayAttack(
  sessionId: string,
  studentId: string
): Promise<boolean> {
  // In a real implementation, this would check a database or cache
  // to see if this session ID has already been used by this student
  
  // For now, we'll implement a simple in-memory cache
  // In production, use Redis or database storage
  
  const cacheKey = `qr_session_${sessionId}_${studentId}`;
  const cached = sessionStorage.getItem(cacheKey);
  
  if (cached) {
    return true; // Replay attack detected
  }
  
  // Mark session as used
  sessionStorage.setItem(cacheKey, Date.now().toString());
  
  // Clean up old entries (optional)
  cleanupOldSessions();
  
  return false;
}

/**
 * Clean up old session entries from cache
 */
function cleanupOldSessions(): void {
  const maxAge = 24 * 60 * 60 * 1000; // 24 hours
  const now = Date.now();
  
  for (let i = 0; i < sessionStorage.length; i++) {
    const key = sessionStorage.key(i);
    if (key && key.startsWith('qr_session_')) {
      const timestamp = parseInt(sessionStorage.getItem(key) || '0');
      if (now - timestamp > maxAge) {
        sessionStorage.removeItem(key);
      }
    }
  }
}

/**
 * Generate device fingerprint for additional security
 */
export function generateDeviceFingerprint(): string {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  if (ctx) {
    ctx.textBaseline = 'top';
    ctx.font = '14px Arial';
    ctx.fillText('Device fingerprint', 2, 2);
  }
  
  const fingerprint = [
    navigator.userAgent,
    navigator.language,
    screen.width + 'x' + screen.height,
    new Date().getTimezoneOffset(),
    canvas.toDataURL(),
    navigator.hardwareConcurrency || 0,
    navigator.deviceMemory || 0,
  ].join('|');
  
  return CryptoJS.SHA256(fingerprint).toString(CryptoJS.enc.Hex);
}

/**
 * Validate location if GPS coordinates are provided
 */
export function validateLocation(
  currentLat: number,
  currentLng: number,
  roomLat: number,
  roomLng: number,
  maxDistanceMeters: number = 100
): {
  isValid: boolean;
  distance: number;
  message: string;
} {
  const distance = calculateDistance(currentLat, currentLng, roomLat, roomLng);
  
  if (distance <= maxDistanceMeters) {
    return {
      isValid: true,
      distance,
      message: `Location verified (${Math.round(distance)}m from room)`,
    };
  }
  
  return {
    isValid: false,
    distance,
    message: `Too far from room (${Math.round(distance)}m away, max ${maxDistanceMeters}m)`,
  };
}

/**
 * Calculate distance between two GPS coordinates using Haversine formula
 */
function calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
  const R = 6371e3; // Earth's radius in meters
  const φ1 = lat1 * Math.PI / 180;
  const φ2 = lat2 * Math.PI / 180;
  const Δφ = (lat2 - lat1) * Math.PI / 180;
  const Δλ = (lng2 - lng1) * Math.PI / 180;

  const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
          Math.cos(φ1) * Math.cos(φ2) *
          Math.sin(Δλ/2) * Math.sin(Δλ/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

  return R * c; // Distance in meters
}
