import { UAParser } from "ua-parser-js";
import { supabase } from "@/lib/supabase";

interface DeviceInfo {
  browser: string;
  os: string;
  device: string;
  userAgent: string;
}

export function getDeviceFingerprint(): DeviceInfo {
  const parser = new UAParser();
  const result = parser.getResult();

  return {
    browser: `${result.browser.name} ${result.browser.version}`,
    os: `${result.os.name} ${result.os.version}`,
    device: result.device.model || "Unknown",
    userAgent: navigator.userAgent,
  };
}

export async function isLocationSpoofed(
  latitude: number,
  longitude: number
): Promise<boolean> {
  // Check if location accuracy is suspiciously perfect
  if (Number.isInteger(latitude) && Number.isInteger(longitude)) {
    return true;
  }

  // Check if location is obviously invalid
  if (Math.abs(latitude) > 90 || Math.abs(longitude) > 180) {
    return true;
  }

  return false;
}

export async function checkConcurrentSessions(
  studentId: string,
  roomId?: string
): Promise<boolean> {
  const fiveMinutesAgo = new Date();
  fiveMinutesAgo.setMinutes(fiveMinutesAgo.getMinutes() - 5);

  // Build the query
  let query = supabase
    .from("attendance_records")
    .select("*")
    .eq("student_id", studentId)
    .gte("timestamp", fiveMinutesAgo.toISOString());

  // If roomId is provided, check for records in different rooms
  if (roomId) {
    query = query.neq("room_id", roomId);
  }

  // Execute the query
  const { data: recentRecords, error } = await query;

  if (error) {
    console.error("Error checking concurrent sessions:", error);
    return false;
  }

  return recentRecords && recentRecords.length > 0;
}

export async function isDeviceConsistent(
  studentId: string,
  currentDevice: DeviceInfo
): Promise<boolean> {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  // Get previous attendance records from today
  const { data: records, error } = await supabase
    .from("attendance_records")
    .select("device_info")
    .eq("student_id", studentId)
    .gte("timestamp", today.toISOString())
    .order("timestamp", { ascending: false })
    .limit(1);

  if (error || !records || records.length === 0) {
    return true; // No previous records today, so consider it consistent
  }

  try {
    // Handle both string and object device_info
    let previousDevice: any;

    if (typeof records[0].device_info === "string") {
      try {
        // Try to parse as JSON
        previousDevice = JSON.parse(records[0].device_info);
      } catch (parseError) {
        // If it's not valid JSON, just use the string for comparison
        console.log("Device info is not JSON, using string comparison");
        return records[0].device_info === currentDevice;
      }
    } else {
      previousDevice = records[0].device_info;
    }

    // If we have a parsed object, compare critical properties
    if (typeof previousDevice === "object" && previousDevice !== null) {
      // Check if it has the expected properties
      if (
        "os" in previousDevice &&
        "browser" in previousDevice &&
        "device" in previousDevice
      ) {
        return (
          previousDevice.os === currentDevice.os &&
          previousDevice.browser === currentDevice.browser &&
          previousDevice.device === currentDevice.device
        );
      }
    }

    // If we can't do a structured comparison, do a simple string comparison
    return String(records[0].device_info) === String(currentDevice);
  } catch (error) {
    console.error("Error comparing device info:", error);
    return true; // If we can't compare the device info, assume it's consistent
  }
}

export async function hasReachedRateLimit(
  studentId: string,
  roomId?: string
): Promise<boolean> {
  const fiveMinutesAgo = new Date();
  fiveMinutesAgo.setMinutes(fiveMinutesAgo.getMinutes() - 5);

  // Build the query
  let query = supabase
    .from("attendance_records")
    .select("*")
    .eq("student_id", studentId)
    .gte("timestamp", fiveMinutesAgo.toISOString());

  // If roomId is provided, filter by room
  if (roomId) {
    query = query.eq("room_id", roomId);
  }

  // Execute the query
  const { data: recentAttempts, error } = await query;

  if (error) {
    console.error("Error checking rate limit:", error);
    return false;
  }

  // Limit to 5 attempts per 5 minutes
  return recentAttempts && recentAttempts.length >= 5;
}

// Calculate distance between two points using Haversine formula
export function calculateDistance(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number {
  const R = 6371e3; // Earth's radius in meters
  const φ1 = (lat1 * Math.PI) / 180;
  const φ2 = (lat2 * Math.PI) / 180;
  const Δφ = ((lat2 - lat1) * Math.PI) / 180;
  const Δλ = ((lon2 - lon1) * Math.PI) / 180;

  const a =
    Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
    Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  return R * c; // Distance in meters
}

export async function createLocationAlert(
  studentId: string,
  studentLat: number,
  studentLng: number,
  roomLat: number,
  roomLng: number,
  distance: number,
  roomName: string
): Promise<void> {
  try {
    // First create a notification for the teacher
    const { error: notificationError } = await supabase
      .from("notifications")
      .insert({
        student_id: studentId,
        title: "⚠️ Location Alert",
        message: `Student is ${Math.round(
          distance
        )}m away from ${roomName}, outside the allowed radius.`,
        type: "location_alert",
        read: false,
        timestamp: new Date().toISOString(),
        metadata: JSON.stringify({
          student_location: { latitude: studentLat, longitude: studentLng },
          room_location: { latitude: roomLat, longitude: roomLng },
          distance_meters: Math.round(distance),
          room_name: roomName,
        }),
      });

    if (notificationError) {
      console.error(
        "Error creating location alert notification:",
        notificationError
      );
    }

    // Then create a detailed alert record
    const { error } = await supabase.from("attendance_alerts").insert([
      {
        student_id: studentId,
        alert_type: "location_mismatch",
        distance_meters: Math.round(distance),
        student_location: { latitude: studentLat, longitude: studentLng },
        room_location: {
          latitude: roomLat,
          longitude: roomLng,
          name: roomName,
        },
        status: "pending",
        created_at: new Date().toISOString(),
        timestamp: new Date().toISOString(),
      },
    ]);

    if (error) {
      console.error("Error creating location alert record:", error);
    }
  } catch (error) {
    console.error("Error in createLocationAlert:", error);
  }
}
