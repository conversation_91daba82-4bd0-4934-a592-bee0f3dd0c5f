import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { ParentContact } from "@/lib/types/parent-contact";
import { notifyParents } from "@/lib/services/notification-service";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { SendHorizonal, AlertCircle } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

interface TestNotificationDialogProps {
  studentId: string;
  studentName: string;
  parentContacts: ParentContact[];
}

export default function TestNotificationDialog({
  studentId,
  studentName,
  parentContacts,
}: TestNotificationDialogProps) {
  const [open, setOpen] = useState(false);
  const [sending, setSending] = useState(false);
  const [notificationType, setNotificationType] = useState<"test" | "excuse_new" | "excuse_approved" | "excuse_rejected">("test");
  const [customMessage, setCustomMessage] = useState("");
  const { toast } = useToast();

  const handleSendTest = async () => {
    if (!studentId) return;
    
    try {
      setSending(true);
      
      // Prepare notification data
      const notificationData = {
        subject: getSubjectForType(notificationType, studentName),
        message: customMessage || getMessageForType(notificationType, studentName),
        studentName,
        excuseId: "test-notification",
        startDate: new Date().toISOString(),
        endDate: new Date(Date.now() + 86400000).toISOString(), // Tomorrow
        reason: notificationType === "test" ? "Test notification" : "Sample excuse reason",
      };
      
      // Send notification
      const result = await notifyParents(studentId, notificationData);
      
      if (result.success) {
        toast({
          title: "Test Notification Sent",
          description: `Successfully sent test notification to parent(s) of ${studentName}`,
        });
        setOpen(false);
      } else {
        toast({
          title: "Notification Failed",
          description: result.message,
          variant: "destructive",
        });
      }
    } catch (error: any) {
      console.error("Error sending test notification:", error);
      toast({
        title: "Error",
        description: `Failed to send test notification: ${error.message}`,
        variant: "destructive",
      });
    } finally {
      setSending(false);
    }
  };
  
  const getSubjectForType = (type: string, name: string): string => {
    switch (type) {
      case "test":
        return `Test Notification for ${name}'s Parent`;
      case "excuse_new":
        return `New Absence Request from ${name}`;
      case "excuse_approved":
        return `Absence Request Approved for ${name}`;
      case "excuse_rejected":
        return `Absence Request Rejected for ${name}`;
      default:
        return `Notification for ${name}'s Parent`;
    }
  };
  
  const getMessageForType = (type: string, name: string): string => {
    switch (type) {
      case "test":
        return `This is a test notification for ${name}'s parent. If you received this message, the notification system is working correctly.`;
      case "excuse_new":
        return `Your child, ${name}, has submitted a request for absence from school. This request is pending approval from school administration.`;
      case "excuse_approved":
        return `Your child's (${name}) absence request has been APPROVED by the school administration.`;
      case "excuse_rejected":
        return `Your child's (${name}) absence request has been REJECTED by the school administration. Please contact the school for more information.`;
      default:
        return `This is a notification regarding your child, ${name}.`;
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="ml-2">
          <SendHorizonal className="mr-2 h-4 w-4" />
          Test Notification
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Send Test Notification</DialogTitle>
          <DialogDescription>
            Send a test notification to verify parent contact information
          </DialogDescription>
        </DialogHeader>
        
        {parentContacts.length === 0 ? (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>No Parent Contacts</AlertTitle>
            <AlertDescription>
              There are no parent contacts configured for this student. Please add at least one parent contact before sending test notifications.
            </AlertDescription>
          </Alert>
        ) : (
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="notification-type">Notification Type</Label>
              <Select
                value={notificationType}
                onValueChange={(value: any) => setNotificationType(value)}
              >
                <SelectTrigger id="notification-type">
                  <SelectValue placeholder="Select notification type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="test">Test Message</SelectItem>
                  <SelectItem value="excuse_new">New Excuse Request</SelectItem>
                  <SelectItem value="excuse_approved">Excuse Approved</SelectItem>
                  <SelectItem value="excuse_rejected">Excuse Rejected</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="custom-message">Custom Message (Optional)</Label>
              <Textarea
                id="custom-message"
                placeholder="Enter a custom message or leave blank to use the default template"
                value={customMessage}
                onChange={(e) => setCustomMessage(e.target.value)}
                className="min-h-[100px]"
              />
              <p className="text-xs text-muted-foreground">
                Leave blank to use the default template for the selected notification type
              </p>
            </div>
            
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Notification Recipients</AlertTitle>
              <AlertDescription>
                <p className="mb-2">This test will be sent to the following contacts:</p>
                <ul className="list-disc pl-5 space-y-1">
                  {parentContacts.map((contact) => (
                    <li key={contact.id}>
                      {contact.parent_name} via {contact.notification_method === "email" 
                        ? `Email (${contact.email})` 
                        : contact.notification_method === "sms" 
                          ? `SMS (${contact.phone})` 
                          : contact.notification_method === "both" 
                            ? `Email & SMS (${contact.email}, ${contact.phone})` 
                            : "No notifications"}
                    </li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          </div>
        )}
        
        <DialogFooter>
          <Button variant="outline" onClick={() => setOpen(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleSendTest} 
            disabled={sending || parentContacts.length === 0}
          >
            {sending ? "Sending..." : "Send Test Notification"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
