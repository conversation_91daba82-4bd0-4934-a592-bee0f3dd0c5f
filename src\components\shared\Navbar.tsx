import { Link, useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { Fingerprint, LogOut, User, ChevronDown } from "lucide-react";
import { useAuth } from "@/context/AuthContext";
import { useSchool } from "@/context/SchoolContext";
import { useSchoolTheme } from "@/components/providers/SchoolThemeProvider";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import SchoolSelector from "@/components/admin/SchoolSelector";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useTranslation } from "react-i18next";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useTab } from "@/context/TabContext";

export default function Navbar() {
  const { profile, signOut, loading } = useAuth();
  const { currentSchool } = useSchool();
  const { logoUrl } = useSchoolTheme();
  const { toast } = useToast();
  const navigate = useNavigate();
  const { t } = useTranslation();

  // Try to use the tab context if available, but don't fail if it's not
  let tabContext;
  try {
    tabContext = useTab();
  } catch (error) {
    // Tab context might not be available in all routes
    tabContext = null;
  }

  // Helper function to navigate to a specific tab
  const navigateToTab = (tabName: string) => {
    const baseUrl = `/${profile.role}`;

    // Update localStorage to persist the tab selection
    if (profile.role === "teacher") {
      localStorage.setItem("teacherActiveTab", tabName);
    } else if (profile.role === "student") {
      localStorage.setItem("studentActiveTab", tabName);
    } else if (profile.role === "admin") {
      localStorage.setItem("adminActiveTab", tabName);
    } else if (profile.role === "system-admin") {
      localStorage.setItem("systemAdminActiveTab", tabName);
    }

    // Try to use the tab context if available
    if (tabContext && tabContext.setActiveTab) {
      tabContext.setActiveTab(tabName);
    }

    // Navigate to the base URL with the tab parameter
    navigate(`${baseUrl}?tab=${tabName}`);
  };

  const handleLogout = async () => {
    try {
      await signOut();
    } catch (error) {
      toast.translateError(t, "common.error", "auth.logoutError");
    }
  };

  return (
    <nav className="bg-primary text-white shadow-md dark:bg-[#F39228]">
      <div className="container mx-auto px-4 py-3 flex justify-between items-center">
        <div className="flex items-center space-x-3">
          <div className="relative flex items-center justify-center school-logo-container">
            <div className="h-10 w-10 rounded-full bg-primary-foreground/10 flex items-center justify-center border-2 border-white/20 hover:border-white/40 transition-all school-logo-fallback pulse-animation">
              <Fingerprint className="h-6 w-6 text-white" />
            </div>
          </div>
          <Link to="/" className="text-xl font-bold uppercase tracking-wide">
            {t("app.name")}
          </Link>
        </div>

        <div className="flex items-center space-x-4">
          {/* Theme Toggle */}
          <ThemeToggle />

          {loading ? (
            <span className="text-sm">{t("common.loading")}</span>
          ) : profile ? (
            <>
              {/* School Selector removed as it's redundant with the school lists */}

              {/* Welcome message - Moved to where school name was */}
              <span className="hidden md:inline text-sm bg-primary-foreground/10 px-2 py-1 rounded uppercase font-medium tracking-wide">
                {t("common.welcome")}, {profile.name}
              </span>

              {/* School name - Moved to where welcome message was */}
              {currentSchool && (
                <span className="hidden lg:inline uppercase font-medium tracking-wide">
                  {currentSchool.name}
                </span>
              )}

              {/* School Logo with Dropdown Menu */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <div className="relative flex items-center justify-center school-logo-container cursor-pointer">
                    {logoUrl ? (
                      <Avatar className="h-10 w-10 border-2 border-white/20 hover:border-white/40 transition-all pulse-animation">
                        <AvatarImage
                          src={logoUrl}
                          alt={currentSchool?.name || "School logo"}
                          className="school-logo navbar-school-logo"
                        />
                        <AvatarFallback className="bg-primary-foreground/10 text-white school-logo-fallback">
                          {currentSchool?.name?.substring(0, 2) || "SL"}
                        </AvatarFallback>
                      </Avatar>
                    ) : (
                      <div className="h-10 w-10 rounded-full bg-primary-foreground/10 flex items-center justify-center border-2 border-white/20 hover:border-white/40 transition-all school-logo-fallback">
                        <Fingerprint className="h-6 w-6 text-white" />
                      </div>
                    )}
                  </div>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuLabel className="uppercase font-medium">
                    {currentSchool?.name || t("common.school")} - {profile.name}
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={() => {
                      // Determine the correct tab for dashboard based on user role
                      let tabName = "dashboard";

                      // For some roles, the dashboard tab might have a different name
                      if (profile.role === "student") {
                        tabName = "scan"; // Student dashboard default tab is "scan"
                      } else if (profile.role === "admin") {
                        tabName = "users"; // Admin dashboard default tab is "users"
                      } else if (profile.role === "system-admin") {
                        tabName = "overview"; // System admin dashboard default tab is "overview"
                      }

                      // Use the helper function to navigate
                      navigateToTab(tabName);
                    }}
                    className="cursor-pointer w-full"
                  >
                    {t("common.dashboard")}
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => {
                      // Determine the correct tab based on user role
                      let tabName = "profile";

                      // For system admin, the tab is called "settings" instead of "profile"
                      if (profile.role === "system-admin") {
                        tabName = "settings";
                      }

                      // Use the helper function to navigate
                      navigateToTab(tabName);
                    }}
                    className="cursor-pointer w-full"
                  >
                    {t("common.profileSettings")}
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    className="text-red-500 focus:text-red-500 cursor-pointer"
                    onClick={handleLogout}
                  >
                    <LogOut className="mr-2 h-4 w-4" />
                    <span>{t("common.logout")}</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </>
          ) : (
            <>
              <Button
                variant="outline"
                size="sm"
                className="border-white text-white hover:bg-primary-light"
                asChild
              >
                <Link to="/login">{t("common.login")}</Link>
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="text-white hover:bg-primary-light"
                asChild
              >
                <Link to="/signup">{t("common.signup")}</Link>
              </Button>
            </>
          )}
        </div>
      </div>
    </nav>
  );
}
