import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import LanguageDetector from "i18next-browser-languagedetector";

// Import translations
import enTranslation from "./locales/en.json";
import trTranslation from "./locales/tr.json";

// Initialize i18next
i18n
  // Detect user language
  .use(LanguageDetector)
  // Pass the i18n instance to react-i18next
  .use(initReactI18next)
  // Initialize i18next
  .init({
    // Resources contain translations
    resources: {
      en: {
        translation: enTranslation,
      },
      tr: {
        translation: trTranslation,
      },
    },
    // Default language
    fallbackLng: "en",
    // Debug mode disabled for production
    debug: false,
    // Detect language from localStorage, navigator, etc.
    detection: {
      order: ["localStorage", "navigator"],
      lookupLocalStorage: "i18nextLng",
      caches: ["localStorage"],
    },
    // Interpolation options
    interpolation: {
      escapeValue: false, // React already escapes values
    },
    // React options
    react: {
      useSuspense: false, // Disable suspense for better compatibility
      bindI18n: "languageChanged", // Re-render components when language changes
    },
    // Ensure translations are loaded before rendering
    load: "languageOnly", // Only load language part of the code (e.g., 'en' instead of 'en-US')
    // Return key if translation is missing
    returnNull: false,
    returnEmptyString: false,
    saveMissing: true,
    missingKeyHandler: (lng, ns, key) => {
      console.warn(
        `Missing translation key: ${key} for language: ${lng} in namespace: ${ns}`
      );
    },
  });

// Initialize language change handler without logging
i18n.on("languageChanged", () => {
  // Language change detected - no logging in production
});

export default i18n;
