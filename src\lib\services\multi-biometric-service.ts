import { supabase } from "@/integrations/supabase/client";
import { startRegistration, startAuthentication, isWebAuthnAvailable } from "@/lib/webauthn";
import CryptoJS from "crypto-js";

// Biometric types supported
export type BiometricType = 'fingerprint' | 'face' | 'voice' | 'iris';

// Biometric authentication result
export interface BiometricAuthResult {
  success: boolean;
  type: BiometricType;
  confidence: number;
  error?: string;
  device_info?: string;
}

// Biometric registration data
export interface BiometricRegistration {
  id: string;
  user_id: string;
  biometric_type: BiometricType;
  confidence_threshold: number;
  device_info?: string;
  created_at: string;
}

class MultiBiometricService {
  private readonly ENCRYPTION_KEY = 'multi_biometric_key_2024';
  private readonly DEFAULT_CONFIDENCE_THRESHOLD = 0.85;

  /**
   * Check which biometric types are supported on this device
   */
  async getSupportedTypes(): Promise<BiometricType[]> {
    const supported: BiometricType[] = [];

    try {
      // Check WebAuthn support (fingerprint/face)
      if (isWebAuthnAvailable()) {
        supported.push('fingerprint', 'face');
      }

      // Check Web Speech API (voice)
      if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
        supported.push('voice');
      }

      // Check MediaDevices for camera (iris)
      if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        supported.push('iris');
      }

      return supported;
    } catch (error) {
      console.error('Error checking biometric support:', error);
      return [];
    }
  }

  /**
   * Register a specific biometric type for a user
   */
  async registerBiometric(
    userId: string, 
    type: BiometricType,
    username: string
  ): Promise<BiometricAuthResult> {
    try {
      let encryptedTemplate: string;
      let confidence = this.DEFAULT_CONFIDENCE_THRESHOLD;

      switch (type) {
        case 'fingerprint':
        case 'face':
          // Use existing WebAuthn implementation
          await startRegistration(userId, username);
          encryptedTemplate = this.encryptData(`webauthn_${type}_${Date.now()}`);
          confidence = 0.95;
          break;

        case 'voice':
          const voiceTemplate = await this.registerVoice();
          encryptedTemplate = this.encryptData(voiceTemplate);
          confidence = 0.80;
          break;

        case 'iris':
          const irisTemplate = await this.registerIris();
          encryptedTemplate = this.encryptData(irisTemplate);
          confidence = 0.98;
          break;

        default:
          throw new Error(`Biometric type ${type} not supported`);
      }

      // Store in database using the new function
      const { data, error } = await supabase.rpc('register_biometric', {
        p_user_id: userId,
        p_biometric_type: type,
        p_encrypted_template: encryptedTemplate,
        p_confidence_threshold: confidence,
        p_device_info: navigator.userAgent,
        p_registration_data: {
          timestamp: new Date().toISOString(),
          type: type,
          browser: navigator.userAgent
        }
      });

      if (error) throw error;

      return {
        success: true,
        type: type,
        confidence: confidence,
        device_info: navigator.userAgent
      };

    } catch (error) {
      console.error(`Error registering ${type} biometric:`, error);
      return {
        success: false,
        type: type,
        confidence: 0,
        error: error instanceof Error ? error.message : 'Registration failed'
      };
    }
  }

  /**
   * Authenticate using a specific biometric type
   */
  async authenticateBiometric(
    userId: string, 
    type: BiometricType
  ): Promise<BiometricAuthResult> {
    try {
      let confidence = 0;

      switch (type) {
        case 'fingerprint':
        case 'face':
          // Use existing WebAuthn implementation
          await startAuthentication(userId);
          confidence = 0.95;
          break;

        case 'voice':
          confidence = await this.authenticateVoice();
          break;

        case 'iris':
          confidence = await this.authenticateIris();
          break;

        default:
          throw new Error(`Biometric type ${type} not supported`);
      }

      // Log authentication attempt and check against threshold
      const { data: authResult, error } = await supabase.rpc('authenticate_biometric', {
        p_user_id: userId,
        p_biometric_type: type,
        p_confidence_score: confidence,
        p_device_info: navigator.userAgent,
        p_ip_address: null, // Will be set by server
        p_user_agent: navigator.userAgent,
        p_session_data: {
          timestamp: new Date().toISOString(),
          type: type
        }
      });

      if (error) throw error;

      return {
        success: authResult,
        type: type,
        confidence: confidence,
        device_info: navigator.userAgent
      };

    } catch (error) {
      console.error(`Error authenticating ${type} biometric:`, error);
      return {
        success: false,
        type: type,
        confidence: 0,
        error: error instanceof Error ? error.message : 'Authentication failed'
      };
    }
  }

  /**
   * Get all registered biometric types for a user
   */
  async getUserBiometrics(userId: string): Promise<BiometricRegistration[]> {
    try {
      const { data, error } = await supabase.rpc('get_user_biometric_types', {
        p_user_id: userId
      });

      if (error) throw error;

      return (data || []).map((item: any) => ({
        id: `${userId}_${item.biometric_type}`,
        user_id: userId,
        biometric_type: item.biometric_type,
        confidence_threshold: item.confidence_threshold,
        device_info: item.device_info,
        created_at: item.created_at
      }));
    } catch (error) {
      console.error('Error fetching user biometrics:', error);
      return [];
    }
  }

  /**
   * Remove a biometric registration
   */
  async removeBiometric(userId: string, type: BiometricType): Promise<boolean> {
    try {
      const { data, error } = await supabase.rpc('remove_biometric_registration', {
        p_user_id: userId,
        p_biometric_type: type
      });

      if (error) throw error;
      return data;
    } catch (error) {
      console.error(`Error removing ${type} biometric:`, error);
      return false;
    }
  }

  /**
   * Register voice biometric
   */
  private async registerVoice(): Promise<string> {
    return new Promise((resolve, reject) => {
      const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
      
      if (!SpeechRecognition) {
        reject(new Error('Speech recognition not supported'));
        return;
      }

      const recognition = new SpeechRecognition();
      recognition.continuous = false;
      recognition.interimResults = false;
      recognition.lang = 'en-US';

      const timeout = setTimeout(() => {
        recognition.stop();
        reject(new Error('Voice registration timeout'));
      }, 10000);

      recognition.onresult = (event: any) => {
        clearTimeout(timeout);
        const transcript = event.results[0][0].transcript;
        const confidence = event.results[0][0].confidence;
        
        // Create voice template
        const voiceTemplate = JSON.stringify({
          transcript: transcript.toLowerCase().trim(),
          confidence: confidence,
          length: transcript.length,
          words: transcript.split(' ').length,
          timestamp: Date.now()
        });

        resolve(voiceTemplate);
      };

      recognition.onerror = (event: any) => {
        clearTimeout(timeout);
        reject(new Error(`Voice recognition error: ${event.error}`));
      };

      recognition.start();
    });
  }

  /**
   * Authenticate voice biometric
   */
  private async authenticateVoice(): Promise<number> {
    return new Promise((resolve, reject) => {
      const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
      
      if (!SpeechRecognition) {
        reject(new Error('Speech recognition not supported'));
        return;
      }

      const recognition = new SpeechRecognition();
      recognition.continuous = false;
      recognition.interimResults = false;
      recognition.lang = 'en-US';

      const timeout = setTimeout(() => {
        recognition.stop();
        reject(new Error('Voice authentication timeout'));
      }, 10000);

      recognition.onresult = (event: any) => {
        clearTimeout(timeout);
        const confidence = event.results[0][0].confidence;
        resolve(confidence);
      };

      recognition.onerror = (event: any) => {
        clearTimeout(timeout);
        reject(new Error(`Voice authentication error: ${event.error}`));
      };

      recognition.start();
    });
  }

  /**
   * Register iris biometric (simplified implementation)
   */
  private async registerIris(): Promise<string> {
    return new Promise((resolve, reject) => {
      // Simplified iris registration - in reality this would use specialized iris scanning
      navigator.mediaDevices.getUserMedia({ video: true })
        .then(stream => {
          // Simulate iris template creation
          const irisTemplate = JSON.stringify({
            type: 'iris',
            timestamp: Date.now(),
            device: navigator.userAgent,
            // In reality, this would contain iris pattern data
            pattern_hash: CryptoJS.SHA256(`iris_${Date.now()}`).toString()
          });

          // Stop the camera stream
          stream.getTracks().forEach(track => track.stop());
          
          resolve(irisTemplate);
        })
        .catch(error => {
          reject(new Error(`Iris registration failed: ${error.message}`));
        });
    });
  }

  /**
   * Authenticate iris biometric (simplified implementation)
   */
  private async authenticateIris(): Promise<number> {
    return new Promise((resolve, reject) => {
      // Simplified iris authentication
      navigator.mediaDevices.getUserMedia({ video: true })
        .then(stream => {
          // Simulate iris matching - return high confidence
          const confidence = 0.95;

          // Stop the camera stream
          stream.getTracks().forEach(track => track.stop());
          
          resolve(confidence);
        })
        .catch(error => {
          reject(new Error(`Iris authentication failed: ${error.message}`));
        });
    });
  }

  /**
   * Encrypt sensitive data
   */
  private encryptData(data: string): string {
    return CryptoJS.AES.encrypt(data, this.ENCRYPTION_KEY).toString();
  }

  /**
   * Decrypt sensitive data
   */
  private decryptData(encryptedData: string): string {
    const bytes = CryptoJS.AES.decrypt(encryptedData, this.ENCRYPTION_KEY);
    return bytes.toString(CryptoJS.enc.Utf8);
  }
}

// Global multi-biometric service instance
export const multiBiometricService = new MultiBiometricService();
