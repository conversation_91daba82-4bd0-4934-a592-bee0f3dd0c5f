import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { format } from "date-fns";
import { useAuth } from "@/context/AuthContext";
import { supabase } from "@/lib/supabase";
import { useTranslation } from "react-i18next";
import {
  AlertTriangle,
  MapPin,
  Check,
  Trash2,
  Filter,
  Search,
  BarChart4,
  List,
  Smartphone,
  Map,
  Clock,
  Users,
  Loader2,
  ChevronDown,
  Info,
  Shield,
  AlertCircle,
  X,
  MoreVertical,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Progress } from "@/components/ui/progress";

// Types
interface FraudAlert {
  id: string;
  title: string;
  message: string;
  created_at: string;
  type: string;
  category: string;
  severity: string;
  status: string;
  distance_meters: number;
  student_id: string;
  teacher_id: string;
  student_location: {
    x: number;
    y: number;
  };
  metadata: {
    student_name: string;
    room_number: string;
    exceeded_by_meters: number;
    device_info?: string;
    verification_method?: string;
  };
  admin_read_at: string | null;
}

interface FraudStats {
  total: number;
  locationBased: number;
  deviceBased: number;
  timeBased: number;
  patternBased: number;
  highRisk: number;
  mediumRisk: number;
  lowRisk: number;
  resolved: number;
  pending: number;
}

export default function EnhancedFraudDetection() {
  const [alerts, setAlerts] = useState<FraudAlert[]>([]);
  const [filteredAlerts, setFilteredAlerts] = useState<FraudAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("overview");
  const [searchQuery, setSearchQuery] = useState("");
  const [filterSeverity, setFilterSeverity] = useState<string>("all");
  const [filterType, setFilterType] = useState<string>("all");
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [stats, setStats] = useState<FraudStats>({
    total: 0,
    locationBased: 0,
    deviceBased: 0,
    timeBased: 0,
    patternBased: 0,
    highRisk: 0,
    mediumRisk: 0,
    lowRisk: 0,
    resolved: 0,
    pending: 0,
  });
  const { toast } = useToast();
  const { profile } = useAuth();
  const { t } = useTranslation();

  useEffect(() => {
    fetchAlerts();
    const unsubscribe = subscribeToAlerts();
    return () => {
      unsubscribe();
    };
  }, []);

  useEffect(() => {
    // Apply filters whenever alerts, search query, or filters change
    applyFilters();
  }, [alerts, searchQuery, filterSeverity, filterType, filterStatus]);

  const fetchAlerts = async () => {
    try {
      setLoading(true);
      // Fetch all potential fraud alerts - using only the type field since category doesn't exist
      const { data, error } = await supabase
        .from("notifications")
        .select("*")
        .in("type", ["distance_alert", "attendance_alert", "system_alert"])
        .order("created_at", { ascending: false });

      if (error) throw error;

      // Process the data to add missing fields that our component expects
      const alertsData = (data || []).map((alert) => ({
        ...alert,
        // Add category field based on type
        category:
          alert.type === "distance_alert" ? "fraud_detection" : "general",
        // Add severity field based on distance_meters if available
        severity: alert.distance_meters
          ? alert.distance_meters > 100
            ? "high"
            : alert.distance_meters > 50
            ? "medium"
            : "low"
          : "medium",
        // Add status field if not present
        status: alert.admin_read_at ? "resolved" : "pending",
        // Ensure metadata exists
        metadata: alert.metadata || {
          student_name: "Unknown Student",
          room_number: alert.room_number || "Unknown",
          exceeded_by_meters: alert.distance_meters
            ? alert.distance_meters - 50
            : 0,
        },
      }));

      setAlerts(alertsData);
      calculateStats(alertsData);
    } catch (error) {
      console.error("Error fetching fraud alerts:", error);
      toast({
        title: "Error",
        description: "Failed to fetch fraud detection alerts",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const calculateStats = (alertsData: FraudAlert[]) => {
    const newStats: FraudStats = {
      total: alertsData.length,
      locationBased: alertsData.filter((a) => a.type === "distance_alert")
        .length,
      deviceBased: alertsData.filter(
        (a) =>
          a.type === "device_inconsistency" || a.type === "attendance_alert"
      ).length,
      timeBased: alertsData.filter(
        (a) => a.type === "concurrent_session" || a.type === "system_alert"
      ).length,
      patternBased: alertsData.filter(
        (a) =>
          a.type === "pattern_anomaly" ||
          (a.type === "system_alert" && a.message?.includes("pattern"))
      ).length,
      highRisk: alertsData.filter((a) => a.severity === "high").length,
      mediumRisk: alertsData.filter((a) => a.severity === "medium").length,
      lowRisk: alertsData.filter((a) => a.severity === "low").length,
      resolved: alertsData.filter((a) => a.status === "resolved").length,
      pending: alertsData.filter((a) => a.status === "pending").length,
    };
    setStats(newStats);
  };

  const subscribeToAlerts = () => {
    const subscription = supabase
      .channel("fraud_alerts_enhanced")
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "notifications",
          // No filter on category since it doesn't exist
        },
        (payload) => {
          if (payload.eventType === "INSERT") {
            // Process the new alert to add missing fields
            const newAlert = {
              ...(payload.new as any),
              category:
                payload.new.type === "distance_alert"
                  ? "fraud_detection"
                  : "general",
              severity: payload.new.distance_meters
                ? payload.new.distance_meters > 100
                  ? "high"
                  : payload.new.distance_meters > 50
                  ? "medium"
                  : "low"
                : "medium",
              status: payload.new.admin_read_at ? "resolved" : "pending",
              metadata: payload.new.metadata || {
                student_name: "Unknown Student",
                room_number: payload.new.room_number || "Unknown",
                exceeded_by_meters: payload.new.distance_meters
                  ? payload.new.distance_meters - 50
                  : 0,
              },
            };

            setAlerts((prev) => {
              const newAlerts = [newAlert as FraudAlert, ...prev];
              calculateStats(newAlerts);
              return newAlerts;
            });

            toast({
              title: t("admin.fraudDetection.newFraudAlert"),
              description: t("admin.fraudDetection.newFraudAttemptDetected"),
              variant: "destructive",
            });
          } else if (payload.eventType === "DELETE") {
            setAlerts((prev) => {
              const newAlerts = prev.filter(
                (alert) => alert.id !== payload.old.id
              );
              calculateStats(newAlerts);
              return newAlerts;
            });
          } else if (payload.eventType === "UPDATE") {
            // Process the updated alert to add missing fields
            const updatedAlert = {
              ...(payload.new as any),
              category:
                payload.new.type === "distance_alert"
                  ? "fraud_detection"
                  : "general",
              severity: payload.new.distance_meters
                ? payload.new.distance_meters > 100
                  ? "high"
                  : payload.new.distance_meters > 50
                  ? "medium"
                  : "low"
                : "medium",
              status: payload.new.admin_read_at ? "resolved" : "pending",
              metadata: payload.new.metadata || {
                student_name: "Unknown Student",
                room_number: payload.new.room_number || "Unknown",
                exceeded_by_meters: payload.new.distance_meters
                  ? payload.new.distance_meters - 50
                  : 0,
              },
            };

            setAlerts((prev) => {
              const newAlerts = prev.map((alert) =>
                alert.id === payload.new.id
                  ? (updatedAlert as FraudAlert)
                  : alert
              );
              calculateStats(newAlerts);
              return newAlerts;
            });
          }
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  };

  const applyFilters = () => {
    let filtered = [...alerts];

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (alert) =>
          alert.metadata.student_name?.toLowerCase().includes(query) ||
          alert.message.toLowerCase().includes(query) ||
          alert.metadata.room_number?.toLowerCase().includes(query)
      );
    }

    // Apply severity filter
    if (filterSeverity !== "all") {
      filtered = filtered.filter((alert) => alert.severity === filterSeverity);
    }

    // Apply type filter
    if (filterType !== "all") {
      filtered = filtered.filter((alert) => alert.type === filterType);
    }

    // Apply status filter
    if (filterStatus !== "all") {
      filtered = filtered.filter((alert) => alert.status === filterStatus);
    }

    setFilteredAlerts(filtered);
  };

  const markAsInvestigating = async (alertId: string) => {
    try {
      const { error } = await supabase
        .from("notifications")
        .update({
          admin_read_at: new Date().toISOString(),
          status: "investigating",
        })
        .eq("id", alertId);

      if (error) throw error;

      setAlerts((prev) =>
        prev.map((alert) =>
          alert.id === alertId
            ? {
                ...alert,
                admin_read_at: new Date().toISOString(),
                status: "investigating",
              }
            : alert
        )
      );

      toast({
        title: t("admin.fraudDetection.statusUpdated"),
        description: t("admin.fraudDetection.alertMarkedAsInvestigating"),
      });
    } catch (error) {
      console.error("Error updating alert status:", error);
      toast({
        title: t("admin.fraudDetection.error"),
        description: t("admin.fraudDetection.failedToUpdateStatus"),
        variant: "destructive",
      });
    }
  };

  const markAsResolved = async (alertId: string) => {
    try {
      const { error } = await supabase
        .from("notifications")
        .update({
          status: "resolved",
        })
        .eq("id", alertId);

      if (error) throw error;

      setAlerts((prev) =>
        prev.map((alert) =>
          alert.id === alertId ? { ...alert, status: "resolved" } : alert
        )
      );

      toast({
        title: t("admin.fraudDetection.statusUpdated"),
        description: t("admin.fraudDetection.alertMarkedAsResolved"),
      });
    } catch (error) {
      console.error("Error resolving alert:", error);
      toast({
        title: t("admin.fraudDetection.error"),
        description: t("admin.fraudDetection.failedToResolveAlert"),
        variant: "destructive",
      });
    }
  };

  const deleteAlert = async (alertId: string) => {
    try {
      const { error } = await supabase
        .from("notifications")
        .delete()
        .eq("id", alertId);

      if (error) throw error;

      setAlerts((prev) => prev.filter((alert) => alert.id !== alertId));

      toast({
        title: t("admin.fraudDetection.success"),
        description: t("admin.fraudDetection.alertDeletedSuccessfully"),
      });
    } catch (error) {
      console.error("Error deleting alert:", error);
      toast({
        title: t("admin.fraudDetection.error"),
        description: t("admin.fraudDetection.failedToDeleteAlert"),
        variant: "destructive",
      });
    }
  };

  const openInMaps = (lat: number, lng: number) => {
    window.open(`https://www.google.com/maps?q=${lat},${lng}`, "_blank");
  };

  const getAlertTypeIcon = (type: string) => {
    switch (type) {
      case "distance_alert":
        return <MapPin className="h-4 w-4 text-red-500" />;
      case "device_inconsistency":
      case "attendance_alert":
        return <Smartphone className="h-4 w-4 text-orange-500" />;
      case "concurrent_session":
      case "system_alert":
        return <Users className="h-4 w-4 text-yellow-500" />;
      case "pattern_anomaly":
        return <BarChart4 className="h-4 w-4 text-purple-500" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getAlertTypeLabel = (type: string) => {
    switch (type) {
      case "distance_alert":
        return "Location";
      case "device_inconsistency":
      case "attendance_alert":
        return "Device";
      case "concurrent_session":
      case "system_alert":
        return "Concurrent";
      case "pattern_anomaly":
        return "Pattern";
      default:
        return "Unknown";
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case "high":
        return "text-red-500 bg-red-50 border-red-200";
      case "medium":
        return "text-orange-500 bg-orange-50 border-orange-200";
      case "low":
        return "text-yellow-500 bg-yellow-50 border-yellow-200";
      default:
        return "text-gray-500 bg-gray-50 border-gray-200";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "text-blue-500 bg-blue-50 border-blue-200";
      case "investigating":
        return "text-purple-500 bg-purple-50 border-purple-200";
      case "resolved":
        return "text-green-500 bg-green-50 border-green-200";
      default:
        return "text-gray-500 bg-gray-50 border-gray-200";
    }
  };

  if (loading) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Loading fraud detection dashboard...</CardTitle>
        </CardHeader>
        <CardContent className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-primary" />
              {t("admin.fraudDetection.title")}
            </CardTitle>
            <CardDescription>
              {t("admin.fraudDetection.description")}
            </CardDescription>
          </div>
          <Badge
            variant={
              filteredAlerts.filter((a) => !a.admin_read_at).length > 0
                ? "destructive"
                : "outline"
            }
          >
            {filteredAlerts.filter((a) => !a.admin_read_at).length}{" "}
            {t("admin.fraudDetection.unread")}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview" className="flex items-center gap-2">
              <BarChart4 className="h-4 w-4" />
              <span>{t("admin.fraudDetection.overview")}</span>
            </TabsTrigger>
            <TabsTrigger value="alerts" className="flex items-center gap-2">
              <AlertTriangle className="h-4 w-4" />
              <span>{t("admin.fraudDetection.alerts")}</span>
            </TabsTrigger>
            <TabsTrigger value="location" className="flex items-center gap-2">
              <Map className="h-4 w-4" />
              <span>{t("admin.fraudDetection.location")}</span>
            </TabsTrigger>
            <TabsTrigger value="devices" className="flex items-center gap-2">
              <Smartphone className="h-4 w-4" />
              <span>{t("admin.fraudDetection.devices")}</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">
                    {t("admin.fraudDetection.totalAlerts")}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.total}</div>
                  <p className="text-xs text-muted-foreground">
                    {stats.pending} {t("admin.fraudDetection.pending")},{" "}
                    {stats.resolved} {t("admin.fraudDetection.resolved")}
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">
                    {t("admin.fraudDetection.highRisk")}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-red-500">
                    {stats.highRisk}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {Math.round((stats.highRisk / (stats.total || 1)) * 100)}%
                    {t("admin.fraudDetection.ofTotalAlerts")}
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">
                    {t("admin.fraudDetection.locationFraud")}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-orange-500">
                    {stats.locationBased}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {Math.round(
                      (stats.locationBased / (stats.total || 1)) * 100
                    )}
                    % {t("admin.fraudDetection.ofTotalAlerts")}
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">
                    {t("admin.fraudDetection.deviceFraud")}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-blue-500">
                    {stats.deviceBased}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {Math.round((stats.deviceBased / (stats.total || 1)) * 100)}
                    % {t("admin.fraudDetection.ofTotalAlerts")}
                  </p>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm font-medium">
                  {t("admin.fraudDetection.fraudDistribution")}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center">
                        <div className="h-3 w-3 rounded-full bg-red-500 mr-2"></div>
                        <span>{t("admin.fraudDetection.locationBased")}</span>
                      </div>
                      <span>{stats.locationBased}</span>
                    </div>
                    <Progress
                      value={(stats.locationBased / (stats.total || 1)) * 100}
                      className="h-2 bg-gray-100"
                      indicatorClassName="bg-red-500"
                    />
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center">
                        <div className="h-3 w-3 rounded-full bg-blue-500 mr-2"></div>
                        <span>{t("admin.fraudDetection.deviceBased")}</span>
                      </div>
                      <span>{stats.deviceBased}</span>
                    </div>
                    <Progress
                      value={(stats.deviceBased / (stats.total || 1)) * 100}
                      className="h-2 bg-gray-100"
                      indicatorClassName="bg-blue-500"
                    />
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center">
                        <div className="h-3 w-3 rounded-full bg-yellow-500 mr-2"></div>
                        <span>{t("admin.fraudDetection.timeBased")}</span>
                      </div>
                      <span>{stats.timeBased}</span>
                    </div>
                    <Progress
                      value={(stats.timeBased / (stats.total || 1)) * 100}
                      className="h-2 bg-gray-100"
                      indicatorClassName="bg-yellow-500"
                    />
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center">
                        <div className="h-3 w-3 rounded-full bg-purple-500 mr-2"></div>
                        <span>{t("admin.fraudDetection.patternBased")}</span>
                      </div>
                      <span>{stats.patternBased}</span>
                    </div>
                    <Progress
                      value={(stats.patternBased / (stats.total || 1)) * 100}
                      className="h-2 bg-gray-100"
                      indicatorClassName="bg-purple-500"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm font-medium">
                  {t("admin.fraudDetection.riskAssessment")}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center">
                        <div className="h-3 w-3 rounded-full bg-red-500 mr-2"></div>
                        <span>{t("admin.fraudDetection.highRisk")}</span>
                      </div>
                      <span>{stats.highRisk}</span>
                    </div>
                    <Progress
                      value={(stats.highRisk / (stats.total || 1)) * 100}
                      className="h-2 bg-gray-100"
                      indicatorClassName="bg-red-500"
                    />
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center">
                        <div className="h-3 w-3 rounded-full bg-orange-500 mr-2"></div>
                        <span>{t("admin.fraudDetection.mediumRisk")}</span>
                      </div>
                      <span>{stats.mediumRisk}</span>
                    </div>
                    <Progress
                      value={(stats.mediumRisk / (stats.total || 1)) * 100}
                      className="h-2 bg-gray-100"
                      indicatorClassName="bg-orange-500"
                    />
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center">
                        <div className="h-3 w-3 rounded-full bg-yellow-500 mr-2"></div>
                        <span>{t("admin.fraudDetection.lowRisk")}</span>
                      </div>
                      <span>{stats.lowRisk}</span>
                    </div>
                    <Progress
                      value={(stats.lowRisk / (stats.total || 1)) * 100}
                      className="h-2 bg-gray-100"
                      indicatorClassName="bg-yellow-500"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="alerts" className="space-y-4">
            <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
              <div className="flex flex-1 w-full md:w-auto gap-2">
                <div className="relative flex-1">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder={t("admin.fraudDetection.searchAlerts")}
                    className="pl-8"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="gap-1">
                      <Filter className="h-4 w-4" />
                      <span className="hidden sm:inline">
                        {t("admin.fraudDetection.filters")}
                      </span>
                      <ChevronDown className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-[200px]">
                    <DropdownMenuLabel>
                      {t("admin.fraudDetection.filterAlerts")}
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />

                    <div className="p-2">
                      <p className="text-xs font-medium mb-1">
                        {t("admin.fraudDetection.severity")}
                      </p>
                      <Select
                        value={filterSeverity}
                        onValueChange={setFilterSeverity}
                      >
                        <SelectTrigger className="h-8">
                          <SelectValue
                            placeholder={t(
                              "admin.fraudDetection.allSeverities"
                            )}
                          />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">
                            {t("admin.fraudDetection.allSeverities")}
                          </SelectItem>
                          <SelectItem value="high">
                            {t("admin.fraudDetection.highRisk")}
                          </SelectItem>
                          <SelectItem value="medium">
                            {t("admin.fraudDetection.mediumRisk")}
                          </SelectItem>
                          <SelectItem value="low">
                            {t("admin.fraudDetection.lowRisk")}
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="p-2">
                      <p className="text-xs font-medium mb-1">
                        {t("admin.fraudDetection.type")}
                      </p>
                      <Select value={filterType} onValueChange={setFilterType}>
                        <SelectTrigger className="h-8">
                          <SelectValue
                            placeholder={t("admin.fraudDetection.allTypes")}
                          />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">
                            {t("admin.fraudDetection.allTypes")}
                          </SelectItem>
                          <SelectItem value="distance_alert">
                            {t("admin.fraudDetection.location")}
                          </SelectItem>
                          <SelectItem value="device_inconsistency">
                            {t("admin.fraudDetection.devices")}
                          </SelectItem>
                          <SelectItem value="concurrent_session">
                            Concurrent
                          </SelectItem>
                          <SelectItem value="pattern_anomaly">
                            Pattern
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="p-2">
                      <p className="text-xs font-medium mb-1">
                        {t("admin.fraudDetection.status")}
                      </p>
                      <Select
                        value={filterStatus}
                        onValueChange={setFilterStatus}
                      >
                        <SelectTrigger className="h-8">
                          <SelectValue
                            placeholder={t("admin.fraudDetection.allStatuses")}
                          />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">
                            {t("admin.fraudDetection.allStatuses")}
                          </SelectItem>
                          <SelectItem value="pending">
                            {t("admin.fraudDetection.pending")}
                          </SelectItem>
                          <SelectItem value="investigating">
                            {t("admin.fraudDetection.investigating")}
                          </SelectItem>
                          <SelectItem value="resolved">
                            {t("admin.fraudDetection.resolved")}
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      onClick={() => {
                        setFilterSeverity("all");
                        setFilterType("all");
                        setFilterStatus("all");
                        setSearchQuery("");
                      }}
                    >
                      <X className="h-4 w-4 mr-2" />
                      {t("admin.fraudDetection.resetFilters")}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <span>
                  {t("admin.fraudDetection.showing")} {filteredAlerts.length}{" "}
                  {t("admin.fraudDetection.of")} {alerts.length}{" "}
                  {t("admin.fraudDetection.alerts")}
                </span>
              </div>
            </div>

            <div className="space-y-4">
              {filteredAlerts.length === 0 ? (
                <Card>
                  <CardContent className="flex flex-col items-center justify-center py-8">
                    <Info className="h-12 w-12 text-muted-foreground opacity-50 mb-2" />
                    <p className="text-muted-foreground text-center">
                      {alerts.length === 0
                        ? t("admin.fraudDetection.noFraudAlertsDetected")
                        : t("admin.fraudDetection.noAlertsMatchFilters")}
                    </p>
                    {alerts.length > 0 && (
                      <Button
                        variant="link"
                        onClick={() => {
                          setFilterSeverity("all");
                          setFilterType("all");
                          setFilterStatus("all");
                          setSearchQuery("");
                        }}
                      >
                        {t("admin.fraudDetection.resetFilters")}
                      </Button>
                    )}
                  </CardContent>
                </Card>
              ) : (
                filteredAlerts.map((alert) => (
                  <Card
                    key={alert.id}
                    className={
                      alert.admin_read_at
                        ? "border-l-4 border-l-gray-200"
                        : "border-l-4 border-l-red-500"
                    }
                  >
                    <CardContent className="p-4">
                      <div className="flex flex-col md:flex-row md:items-start justify-between gap-4">
                        <div className="space-y-2 flex-1">
                          <div className="flex items-center gap-2">
                            {getAlertTypeIcon(alert.type)}
                            <h3 className="font-semibold">
                              {alert.metadata.student_name}
                            </h3>
                            <Badge
                              className={`${getSeverityColor(alert.severity)}`}
                            >
                              {alert.severity.charAt(0).toUpperCase() +
                                alert.severity.slice(1)}{" "}
                              Risk
                            </Badge>
                            <Badge
                              className={`${getStatusColor(alert.status)}`}
                            >
                              {alert.status.charAt(0).toUpperCase() +
                                alert.status.slice(1)}
                            </Badge>
                          </div>

                          <p className="text-sm">{alert.message}</p>

                          <div className="flex flex-wrap gap-2 text-xs">
                            <Badge variant="outline" className="gap-1">
                              <MapPin className="h-3 w-3" />
                              {t("admin.fraudDetection.room")}:{" "}
                              {alert.metadata.room_number}
                            </Badge>

                            {alert.distance_meters && (
                              <Badge variant="outline" className="gap-1">
                                <Map className="h-3 w-3" />
                                {t("admin.fraudDetection.distance")}:{" "}
                                {Math.round(alert.distance_meters)}m
                              </Badge>
                            )}

                            {alert.metadata.exceeded_by_meters && (
                              <Badge variant="outline" className="gap-1">
                                <AlertCircle className="h-3 w-3" />
                                {t("admin.fraudDetection.exceededBy")}:{" "}
                                {Math.round(alert.metadata.exceeded_by_meters)}m
                              </Badge>
                            )}

                            {alert.metadata.device_info && (
                              <Badge variant="outline" className="gap-1">
                                <Smartphone className="h-3 w-3" />
                                {t("admin.fraudDetection.deviceChange")}
                              </Badge>
                            )}

                            <Badge variant="outline" className="gap-1">
                              <Clock className="h-3 w-3" />
                              {format(
                                new Date(alert.created_at),
                                "MMM d, h:mm a"
                              )}
                            </Badge>
                          </div>
                        </div>

                        <div className="flex flex-row md:flex-col gap-2">
                          {alert.student_location && (
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() =>
                                      openInMaps(
                                        alert.student_location.y,
                                        alert.student_location.x
                                      )
                                    }
                                  >
                                    <MapPin className="h-4 w-4" />
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>
                                    {t("admin.fraudDetection.viewLocation")}
                                  </p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          )}

                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button size="sm" variant="outline">
                                <MoreVertical className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>
                                {t("admin.fraudDetection.actions")}
                              </DropdownMenuLabel>
                              <DropdownMenuSeparator />
                              {!alert.admin_read_at && (
                                <DropdownMenuItem
                                  onClick={() => markAsInvestigating(alert.id)}
                                >
                                  <Check className="h-4 w-4 mr-2" />
                                  {t(
                                    "admin.fraudDetection.markAsInvestigating"
                                  )}
                                </DropdownMenuItem>
                              )}
                              {alert.status !== "resolved" && (
                                <DropdownMenuItem
                                  onClick={() => markAsResolved(alert.id)}
                                >
                                  <Check className="h-4 w-4 mr-2" />
                                  {t("admin.fraudDetection.markAsResolved")}
                                </DropdownMenuItem>
                              )}
                              <DropdownMenuItem
                                onClick={() => deleteAlert(alert.id)}
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                {t("admin.fraudDetection.deleteAlert")}
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </TabsContent>

          <TabsContent value="location" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm font-medium">
                  {t("admin.fraudDetection.locationBasedFraudDetection")}
                </CardTitle>
                <CardDescription>
                  {t("admin.fraudDetection.monitorAttendanceVerification")}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">
                        {t("admin.fraudDetection.distanceViolations")}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-red-500">
                        {stats.locationBased}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {t(
                          "admin.fraudDetection.studentsAttemptingOutsideRadius"
                        )}
                      </p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">
                        {t("admin.fraudDetection.geofenceViolations")}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-orange-500">
                        {Math.round(stats.locationBased * 0.7)}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {t("admin.fraudDetection.studentsOutsideCampus")}
                      </p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">
                        {t("admin.fraudDetection.locationSpoofing")}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-yellow-500">
                        {Math.round(stats.locationBased * 0.3)}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {t("admin.fraudDetection.detectedGpsSpoofing")}
                      </p>
                    </CardContent>
                  </Card>
                </div>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm font-medium">
                      {t("admin.fraudDetection.locationFraudPreventionTips")}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-start gap-2">
                        <div className="mt-0.5">
                          <AlertCircle className="h-5 w-5 text-amber-500" />
                        </div>
                        <div>
                          <h4 className="text-sm font-medium">
                            {t("admin.fraudDetection.adjustRoomGeofenceRadius")}
                          </h4>
                          <p className="text-sm text-muted-foreground">
                            {t("admin.fraudDetection.configureGeofenceRadius")}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-start gap-2">
                        <div className="mt-0.5">
                          <AlertCircle className="h-5 w-5 text-amber-500" />
                        </div>
                        <div>
                          <h4 className="text-sm font-medium">
                            {t(
                              "admin.fraudDetection.enableMultiFactorVerification"
                            )}
                          </h4>
                          <p className="text-sm text-muted-foreground">
                            {t("admin.fraudDetection.requireBothLocation")}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-start gap-2">
                        <div className="mt-0.5">
                          <AlertCircle className="h-5 w-5 text-amber-500" />
                        </div>
                        <div>
                          <h4 className="text-sm font-medium">
                            {t(
                              "admin.fraudDetection.implementLocationConsistency"
                            )}
                          </h4>
                          <p className="text-sm text-muted-foreground">
                            {t("admin.fraudDetection.flagSuspiciousChanges")}
                          </p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="devices" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm font-medium">
                  {t("admin.fraudDetection.deviceBasedFraudDetection")}
                </CardTitle>
                <CardDescription>
                  {t("admin.fraudDetection.monitorSuspiciousDevicePatterns")}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">
                        {t("admin.fraudDetection.deviceInconsistencies")}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-blue-500">
                        {stats.deviceBased}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {t(
                          "admin.fraudDetection.studentsUsingDifferentDevices"
                        )}
                      </p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">
                        {t("admin.fraudDetection.concurrentSessions")}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-purple-500">
                        {stats.timeBased}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {t("admin.fraudDetection.multipleAttendanceRecords")}
                      </p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">
                        {t("admin.fraudDetection.emulatorDetection")}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-indigo-500">
                        {Math.round(stats.deviceBased * 0.2)}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {t("admin.fraudDetection.detectedEmulatorUsage")}
                      </p>
                    </CardContent>
                  </Card>
                </div>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm font-medium">
                      {t("admin.fraudDetection.deviceFraudPreventionTips")}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-start gap-2">
                        <div className="mt-0.5">
                          <Smartphone className="h-5 w-5 text-blue-500" />
                        </div>
                        <div>
                          <h4 className="text-sm font-medium">
                            {t(
                              "admin.fraudDetection.implementDeviceFingerprinting"
                            )}
                          </h4>
                          <p className="text-sm text-muted-foreground">
                            {t(
                              "admin.fraudDetection.trackUniqueDeviceCharacteristics"
                            )}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-start gap-2">
                        <div className="mt-0.5">
                          <Smartphone className="h-5 w-5 text-blue-500" />
                        </div>
                        <div>
                          <h4 className="text-sm font-medium">
                            {t(
                              "admin.fraudDetection.enforceDeviceRegistration"
                            )}
                          </h4>
                          <p className="text-sm text-muted-foreground">
                            {t(
                              "admin.fraudDetection.requireStudentsToRegister"
                            )}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-start gap-2">
                        <div className="mt-0.5">
                          <Smartphone className="h-5 w-5 text-blue-500" />
                        </div>
                        <div>
                          <h4 className="text-sm font-medium">
                            {t("admin.fraudDetection.detectEmulatorsAndRooted")}
                          </h4>
                          <p className="text-sm text-muted-foreground">
                            {t(
                              "admin.fraudDetection.identifyAndBlockAttendance"
                            )}
                          </p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
