import { useEffect, useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useSchool } from "@/context/SchoolContext";
import { useAuth } from "@/context/AuthContext";
import {
  Building2,
  Users,
  School,
  MapPin,
  Phone,
  Mail,
  Globe,
} from "lucide-react";
import { useTranslation } from "react-i18next";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { fetchSchools } from "@/lib/api/schools";

export default function SchoolContext() {
  const { currentSchool, loading: schoolLoading } = useSchool();
  const { profile } = useAuth();
  const [schoolCount, setSchoolCount] = useState<number | null>(null);
  const [loadingCount, setLoadingCount] = useState(true);
  const { t } = useTranslation();

  // Fetch school count for system admins
  useEffect(() => {
    const getSchoolCount = async () => {
      if (profile?.accessLevel === 3) {
        setLoadingCount(true);
        try {
          const schools = await fetchSchools();
          setSchoolCount(schools.length);
        } catch (error) {
          console.error("Error fetching school count:", error);
        } finally {
          setLoadingCount(false);
        }
      }
    };

    getSchoolCount();
  }, [profile]);

  // If loading, show loading spinner
  if (schoolLoading) {
    return (
      <div className="flex justify-center items-center h-32">
        <LoadingSpinner message="Loading school information..." />
      </div>
    );
  }

  // If no school is selected for system admin, show school count
  if (profile?.accessLevel === 3 && !currentSchool) {
    return (
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            System Overview
          </CardTitle>
          <CardDescription>
            You have system administrator access
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div className="flex items-center gap-3 p-3 sm:p-4 border rounded-lg">
              <Building2 className="h-6 w-6 sm:h-8 sm:w-8 text-primary flex-shrink-0" />
              <div>
                <p className="text-[10px] xs:text-xs sm:text-sm text-muted-foreground">
                  Total Schools
                </p>
                <div>
                  {loadingCount ? (
                    <Skeleton className="h-6 sm:h-7 w-12 sm:w-16" />
                  ) : (
                    <p className="text-xl sm:text-2xl font-bold">
                      {schoolCount || 0}
                    </p>
                  )}
                </div>
              </div>
            </div>
            <div className="flex items-center gap-3 p-3 sm:p-4 border rounded-lg">
              <Users className="h-6 w-6 sm:h-8 sm:w-8 text-primary flex-shrink-0" />
              <div>
                <p className="text-[10px] xs:text-xs sm:text-sm text-muted-foreground">
                  Access Level
                </p>
                <p className="text-xl sm:text-2xl font-bold">System</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // If school is selected or user is school admin, show school details
  return (
    <Card className="shadow-sm hover:shadow-md transition-shadow duration-200 border-t-4 border-t-primary w-full max-w-full mx-auto">
      <CardHeader className="pb-2 px-2 xs:px-3 sm:px-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2 sm:gap-0">
          <div>
            <CardTitle className="text-sm xs:text-base sm:text-lg flex items-center gap-1 xs:gap-2">
              <School className="h-3 w-3 xs:h-4 xs:w-4 sm:h-5 sm:w-5 text-primary" />
              {currentSchool?.name || "UMH"}
            </CardTitle>
            <CardDescription>
              {profile?.accessLevel === 3
                ? "You are viewing a specific school"
                : t("admin.schoolContext.yourSchoolInformation")}
            </CardDescription>
          </div>
          {currentSchool?.isActive !== false ? (
            <Badge
              variant="outline"
              className="bg-green-500/10 text-green-600 border-green-200 text-xs"
            >
              {t("admin.schoolContext.active")}
            </Badge>
          ) : (
            <Badge
              variant="outline"
              className="bg-red-500/10 text-red-600 border-red-200 text-xs"
            >
              Inactive
            </Badge>
          )}
        </div>
      </CardHeader>
      <CardContent className="px-2 xs:px-3 sm:px-6 py-2 sm:py-4">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          {currentSchool?.address && (
            <div className="flex items-start gap-2 sm:gap-3">
              <MapPin className="h-4 w-4 sm:h-5 sm:w-5 text-primary mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-[10px] xs:text-xs sm:text-sm font-medium">
                  {t("admin.schoolContext.address")}
                </p>
                <p className="text-[10px] xs:text-xs sm:text-sm text-muted-foreground break-words">
                  {currentSchool.address}
                  {currentSchool.city && `, ${currentSchool.city}`}
                  {currentSchool.state && `, ${currentSchool.state}`}
                  {currentSchool.zip && ` ${currentSchool.zip}`}
                  {currentSchool.country && `, ${currentSchool.country}`}
                </p>
              </div>
            </div>
          )}

          {currentSchool?.phone && (
            <div className="flex items-start gap-2 sm:gap-3">
              <Phone className="h-4 w-4 sm:h-5 sm:w-5 text-primary mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-[10px] xs:text-xs sm:text-sm font-medium">
                  {t("admin.schoolContext.phone")}
                </p>
                <p className="text-[10px] xs:text-xs sm:text-sm text-muted-foreground">
                  {currentSchool.phone}
                </p>
              </div>
            </div>
          )}

          {currentSchool?.email && (
            <div className="flex items-start gap-2 sm:gap-3">
              <Mail className="h-4 w-4 sm:h-5 sm:w-5 text-primary mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-[10px] xs:text-xs sm:text-sm font-medium">
                  {t("admin.schoolContext.email")}
                </p>
                <p className="text-[10px] xs:text-xs sm:text-sm text-muted-foreground break-words">
                  {currentSchool.email}
                </p>
              </div>
            </div>
          )}

          {currentSchool?.website && (
            <div className="flex items-start gap-2 sm:gap-3">
              <Globe className="h-4 w-4 sm:h-5 sm:w-5 text-primary mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-[10px] xs:text-xs sm:text-sm font-medium">
                  Website
                </p>
                <a
                  href={
                    currentSchool.website.startsWith("http")
                      ? currentSchool.website
                      : `https://${currentSchool.website}`
                  }
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-[10px] xs:text-xs sm:text-sm text-primary hover:underline break-words"
                >
                  {currentSchool.website}
                </a>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
