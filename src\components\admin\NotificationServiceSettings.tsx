import React, { useState, useEffect } from "react";
import { useToast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { useTranslation } from "react-i18next";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Loader2,
  Mail,
  MessageSquare,
  CheckCircle,
  AlertCircle,
  ToggleLeft,
  ToggleRight,
} from "lucide-react";
import {
  getEmailServiceConfig,
  getSMSServiceConfig,
  saveEmailServiceConfig,
  saveSMSServiceConfig,
  testEmailService,
  testSMSService,
} from "@/lib/services/external-notification-services";

export default function NotificationServiceSettings() {
  const { toast } = useToast();
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<string>("email");
  const [loading, setLoading] = useState<boolean>(true);
  const [testing, setTesting] = useState<boolean>(false);
  const [testEmail, setTestEmail] = useState<string>("");
  const [testPhone, setTestPhone] = useState<string>("");

  // Email service state
  const [emailConfig, setEmailConfig] = useState({
    apiKey: "",
    fromEmail: "",
    enabled: true, // Default to enabled for email
  });

  // SMS service state
  const [smsConfig, setSmsConfig] = useState({
    accountSid: "",
    authToken: "",
    phoneNumber: "",
    enabled: false, // Default to disabled for SMS
  });

  // Load configurations on component mount
  useEffect(() => {
    const loadConfigurations = async () => {
      setLoading(true);

      try {
        // Set default configurations first
        setEmailConfig({
          apiKey: "",
          fromEmail: "<EMAIL>",
          enabled: true, // Default to enabled for email
        });

        setSmsConfig({
          accountSid: "",
          authToken: "",
          phoneNumber: "",
          enabled: false, // Default to disabled for SMS
        });

        // Load email configuration
        const emailCfg = await getEmailServiceConfig();
        if (emailCfg) {
          setEmailConfig({
            apiKey: emailCfg.apiKey || "",
            fromEmail: emailCfg.fromEmail || "<EMAIL>",
            enabled: emailCfg.enabled !== undefined ? emailCfg.enabled : true, // Default to enabled if not specified
          });
        }

        // Load SMS configuration
        const smsCfg = await getSMSServiceConfig();
        if (smsCfg) {
          setSmsConfig({
            accountSid: smsCfg.accountSid || "",
            authToken: smsCfg.authToken || "",
            phoneNumber: smsCfg.phoneNumber || "",
            enabled: smsCfg.enabled !== undefined ? smsCfg.enabled : false, // Default to disabled if not specified
          });
        }

        // Show welcome message only on first load
        if (!emailCfg && !smsCfg) {
          toast({
            title: "Welcome to Email & SMS Settings",
            description:
              "Configure your email and SMS services here for parent notifications.",
          });
        }
      } catch (error) {
        console.error("Error loading configurations:", error);
        toast({
          title: "Error",
          description:
            "Failed to load saved configurations. Using default values.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    loadConfigurations();
  }, [toast]);

  // Handle email config changes
  const handleEmailConfigChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setEmailConfig((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle SMS config changes
  const handleSmsConfigChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setSmsConfig((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Save email configuration
  const saveEmailConfig = async () => {
    setLoading(true);
    try {
      // Ensure the email has a valid format
      if (!emailConfig.fromEmail.includes("@")) {
        toast({
          title: "Invalid Email",
          description:
            "Please enter a valid email address for the From Email field",
          variant: "destructive",
        });
        setLoading(false);
        return;
      }

      const success = await saveEmailServiceConfig(emailConfig);
      if (success) {
        toast({
          title: "Success",
          description: "Email service configuration saved successfully.",
        });
      } else {
        toast({
          title: "Error",
          description:
            "Failed to save email service configuration. Please try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error saving email configuration:", error);
      toast({
        title: "Error",
        description:
          "An unexpected error occurred while saving email configuration.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Save SMS configuration
  const saveSmsConfig = async () => {
    setLoading(true);
    try {
      // Validate phone number format
      if (!smsConfig.phoneNumber.startsWith("+")) {
        toast({
          title: "Invalid Phone Number",
          description:
            "Phone number must include country code and start with + (e.g., +**********)",
          variant: "destructive",
        });
        setLoading(false);
        return;
      }

      const success = await saveSMSServiceConfig(smsConfig);
      if (success) {
        toast({
          title: "Success",
          description: "SMS service configuration saved successfully.",
        });
      } else {
        toast({
          title: "Error",
          description:
            "Failed to save SMS service configuration. Please try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error saving SMS configuration:", error);
      toast({
        title: "Error",
        description:
          "An unexpected error occurred while saving SMS configuration.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Test email service
  const handleTestEmail = async () => {
    if (!testEmail) {
      toast({
        title: "Error",
        description: "Please enter a test email address",
        variant: "destructive",
      });
      return;
    }

    // Validate email format
    if (!testEmail.includes("@")) {
      toast({
        title: "Invalid Email",
        description: "Please enter a valid email address for testing",
        variant: "destructive",
      });
      return;
    }

    setTesting(true);
    try {
      // Check if configuration is saved and enabled
      if (!emailConfig.apiKey || !emailConfig.fromEmail) {
        toast({
          title: "Configuration Missing",
          description: "Please save your email configuration before testing",
          variant: "destructive",
        });
        setTesting(false);
        return;
      }

      // Check if email service is enabled
      if (!emailConfig.enabled) {
        toast({
          title: "Email Service Disabled",
          description: "Please enable email notifications before testing",
          variant: "destructive",
        });
        setTesting(false);
        return;
      }

      const success = await testEmailService(testEmail);
      if (success) {
        toast({
          title: "Success",
          description: "Test email sent successfully. Please check the inbox.",
        });
      } else {
        toast({
          title: "Error",
          description:
            "Failed to send test email. Please check your SendGrid API key and From Email address.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error testing email service:", error);
      toast({
        title: "Error",
        description:
          "An unexpected error occurred while sending the test email. Please check your network connection and try again.",
        variant: "destructive",
      });
    } finally {
      setTesting(false);
    }
  };

  // Test SMS service
  const handleTestSms = async () => {
    if (!testPhone) {
      toast({
        title: "Error",
        description: "Please enter a test phone number",
        variant: "destructive",
      });
      return;
    }

    // Validate phone number format
    if (!testPhone.startsWith("+")) {
      toast({
        title: "Invalid Phone Number",
        description:
          "Phone number must include country code and start with + (e.g., +**********)",
        variant: "destructive",
      });
      return;
    }

    setTesting(true);
    try {
      // Check if configuration is saved first
      if (
        !smsConfig.accountSid ||
        !smsConfig.authToken ||
        !smsConfig.phoneNumber
      ) {
        toast({
          title: "Configuration Missing",
          description: "Please save your SMS configuration before testing",
          variant: "destructive",
        });
        setTesting(false);
        return;
      }

      // Check if SMS service is enabled
      if (!smsConfig.enabled) {
        toast({
          title: "SMS Service Disabled",
          description: "Please enable SMS notifications before testing",
          variant: "destructive",
        });
        setTesting(false);
        return;
      }

      const success = await testSMSService(testPhone);
      if (success) {
        toast({
          title: "Success",
          description: "Test SMS sent successfully. Please check your phone.",
        });
      } else {
        toast({
          title: "Error",
          description:
            "Failed to send test SMS. Please check your Twilio credentials and phone number.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error testing SMS service:", error);
      toast({
        title: "Error",
        description:
          "An unexpected error occurred while sending the test SMS. Please check your network connection and try again.",
        variant: "destructive",
      });
    } finally {
      setTesting(false);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>
          {t("admin.parentNotifications.notificationServiceSettings")}
        </CardTitle>
        <CardDescription>
          {t("admin.parentNotifications.configureEmailSms")}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="email" className="flex items-center">
              <Mail className="mr-2 h-4 w-4" />
              {t("admin.parentNotifications.emailService")}
            </TabsTrigger>
            <TabsTrigger value="sms" className="flex items-center">
              <MessageSquare className="mr-2 h-4 w-4" />
              {t("admin.parentNotifications.smsService")}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="email" className="space-y-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="apiKey">
                  {t("admin.parentNotifications.sendgridApiKey")}
                </Label>
                <Input
                  id="apiKey"
                  name="apiKey"
                  type="password"
                  placeholder={t(
                    "admin.parentNotifications.enterSendgridApiKey"
                  )}
                  value={emailConfig.apiKey}
                  onChange={handleEmailConfigChange}
                  disabled={loading}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="fromEmail">
                  {t("admin.parentNotifications.fromEmailAddress")}
                </Label>
                <Input
                  id="fromEmail"
                  name="fromEmail"
                  type="email"
                  placeholder={t("admin.parentNotifications.enterFromEmail")}
                  value={emailConfig.fromEmail}
                  onChange={handleEmailConfigChange}
                  disabled={loading}
                />
                <p className="text-xs text-muted-foreground">
                  {t("admin.parentNotifications.emailVerificationRequired")}
                </p>
              </div>

              <div className="flex items-center space-x-2 pt-2">
                <Switch
                  id="emailEnabled"
                  checked={emailConfig.enabled}
                  onCheckedChange={(checked) =>
                    setEmailConfig({ ...emailConfig, enabled: checked })
                  }
                  disabled={loading}
                />
                <Label htmlFor="emailEnabled" className="cursor-pointer">
                  {emailConfig.enabled ? (
                    <span className="flex items-center text-green-600">
                      <ToggleRight className="mr-1 h-4 w-4" />
                      {t("admin.parentNotifications.emailNotificationsEnabled")}
                    </span>
                  ) : (
                    <span className="flex items-center text-gray-500">
                      <ToggleLeft className="mr-1 h-4 w-4" />
                      {t(
                        "admin.parentNotifications.emailNotificationsDisabled"
                      )}
                    </span>
                  )}
                </Label>
              </div>

              <Button
                onClick={saveEmailConfig}
                disabled={
                  loading || !emailConfig.apiKey || !emailConfig.fromEmail
                }
                className="mt-2"
              >
                {loading ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <CheckCircle className="mr-2 h-4 w-4" />
                )}
                {t("admin.parentNotifications.saveEmailConfiguration")}
              </Button>

              <div className="border-t pt-4 mt-4">
                <h3 className="text-sm font-medium mb-2">
                  {t("admin.parentNotifications.testEmailService")}
                </h3>
                <div className="flex space-x-2">
                  <Input
                    placeholder={t(
                      "admin.parentNotifications.enterTestEmailAddress"
                    )}
                    value={testEmail}
                    onChange={(e) => setTestEmail(e.target.value)}
                    disabled={testing}
                  />
                  <Button
                    onClick={handleTestEmail}
                    disabled={
                      testing ||
                      !testEmail ||
                      !emailConfig.apiKey ||
                      !emailConfig.fromEmail ||
                      !emailConfig.enabled
                    }
                  >
                    {testing ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      t("admin.parentNotifications.test")
                    )}
                  </Button>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="sms" className="space-y-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="accountSid">
                  {t("admin.parentNotifications.twilioAccountSid")}
                </Label>
                <Input
                  id="accountSid"
                  name="accountSid"
                  type="password"
                  placeholder={t(
                    "admin.parentNotifications.enterTwilioAccountSid"
                  )}
                  value={smsConfig.accountSid}
                  onChange={handleSmsConfigChange}
                  disabled={loading}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="authToken">
                  {t("admin.parentNotifications.twilioAuthToken")}
                </Label>
                <Input
                  id="authToken"
                  name="authToken"
                  type="password"
                  placeholder={t(
                    "admin.parentNotifications.enterTwilioAuthToken"
                  )}
                  value={smsConfig.authToken}
                  onChange={handleSmsConfigChange}
                  disabled={loading}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="phoneNumber">
                  {t("admin.parentNotifications.twilioPhoneNumber")}
                </Label>
                <Input
                  id="phoneNumber"
                  name="phoneNumber"
                  placeholder="+**********"
                  value={smsConfig.phoneNumber}
                  onChange={handleSmsConfigChange}
                  disabled={loading}
                />
                <p className="text-xs text-muted-foreground">
                  {t("admin.parentNotifications.enterTwilioPhoneNumber")}
                </p>
              </div>

              <div className="flex items-center space-x-2 pt-2">
                <Switch
                  id="smsEnabled"
                  checked={smsConfig.enabled}
                  onCheckedChange={(checked) =>
                    setSmsConfig({ ...smsConfig, enabled: checked })
                  }
                  disabled={loading}
                />
                <Label htmlFor="smsEnabled" className="cursor-pointer">
                  {smsConfig.enabled ? (
                    <span className="flex items-center text-green-600">
                      <ToggleRight className="mr-1 h-4 w-4" />
                      {t("admin.parentNotifications.smsNotificationsEnabled")}
                    </span>
                  ) : (
                    <span className="flex items-center text-gray-500">
                      <ToggleLeft className="mr-1 h-4 w-4" />
                      {t("admin.parentNotifications.smsNotificationsDisabled")}
                    </span>
                  )}
                </Label>
              </div>

              <Button
                onClick={saveSmsConfig}
                disabled={
                  loading ||
                  !smsConfig.accountSid ||
                  !smsConfig.authToken ||
                  !smsConfig.phoneNumber
                }
                className="mt-2"
              >
                {loading ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <CheckCircle className="mr-2 h-4 w-4" />
                )}
                {t("admin.parentNotifications.saveSmsConfiguration")}
              </Button>

              <div className="border-t pt-4 mt-4">
                <h3 className="text-sm font-medium mb-2">
                  {t("admin.parentNotifications.testSmsService")}
                </h3>
                <div className="flex space-x-2">
                  <Input
                    placeholder={t(
                      "admin.parentNotifications.enterTestPhoneNumber"
                    )}
                    value={testPhone}
                    onChange={(e) => setTestPhone(e.target.value)}
                    disabled={testing}
                  />
                  <Button
                    onClick={handleTestSms}
                    disabled={
                      testing ||
                      !testPhone ||
                      !smsConfig.accountSid ||
                      !smsConfig.authToken ||
                      !smsConfig.phoneNumber ||
                      !smsConfig.enabled
                    }
                  >
                    {testing ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      t("admin.parentNotifications.test")
                    )}
                  </Button>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between border-t pt-4">
        <p className="text-sm text-muted-foreground">
          <AlertCircle className="inline-block mr-1 h-4 w-4" />
          {t("admin.parentNotifications.apiKeysSecurelyStored")}
        </p>
      </CardFooter>
    </Card>
  );
}
