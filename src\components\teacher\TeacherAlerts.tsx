import { useState, useEffect } from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  Card<PERSON><PERSON>er,
  CardTitle,
} from "@/components/ui/card";
import { useTranslation } from "react-i18next";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Bell,
  Check,
  AlertTriangle,
  Clock,
  Calendar,
  Trash2,
} from "lucide-react";
import { format } from "date-fns";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/lib/supabase";
import { useAuth } from "@/context/AuthContext";
import { motion, AnimatePresence } from "framer-motion";

interface AlertMetadata {
  room_id?: string;
  room_name?: string;
  block_name?: string;
  verification_method?: string;
  previous_status?: string;
  timestamp?: string;
  attempt_type?: "override_attempt";
  manual_set_time?: string;
  category?: string;
  severity?: string;
  status?: string;
}

interface Alert {
  id: string;
  student_id: string;
  title: string;
  message: string;
  type: string;
  category?: string;
  metadata: AlertMetadata;
  created_at: string;
  read: boolean;
  distance_meters?: number;
  room_number?: string;
  severity?: string;
  status?: string;
  profiles?: {
    name: string;
    student_id: string;
    block_name?: string;
    room_number?: string;
  };
}

export default function TeacherAlerts() {
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();
  const { profile } = useAuth();
  const { t } = useTranslation();

  useEffect(() => {
    if (!profile?.id) return;

    fetchAlerts();
    // Set up real-time subscription
    const subscription = supabase
      .channel("teacher_alerts")
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "notifications",
          filter: `teacher_id=eq.${profile.id}`,
        },
        () => {
          fetchAlerts();
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [profile?.id]);

  const fetchAlerts = async () => {
    try {
      const { data, error } = await supabase
        .from("notifications")
        .select(
          `
          id,
          student_id,
          title,
          message,
          type,
          metadata,
          created_at,
          read,
          distance_meters,
          room_number,
          profiles:student_id (
            name,
            student_id
          )
        `
        )
        .eq("type", "distance_alert")
        .eq("teacher_id", profile?.id)
        .eq("read", false)
        .order("created_at", { ascending: false });

      if (error) throw error;

      // Parse metadata from JSON string and ensure correct typing
      const parsedData = (data || []).map((alert) => ({
        ...alert,
        metadata:
          typeof alert.metadata === "string"
            ? JSON.parse(alert.metadata)
            : alert.metadata,
        // Add default values for missing fields to maintain compatibility with the Alert interface
        category: alert.metadata?.category || "",
        severity: alert.metadata?.severity || "medium",
        status: alert.metadata?.status || "active",
        profiles: {
          ...alert.profiles,
          block_name: alert.metadata?.block_name || "",
          room_number: alert.room_number || alert.metadata?.room_name || "",
        },
      })) as Alert[];

      setAlerts(parsedData);
    } catch (error) {
      console.error("Error fetching alerts:", error);
      toast({
        title: t("common.error"),
        description: t("teacher.alerts.failedToLoad"),
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const markAsRead = async (alertId: string) => {
    try {
      // Delete the alert instead of marking it as read
      const { error } = await supabase
        .from("notifications")
        .delete()
        .eq("id", alertId);

      if (error) throw error;

      // Remove the alert from the list with animation
      setAlerts((prev) => prev.filter((alert) => alert.id !== alertId));

      toast({
        title: t("teacher.alerts.alertDismissed"),
        description: t("teacher.alerts.alertDeleted"),
        variant: "default",
      });
    } catch (error) {
      console.error("Error deleting alert:", error);
      toast({
        title: t("common.error"),
        description: t("teacher.alerts.errorDeletingAlert"),
        variant: "destructive",
      });
    }
  };

  const clearAllAlerts = async () => {
    try {
      // Delete all alerts instead of marking them as read
      const { error } = await supabase
        .from("notifications")
        .delete()
        .eq("type", "distance_alert")
        .eq("teacher_id", profile?.id)
        .is("read", false);

      if (error) throw error;

      setAlerts([]);

      toast({
        title: t("teacher.alerts.alertsCleared"),
        description: t("teacher.alerts.allAlertsDeleted"),
        variant: "default",
      });
    } catch (error) {
      console.error("Error deleting alerts:", error);
      toast({
        title: t("common.error"),
        description: t("teacher.alerts.errorDeletingAlerts"),
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-32">
        <LoadingSpinner message="Loading alerts..." />
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>{t("teacher.alerts.distanceAlerts")}</CardTitle>
            <CardDescription>
              {t("teacher.alerts.studentDistanceVerificationAlerts")}
            </CardDescription>
          </div>
          {alerts.length > 0 && (
            <Button
              variant="outline"
              onClick={clearAllAlerts}
              title={t("teacher.alerts.deleteAllTitle")}
            >
              <Trash2 className="w-4 h-4 mr-2" />
              {t("teacher.alerts.deleteAll")}
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {alerts.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Bell className="w-12 h-12 mx-auto mb-2 opacity-30" />
            <p>{t("teacher.alerts.noDistanceAlerts")}</p>
          </div>
        ) : (
          <div className="space-y-4">
            <AnimatePresence>
              {alerts.map((alert) => (
                <motion.div
                  key={alert.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, x: -100 }}
                  className="border rounded-lg p-4 relative hover:border-primary/50 transition-colors"
                >
                  <div className="flex items-start justify-between gap-4">
                    <div className="space-y-1 flex-1">
                      <div className="flex items-center gap-2">
                        <h4 className="font-medium">{alert.title}</h4>
                        <Badge variant="destructive">
                          {t("teacher.alerts.distanceAlert")}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {alert.message}
                      </p>
                      <div className="flex flex-wrap gap-2 mt-2">
                        <Badge variant="outline" className="text-xs">
                          <Clock className="w-3 h-3 mr-1" />
                          {format(new Date(alert.created_at), "MMM d, h:mm a")}
                        </Badge>
                        {(alert.severity || alert.metadata?.severity) && (
                          <Badge variant="outline" className="text-xs">
                            {t("teacher.alerts.severity")}:{" "}
                            {alert.severity ||
                              alert.metadata?.severity ||
                              "medium"}
                          </Badge>
                        )}
                        {alert.distance_meters && (
                          <Badge variant="outline" className="text-xs">
                            {t("teacher.alerts.distance")}:{" "}
                            {Math.round(alert.distance_meters)}m
                          </Badge>
                        )}
                        {alert.profiles && alert.profiles.room_number && (
                          <Badge variant="outline" className="text-xs">
                            {t("teacher.alerts.room")}:{" "}
                            {alert.profiles.room_number}
                          </Badge>
                        )}
                        {alert.room_number && !alert.profiles?.room_number && (
                          <Badge variant="outline" className="text-xs">
                            {t("teacher.alerts.room")}: {alert.room_number}
                          </Badge>
                        )}
                      </div>
                    </div>
                    <Button
                      size="icon"
                      variant="ghost"
                      onClick={() => markAsRead(alert.id)}
                      className="shrink-0"
                      title={t("teacher.alerts.deleteAlertTitle")}
                    >
                      <Check className="w-4 h-4" />
                    </Button>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
