import { useState, useEffect, useRef } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import QRCode from "react-qr-code";
import { websocketService, type QRUpdate, type AttendanceUpdate } from "@/lib/services/websocket-service";
import { supabase } from "@/integrations/supabase/client";
import { Clock, Users, Wifi, WifiOff, Shield, CheckCircle } from "lucide-react";

interface TabletQRDisplayProps {
  roomId: string;
  schoolId: string;
}

interface RoomInfo {
  id: string;
  name: string;
  building?: string;
  floor?: number;
  capacity: number;
  block_name?: string;
}

export default function TabletQRDisplay({ roomId, schoolId }: TabletQRDisplayProps) {
  // QR Code state
  const [qrData, setQrData] = useState<string>("");
  const [qrExpiry, setQrExpiry] = useState<Date | null>(null);
  const [timeLeft, setTimeLeft] = useState<number>(0);
  const [isActive, setIsActive] = useState(false);

  // Room and attendance state
  const [roomInfo, setRoomInfo] = useState<RoomInfo | null>(null);
  const [attendanceCount, setAttendanceCount] = useState<number>(0);
  const [recentAttendance, setRecentAttendance] = useState<AttendanceUpdate[]>([]);
  
  // Connection state
  const [isConnected, setIsConnected] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  
  // Auto-refresh timer
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // Fetch room information
  useEffect(() => {
    const fetchRoomInfo = async () => {
      try {
        const { data: room, error } = await supabase
          .from("rooms")
          .select(`
            id,
            name,
            building,
            floor,
            capacity,
            current_qr_code,
            qr_expiry,
            blocks (
              name
            )
          `)
          .eq("id", roomId)
          .single();

        if (error) throw error;

        setRoomInfo({
          id: room.id,
          name: room.name,
          building: room.building,
          floor: room.floor,
          capacity: room.capacity,
          block_name: room.blocks?.name,
        });

        // Load existing QR code if available
        if (room.current_qr_code && room.qr_expiry) {
          const expiry = new Date(room.qr_expiry);
          if (expiry.getTime() > Date.now()) {
            setQrData(room.current_qr_code);
            setQrExpiry(expiry);
            setIsActive(true);
          }
        }
      } catch (error) {
        console.error("Error fetching room info:", error);
      }
    };

    fetchRoomInfo();
  }, [roomId]);

  // Subscribe to real-time QR updates
  useEffect(() => {
    const unsubscribeQR = websocketService.subscribeToQRUpdates(
      schoolId,
      (update: QRUpdate) => {
        if (update.data.room_id === roomId) {
          console.log("QR Update received:", update);
          
          if (update.type === 'qr_generated' || update.type === 'qr_refreshed') {
            // Fetch the latest QR code from database
            fetchLatestQRCode();
          } else if (update.type === 'qr_expired') {
            setIsActive(false);
            setQrData("");
            setQrExpiry(null);
          }
          
          setLastUpdate(new Date());
          setIsConnected(true);
        }
      },
      roomId
    );

    return unsubscribeQR;
  }, [schoolId, roomId]);

  // Subscribe to real-time attendance updates
  useEffect(() => {
    const unsubscribeAttendance = websocketService.subscribeToAttendance(
      schoolId,
      (update: AttendanceUpdate) => {
        if (update.data.room_id === roomId) {
          console.log("Attendance update received:", update);
          
          // Add to recent attendance
          setRecentAttendance(prev => [update, ...prev.slice(0, 4)]); // Keep last 5
          setAttendanceCount(prev => prev + 1);
          
          // Show visual feedback
          showAttendanceNotification(update.data.student_name || "Student");
        }
      },
      roomId
    );

    return unsubscribeAttendance;
  }, [schoolId, roomId]);

  // Fetch latest QR code from database
  const fetchLatestQRCode = async () => {
    try {
      const { data: room, error } = await supabase
        .from("rooms")
        .select("current_qr_code, qr_expiry")
        .eq("id", roomId)
        .single();

      if (error) throw error;

      if (room.current_qr_code && room.qr_expiry) {
        const expiry = new Date(room.qr_expiry);
        if (expiry.getTime() > Date.now()) {
          setQrData(room.current_qr_code);
          setQrExpiry(expiry);
          setIsActive(true);
          setTimeLeft(Math.floor((expiry.getTime() - Date.now()) / 1000));
        }
      }
    } catch (error) {
      console.error("Error fetching QR code:", error);
      setIsConnected(false);
    }
  };

  // Timer for countdown and auto-refresh
  useEffect(() => {
    if (qrExpiry && isActive) {
      timerRef.current = setInterval(() => {
        const remaining = Math.max(0, Math.floor((qrExpiry.getTime() - Date.now()) / 1000));
        setTimeLeft(remaining);

        if (remaining <= 0) {
          setIsActive(false);
          setQrData("");
          setQrExpiry(null);
        }
      }, 1000);

      return () => {
        if (timerRef.current) {
          clearInterval(timerRef.current);
        }
      };
    }
  }, [qrExpiry, isActive]);

  // Show attendance notification
  const showAttendanceNotification = (studentName: string) => {
    // Create a temporary visual notification
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 bg-green-500 text-white p-4 rounded-lg shadow-lg z-50 animate-pulse';
    notification.innerHTML = `
      <div class="flex items-center gap-2">
        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
        </svg>
        <span>${studentName} checked in!</span>
      </div>
    `;
    
    document.body.appendChild(notification);
    
    // Remove after 3 seconds
    setTimeout(() => {
      document.body.removeChild(notification);
    }, 3000);
  };

  // Format time remaining
  const formatTimeLeft = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  // Connection status indicator
  const ConnectionStatus = () => (
    <div className="flex items-center gap-2 text-sm">
      {isConnected ? (
        <>
          <Wifi className="w-4 h-4 text-green-600" />
          <span className="text-green-600">Connected</span>
        </>
      ) : (
        <>
          <WifiOff className="w-4 h-4 text-red-600" />
          <span className="text-red-600">Disconnected</span>
        </>
      )}
      <span className="text-gray-500">
        Last update: {lastUpdate.toLocaleTimeString()}
      </span>
    </div>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle className="text-2xl font-bold text-gray-800">
                  {roomInfo?.name || "Loading..."}
                </CardTitle>
                <p className="text-gray-600">
                  {roomInfo?.block_name && `Block ${roomInfo.block_name}`}
                  {roomInfo?.building && ` • ${roomInfo.building}`}
                  {roomInfo?.floor && ` • Floor ${roomInfo.floor}`}
                </p>
              </div>
              <div className="text-right">
                <ConnectionStatus />
                <div className="flex items-center gap-2 mt-1">
                  <Users className="w-4 h-4 text-blue-600" />
                  <span className="text-sm text-gray-600">
                    Capacity: {roomInfo?.capacity || 0}
                  </span>
                </div>
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* QR Code Display */}
        <Card className="text-center">
          <CardHeader>
            <CardTitle className="flex items-center justify-center gap-2">
              <Shield className="w-6 h-6 text-blue-600" />
              Attendance QR Code
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {isActive && qrData ? (
              <>
                {/* QR Code */}
                <div className="flex justify-center">
                  <div className="bg-white p-8 rounded-2xl shadow-lg border-4 border-blue-200">
                    <QRCode
                      value={qrData}
                      size={300}
                      style={{ height: "auto", maxWidth: "100%", width: "100%" }}
                    />
                  </div>
                </div>

                {/* Timer */}
                <div className="space-y-2">
                  <div className="flex items-center justify-center gap-2">
                    <Clock className="w-5 h-5 text-blue-600" />
                    <span className="text-lg font-medium">Time Remaining:</span>
                  </div>
                  <div className={`text-4xl font-bold ${
                    timeLeft < 60 ? "text-red-600 animate-pulse" : "text-blue-600"
                  }`}>
                    {formatTimeLeft(timeLeft)}
                  </div>
                  <Badge variant={timeLeft < 60 ? "destructive" : "default"}>
                    {timeLeft < 60 ? "Expiring Soon" : "Active"}
                  </Badge>
                </div>

                {/* Instructions */}
                <div className="bg-blue-50 p-4 rounded-lg">
                  <p className="text-blue-800 font-medium">
                    📱 Scan this QR code with your phone to mark attendance
                  </p>
                  <p className="text-blue-600 text-sm mt-1">
                    Make sure you're in your assigned room before scanning
                  </p>
                </div>
              </>
            ) : (
              <div className="py-12">
                <div className="text-gray-400 mb-4">
                  <Shield className="w-16 h-16 mx-auto" />
                </div>
                <h3 className="text-xl font-medium text-gray-600 mb-2">
                  No Active QR Code
                </h3>
                <p className="text-gray-500">
                  Waiting for attendance session to begin...
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Live Attendance Feed */}
        {recentAttendance.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="w-5 h-5 text-green-600" />
                Recent Check-ins ({attendanceCount})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {recentAttendance.map((attendance, index) => (
                  <div
                    key={index}
                    className="flex justify-between items-center p-3 bg-green-50 rounded-lg border border-green-200"
                  >
                    <span className="font-medium text-green-800">
                      {attendance.data.student_name}
                    </span>
                    <span className="text-green-600 text-sm">
                      {new Date(attendance.data.timestamp).toLocaleTimeString()}
                    </span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
