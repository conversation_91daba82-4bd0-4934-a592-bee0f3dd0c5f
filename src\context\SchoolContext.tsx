import React, { createContext, useContext, useState, useEffect } from "react";
import { useAuth } from "./AuthContext";
import { School } from "@/lib/types";
import { getSchoolById, isSystemAdmin } from "@/lib/utils/school-context";
import { supabase } from "@/lib/supabase";

interface SchoolContextType {
  currentSchool: School | null;
  isSystemAdmin: boolean;
  loading: boolean;
  setCurrentSchool: (school: School | null) => void;
  switchSchool: (schoolId: string) => Promise<void>;
  refreshSchool: () => Promise<void>;
}

const SchoolContext = createContext<SchoolContextType | undefined>(undefined);

export const useSchool = () => {
  const context = useContext(SchoolContext);
  if (context === undefined) {
    throw new Error("useSchool must be used within a SchoolProvider");
  }
  return context;
};

export const SchoolProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const { profile } = useAuth();
  const [currentSchool, setCurrentSchool] = useState<School | null>(null);
  const [isSystemAdminUser, setIsSystemAdminUser] = useState(false);
  const [loading, setLoading] = useState(true);

  // Fetch the current school when the profile changes
  useEffect(() => {
    if (profile) {
      fetchCurrentSchool();
      checkSystemAdmin();
    } else {
      setCurrentSchool(null);
      setLoading(false);
    }
  }, [profile]);

  // Check if the current user is a system admin
  const checkSystemAdmin = async () => {
    if (profile) {
      const isAdmin = await isSystemAdmin(profile.id);
      setIsSystemAdminUser(isAdmin);
    }
  };

  // Fetch the current school based on the user's profile
  const fetchCurrentSchool = async () => {
    setLoading(true);
    try {
      if (profile?.school_id) {
        // Try to get school data without using single()
        const { data, error } = await supabase
          .from("schools")
          .select("*")
          .eq("id", profile.school_id);

        if (error) {
          console.error("Error fetching school:", error);
          createDefaultSchool(profile.school_id);
          return;
        }

        // If we got results, use the first one
        if (data && data.length > 0) {
          const school = data[0];
          setCurrentSchool(school);
        } else {
          createDefaultSchool(profile.school_id);
        }
      } else {
        setCurrentSchool(null);
      }
    } catch (error) {
      console.error("Error fetching school:", error);
      if (profile?.school_id) {
        createDefaultSchool(profile.school_id);
      }
    } finally {
      setLoading(false);
    }
  };

  // Helper function to create a default school
  const createDefaultSchool = (schoolId: string) => {
    setCurrentSchool({
      id: schoolId,
      name: "Default School",
      address: "",
      city: "",
      state: "",
      zip: "",
      country: "",
      phone: "",
      email: "",
      website: "",
      logoUrl: null,
      primaryColor: "#4f46e5", // Default indigo
      secondaryColor: "#f97316", // Default orange
      invitationCode: "",
      isActive: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    });
  };

  // Switch to a different school (for system admins)
  const switchSchool = async (schoolId: string) => {
    if (!isSystemAdminUser) {
      return;
    }

    setLoading(true);
    try {
      // Try to get school data without using single()
      const { data, error } = await supabase
        .from("schools")
        .select("*")
        .eq("id", schoolId);

      if (error) {
        console.error("Error switching school:", error);
        createDefaultSchool(schoolId);
        return;
      }

      // If we got results, use the first one
      if (data && data.length > 0) {
        const school = data[0];
        setCurrentSchool(school);
      } else {
        createDefaultSchool(schoolId);
      }
    } catch (error) {
      console.error("Error switching school:", error);
      createDefaultSchool(schoolId);
    } finally {
      setLoading(false);
    }
  };

  // Refresh the current school data
  const refreshSchool = async () => {
    if (profile?.school_id) {
      setLoading(true);
      try {
        // Try to get school data without using single()
        const { data, error } = await supabase
          .from("schools")
          .select("*")
          .eq("id", profile.school_id);

        if (error) {
          console.error("Error refreshing school:", error);
          // Keep the current school if it exists, otherwise create a default one
          if (!currentSchool) {
            createDefaultSchool(profile.school_id);
          }
          return;
        }

        // If we got results, use the first one
        if (data && data.length > 0) {
          const school = data[0];
          setCurrentSchool(school);
        } else {
          // Keep the current school if it exists, otherwise create a default one
          if (!currentSchool) {
            createDefaultSchool(profile.school_id);
          }
        }
      } catch (error) {
        console.error("Error refreshing school:", error);
        // Keep the current school if it exists
      } finally {
        setLoading(false);
      }
    }
  };

  // Set up real-time subscription for school changes
  useEffect(() => {
    if (!profile?.school_id) return;

    const schoolSubscription = supabase
      .channel("schools-changes")
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "schools",
          filter: `id=eq.${profile.school_id}`,
        },
        () => {
          refreshSchool();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(schoolSubscription);
    };
  }, [profile?.school_id]);

  return (
    <SchoolContext.Provider
      value={{
        currentSchool,
        isSystemAdmin: isSystemAdminUser,
        loading,
        setCurrentSchool,
        switchSchool,
        refreshSchool,
      }}
    >
      {children}
    </SchoolContext.Provider>
  );
};
