/**
 * 🎨 Language-Aware Branding Hook
 * =====================================================
 * This hook provides branding information based on the current language.
 * It automatically switches between English and Turkish branding.
 */

import { useTranslation } from 'react-i18next';
import { 
  getBranding, 
  getUIText, 
  getEmailTemplates, 
  getSEO,
  BRANDING_EN,
  BRANDING_TR 
} from '@/config/branding';

export const useBranding = () => {
  const { i18n } = useTranslation();
  const currentLanguage = i18n.language || 'en';

  // Get language-specific branding
  const branding = getBranding(currentLanguage);
  const uiText = getUIText(currentLanguage);
  const emailTemplates = getEmailTemplates(currentLanguage);
  const seo = getSEO(currentLanguage);

  return {
    // Current language
    language: currentLanguage,
    
    // Language-specific branding
    branding,
    uiText,
    emailTemplates,
    seo,
    
    // Direct access to language-specific branding
    english: BRANDING_EN,
    turkish: BRANDING_TR,
    
    // Helper functions
    isEnglish: currentLanguage === 'en',
    isTurkish: currentLanguage === 'tr',
    
    // Get branding for specific language
    getBrandingFor: (lang: string) => getBranding(lang),
    getUITextFor: (lang: string) => getUIText(lang),
    getEmailTemplatesFor: (lang: string) => getEmailTemplates(lang),
    getSEOFor: (lang: string) => getSEO(lang),
  };
};

// Export type for TypeScript
export type UseBrandingReturn = ReturnType<typeof useBranding>;
