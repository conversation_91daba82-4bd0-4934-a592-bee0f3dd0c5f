# 🎨 App Branding Configuration Guide

## 🚀 Quick Start: How to Change App Name

### **Method 1: Edit .env File (RECOMMENDED)**

1. **Open `.env` file** in your project root
2. **Find the branding section** (lines 7-14):
   ```bash
   VITE_APP_NAME="Attendance Tracking System"
   VITE_APP_SHORT_NAME="ATS"
   VITE_APP_DESCRIPTION="Secure and efficient attendance tracking system for educational institutions"
   VITE_COMPANY_NAME="Attendance Tracking System Team"
   VITE_COMPANY_WEBSITE="https://attendancetracking.edu"
   VITE_CONTACT_EMAIL="<EMAIL>"
   VITE_SUPPORT_EMAIL="<EMAIL>"
   VITE_NOREPLY_EMAIL="<EMAIL>"
   ```

3. **Change the values** to your desired app name:
   ```bash
   VITE_APP_NAME="SchoolTracker Pro"
   VITE_APP_SHORT_NAME="STP"
   VITE_APP_DESCRIPTION="Advanced school management and tracking system"
   VITE_COMPANY_NAME="SchoolTracker Team"
   VITE_COMPANY_WEBSITE="https://schooltracker.com"
   VITE_CONTACT_EMAIL="<EMAIL>"
   VITE_SUPPORT_EMAIL="<EMAIL>"
   VITE_NOREPLY_EMAIL="<EMAIL>"
   ```

4. **Restart your development server**:
   ```bash
   npm run dev
   ```

5. **That's it!** ✨ Your entire app is now rebranded!

---

## 📋 What Gets Updated Automatically

When you change the `.env` values, these parts of your app update automatically:

### **🌐 Frontend:**
- ✅ **Browser tab title**
- ✅ **Login page header**
- ✅ **Loading screen message**
- ✅ **All UI text using translations**
- ✅ **Meta tags for SEO**

### **📧 Backend:**
- ✅ **Email templates and signatures**
- ✅ **Footer settings defaults**
- ✅ **Contact information**
- ✅ **System notifications**

### **🔧 Technical:**
- ✅ **HTML meta tags**
- ✅ **Open Graph tags (social media)**
- ✅ **Translation interpolation**
- ✅ **API client identifiers**

---

## 🛠️ Advanced Configuration

### **Custom Branding Variables:**

The system supports these environment variables:

| Variable | Purpose | Example |
|----------|---------|---------|
| `VITE_APP_NAME` | Main app name | "SchoolTracker Pro" |
| `VITE_APP_SHORT_NAME` | Abbreviation | "STP" |
| `VITE_APP_DESCRIPTION` | SEO description | "Advanced school management system" |
| `VITE_COMPANY_NAME` | Company/team name | "SchoolTracker Team" |
| `VITE_COMPANY_WEBSITE` | Company website | "https://schooltracker.com" |
| `VITE_CONTACT_EMAIL` | General contact | "<EMAIL>" |
| `VITE_SUPPORT_EMAIL` | Support contact | "<EMAIL>" |
| `VITE_NOREPLY_EMAIL` | System emails | "<EMAIL>" |

### **Validation Rules:**

- ✅ **App Name**: Max 50 characters
- ✅ **Short Name**: Max 10 characters  
- ✅ **Emails**: Must be valid email format
- ✅ **URLs**: Must be valid URL format

---

## 🔍 Troubleshooting

### **Changes Not Showing?**

1. **Restart dev server**: `npm run dev`
2. **Clear browser cache**: Ctrl+F5 (Windows) or Cmd+Shift+R (Mac)
3. **Check console**: Look for any error messages

### **Validation Errors?**

Open browser console and run:
```javascript
import { DEV_INFO } from './src/config/branding';
DEV_INFO.VALIDATE();
```

### **Debug Current Config:**

```javascript
import { DEV_INFO } from './src/config/branding';
DEV_INFO.ENV_CHECK();
```

---

## 📁 File Structure

```
src/
├── config/
│   └── branding.ts          # 🎨 Main branding configuration
├── i18n/
│   ├── index.ts             # 🌐 Translation setup with interpolation
│   └── locales/
│       ├── en.json          # 🇺🇸 English translations
│       └── tr.json          # 🇹🇷 Turkish translations
└── components/              # 🧩 Components using branding
.env                         # ⚙️ Environment variables (EDIT THIS!)
index.html                   # 🌐 HTML meta tags
```

---

## 🎯 Examples

### **Example 1: School Management System**
```bash
VITE_APP_NAME="EduManage Pro"
VITE_APP_SHORT_NAME="EMP"
VITE_COMPANY_NAME="EduManage Solutions"
VITE_CONTACT_EMAIL="<EMAIL>"
```

### **Example 2: University Tracker**
```bash
VITE_APP_NAME="UniTrack System"
VITE_APP_SHORT_NAME="UTS"
VITE_COMPANY_NAME="UniTrack Technologies"
VITE_CONTACT_EMAIL="<EMAIL>"
```

### **Example 3: Corporate Training**
```bash
VITE_APP_NAME="TrainingHub Enterprise"
VITE_APP_SHORT_NAME="THE"
VITE_COMPANY_NAME="TrainingHub Corp"
VITE_CONTACT_EMAIL="<EMAIL>"
```

---

## ⚡ Quick Commands

```bash
# Start development with new branding
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

---

## 🎉 That's It!

Your app is now fully configurable with just a few environment variable changes. No more hunting through dozens of files to update the app name! 

**Happy branding!** 🚀
