# 🎨 App Branding Configuration Guide

## 🚀 Quick Start: How to Change App Name

### **Method 1: Edit .env File (RECOMMENDED)**

1. **Open `.env` file** in your project root
2. **Find the branding section** (lines 7-25):

   **🇺🇸 English Branding:**

   ```bash
   VITE_APP_NAME="Attendance Tracking System"
   VITE_APP_SHORT_NAME="ATS"
   VITE_APP_DESCRIPTION="Secure and efficient attendance tracking system for educational institutions"
   VITE_COMPANY_NAME="Attendance Tracking System Team"
   VITE_COMPANY_WEBSITE="https://attendancetracking.edu"
   VITE_CONTACT_EMAIL="<EMAIL>"
   VITE_SUPPORT_EMAIL="<EMAIL>"
   VITE_NOREPLY_EMAIL="<EMAIL>"
   ```

   **🇹🇷 Turkish Branding:**

   ```bash
   VITE_APP_NAME_TR="Devam Takip Sistemi"
   VITE_APP_SHORT_NAME_TR="DTS"
   VITE_APP_DESCRIPTION_TR="Eğitim kurumları için güvenli ve etkili devam takip sistemi"
   VITE_COMPANY_NAME_TR="Devam Takip Sistemi Ekibi"
   VITE_COMPANY_WEBSITE_TR="https://devamtakip.edu.tr"
   VITE_CONTACT_EMAIL_TR="<EMAIL>"
   VITE_SUPPORT_EMAIL_TR="<EMAIL>"
   VITE_NOREPLY_EMAIL_TR="<EMAIL>"
   ```

3. **Change the values** to your desired app names:

   **🇺🇸 English Example:**

   ```bash
   VITE_APP_NAME="SchoolTracker Pro"
   VITE_APP_SHORT_NAME="STP"
   VITE_APP_DESCRIPTION="Advanced school management and tracking system"
   VITE_COMPANY_NAME="SchoolTracker Team"
   VITE_COMPANY_WEBSITE="https://schooltracker.com"
   VITE_CONTACT_EMAIL="<EMAIL>"
   VITE_SUPPORT_EMAIL="<EMAIL>"
   VITE_NOREPLY_EMAIL="<EMAIL>"
   ```

   **🇹🇷 Turkish Example:**

   ```bash
   VITE_APP_NAME_TR="Okul Takip Pro"
   VITE_APP_SHORT_NAME_TR="OTP"
   VITE_APP_DESCRIPTION_TR="Gelişmiş okul yönetimi ve takip sistemi"
   VITE_COMPANY_NAME_TR="Okul Takip Ekibi"
   VITE_COMPANY_WEBSITE_TR="https://okultakip.com.tr"
   VITE_CONTACT_EMAIL_TR="<EMAIL>"
   VITE_SUPPORT_EMAIL_TR="<EMAIL>"
   VITE_NOREPLY_EMAIL_TR="<EMAIL>"
   ```

4. **Restart your development server**:

   ```bash
   npm run dev
   ```

5. **That's it!** ✨ Your entire app is now rebranded in both languages!

---

## 📋 What Gets Updated Automatically

When you change the `.env` values, these parts of your app update automatically:

### **🌐 Frontend:**

- ✅ **Browser tab title**
- ✅ **Login page header**
- ✅ **Loading screen message**
- ✅ **All UI text using translations**
- ✅ **Meta tags for SEO**

### **📧 Backend:**

- ✅ **Email templates and signatures**
- ✅ **Footer settings defaults**
- ✅ **Contact information**
- ✅ **System notifications**

### **🔧 Technical:**

- ✅ **HTML meta tags** (Dynamic based on environment variables)
- ✅ **Open Graph tags** (Social media previews in correct language)
- ✅ **Translation interpolation** (Automatic branding variable replacement)
- ✅ **Language detection** (Browser language determines initial branding)

### **🇹🇷 Turkish-Specific Features:**

- ✅ **Culturally appropriate naming** (e.g., "Devam Takip" instead of direct translation)
- ✅ **Turkish domain support** (.edu.tr vs .edu)
- ✅ **Turkish email conventions** ("merhaba@" vs "hello@", "destek@" vs "support@")
- ✅ **Proper Turkish UI text** ("yükleniyor..." vs "Loading...")
- ✅ **Turkish copyright text** ("Tüm hakları saklıdır" vs "All rights reserved")
- ✅ **Language-aware email templates** (Turkish signatures and support messages)

---

## 🛠️ Advanced Configuration

### **Custom Branding Variables:**

The system supports these environment variables:

| Variable               | Purpose           | Example                             |
| ---------------------- | ----------------- | ----------------------------------- |
| `VITE_APP_NAME`        | Main app name     | "SchoolTracker Pro"                 |
| `VITE_APP_SHORT_NAME`  | Abbreviation      | "STP"                               |
| `VITE_APP_DESCRIPTION` | SEO description   | "Advanced school management system" |
| `VITE_COMPANY_NAME`    | Company/team name | "SchoolTracker Team"                |
| `VITE_COMPANY_WEBSITE` | Company website   | "https://schooltracker.com"         |
| `VITE_CONTACT_EMAIL`   | General contact   | "<EMAIL>"           |
| `VITE_SUPPORT_EMAIL`   | Support contact   | "<EMAIL>"         |
| `VITE_NOREPLY_EMAIL`   | System emails     | "<EMAIL>"         |

### **Validation Rules:**

- ✅ **App Name**: Max 50 characters
- ✅ **Short Name**: Max 10 characters
- ✅ **Emails**: Must be valid email format
- ✅ **URLs**: Must be valid URL format

---

## 🔍 Troubleshooting

### **Changes Not Showing?**

1. **Restart dev server**: `npm run dev`
2. **Clear browser cache**: Ctrl+F5 (Windows) or Cmd+Shift+R (Mac)
3. **Check console**: Look for any error messages

### **Validation Errors?**

Open browser console and run:

```javascript
import { DEV_INFO } from "./src/config/branding";
DEV_INFO.VALIDATE();
```

### **Debug Current Config:**

```javascript
import { DEV_INFO } from "./src/config/branding";
DEV_INFO.ENV_CHECK();
```

---

## 📁 File Structure

```
src/
├── config/
│   └── branding.ts          # 🎨 Main branding configuration
├── i18n/
│   ├── index.ts             # 🌐 Translation setup with interpolation
│   └── locales/
│       ├── en.json          # 🇺🇸 English translations
│       └── tr.json          # 🇹🇷 Turkish translations
└── components/              # 🧩 Components using branding
.env                         # ⚙️ Environment variables (EDIT THIS!)
index.html                   # 🌐 HTML meta tags
```

---

## 🎯 Examples

### **Example 1: School Management System**

**🇺🇸 English:**

```bash
VITE_APP_NAME="EduManage Pro"
VITE_APP_SHORT_NAME="EMP"
VITE_COMPANY_NAME="EduManage Solutions"
VITE_CONTACT_EMAIL="<EMAIL>"
```

**🇹🇷 Turkish:**

```bash
VITE_APP_NAME_TR="EduYönetim Pro"
VITE_APP_SHORT_NAME_TR="EYP"
VITE_COMPANY_NAME_TR="EduYönetim Çözümleri"
VITE_CONTACT_EMAIL_TR="<EMAIL>"
```

### **Example 2: University Tracker**

**🇺🇸 English:**

```bash
VITE_APP_NAME="UniTrack System"
VITE_APP_SHORT_NAME="UTS"
VITE_COMPANY_NAME="UniTrack Technologies"
VITE_CONTACT_EMAIL="<EMAIL>"
```

**🇹🇷 Turkish:**

```bash
VITE_APP_NAME_TR="Üniversite Takip Sistemi"
VITE_APP_SHORT_NAME_TR="ÜTS"
VITE_COMPANY_NAME_TR="Üniversite Takip Teknolojileri"
VITE_CONTACT_EMAIL_TR="<EMAIL>"
```

### **Example 3: Corporate Training**

**🇺🇸 English:**

```bash
VITE_APP_NAME="TrainingHub Enterprise"
VITE_APP_SHORT_NAME="THE"
VITE_COMPANY_NAME="TrainingHub Corp"
VITE_CONTACT_EMAIL="<EMAIL>"
```

**🇹🇷 Turkish:**

```bash
VITE_APP_NAME_TR="Eğitim Merkezi Kurumsal"
VITE_APP_SHORT_NAME_TR="EMK"
VITE_COMPANY_NAME_TR="Eğitim Merkezi A.Ş."
VITE_CONTACT_EMAIL_TR="<EMAIL>"
```

---

## ⚡ Quick Commands

```bash
# Start development with new branding
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

---

## 🎉 That's It!

Your app is now fully configurable with just a few environment variable changes. No more hunting through dozens of files to update the app name!

### **🌟 Key Benefits:**

- ✅ **Dual Language Support** - English and Turkish branding
- ✅ **5-Second Changes** - Edit `.env` and restart server
- ✅ **Cultural Appropriateness** - Proper Turkish naming conventions
- ✅ **Developer Friendly** - Perfect for experimentation and finalization
- ✅ **Production Ready** - Robust, type-safe, and well-documented

**Happy branding in both languages!** 🇺🇸🇹🇷 🚀
