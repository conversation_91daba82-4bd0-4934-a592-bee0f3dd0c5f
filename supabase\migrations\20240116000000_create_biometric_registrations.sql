-- Create biometric registrations table
CREATE TABLE IF NOT EXISTS biometric_registrations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    type TEXT NOT NULL CHECK (type IN ('fingerprint', 'face', 'voice', 'iris')),
    template_hash TEXT NOT NULL,
    confidence_threshold DECIMAL(3,2) DEFAULT 0.85,
    device_info TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure one active registration per user per type
    UNIQUE(user_id, type, is_active) DEFERRABLE INITIALLY DEFERRED
);

-- <PERSON>reate index for faster lookups
CREATE INDEX IF NOT EXISTS idx_biometric_registrations_user_type 
ON biometric_registrations(user_id, type) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_biometric_registrations_created_at 
ON biometric_registrations(created_at);

-- <PERSON><PERSON> biometric authentication logs table
CREATE TABLE IF <PERSON>T EXISTS biometric_auth_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    biometric_type TEXT NOT NULL,
    success BOOLEAN NOT NULL,
    confidence DECIMAL(3,2),
    error_message TEXT,
    device_info TEXT,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for auth logs
CREATE INDEX IF NOT EXISTS idx_biometric_auth_logs_user_created 
ON biometric_auth_logs(user_id, created_at);

CREATE INDEX IF NOT EXISTS idx_biometric_auth_logs_success 
ON biometric_auth_logs(success, created_at);

-- Enable Row Level Security
ALTER TABLE biometric_registrations ENABLE ROW LEVEL SECURITY;
ALTER TABLE biometric_auth_logs ENABLE ROW LEVEL SECURITY;

-- RLS Policies for biometric_registrations
CREATE POLICY "Users can view their own biometric registrations" 
ON biometric_registrations FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own biometric registrations" 
ON biometric_registrations FOR INSERT 
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own biometric registrations" 
ON biometric_registrations FOR UPDATE 
USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own biometric registrations" 
ON biometric_registrations FOR DELETE 
USING (auth.uid() = user_id);

-- Admin access to biometric registrations
CREATE POLICY "Admins can view all biometric registrations" 
ON biometric_registrations FOR SELECT 
USING (
    EXISTS (
        SELECT 1 FROM profiles 
        WHERE profiles.id = auth.uid() 
        AND profiles.role = 'admin'
    )
);

-- RLS Policies for biometric_auth_logs
CREATE POLICY "Users can view their own biometric auth logs" 
ON biometric_auth_logs FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "System can insert biometric auth logs" 
ON biometric_auth_logs FOR INSERT 
WITH CHECK (true);

-- Admin access to auth logs
CREATE POLICY "Admins can view all biometric auth logs" 
ON biometric_auth_logs FOR SELECT 
USING (
    EXISTS (
        SELECT 1 FROM profiles 
        WHERE profiles.id = auth.uid() 
        AND profiles.role = 'admin'
    )
);

-- Function to log biometric authentication attempts
CREATE OR REPLACE FUNCTION log_biometric_auth(
    p_user_id UUID,
    p_biometric_type TEXT,
    p_success BOOLEAN,
    p_confidence DECIMAL DEFAULT NULL,
    p_error_message TEXT DEFAULT NULL,
    p_device_info TEXT DEFAULT NULL,
    p_ip_address INET DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
    log_id UUID;
BEGIN
    INSERT INTO biometric_auth_logs (
        user_id,
        biometric_type,
        success,
        confidence,
        error_message,
        device_info,
        ip_address,
        user_agent
    ) VALUES (
        p_user_id,
        p_biometric_type,
        p_success,
        p_confidence,
        p_error_message,
        p_device_info,
        p_ip_address,
        p_user_agent
    ) RETURNING id INTO log_id;
    
    RETURN log_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get biometric registration status
CREATE OR REPLACE FUNCTION get_user_biometric_status(p_user_id UUID)
RETURNS TABLE (
    biometric_type TEXT,
    is_registered BOOLEAN,
    created_at TIMESTAMP WITH TIME ZONE,
    device_info TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        br.type as biometric_type,
        br.is_active as is_registered,
        br.created_at,
        br.device_info
    FROM biometric_registrations br
    WHERE br.user_id = p_user_id
    AND br.is_active = true
    ORDER BY br.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to clean up old biometric auth logs (keep last 90 days)
CREATE OR REPLACE FUNCTION cleanup_biometric_auth_logs()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM biometric_auth_logs 
    WHERE created_at < NOW() - INTERVAL '90 days';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_biometric_registrations_updated_at
    BEFORE UPDATE ON biometric_registrations
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON biometric_registrations TO authenticated;
GRANT ALL ON biometric_auth_logs TO authenticated;
GRANT EXECUTE ON FUNCTION log_biometric_auth TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_biometric_status TO authenticated;
GRANT EXECUTE ON FUNCTION cleanup_biometric_auth_logs TO authenticated;
