import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import mkcert from "vite-plugin-mkcert";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // Load env file based on `mode` in the current working directory.
  // Set the third parameter to '' to load all env regardless of the `VITE_` prefix.
  const env = loadEnv(mode, process.cwd(), "");

  return {
    server: {
      host: "0.0.0.0", // Listen on all network interfaces
      port: 8081,
      strictPort: true, // Don't try other ports if 8081 is taken
      https: true, // Enable HTTPS
      proxy: {
        // Add proxy configuration if needed
      },
    },
    preview: {
      host: "0.0.0.0",
      port: 8081,
      strictPort: true,
    },
    plugins: [
      react(),
      mkcert(), // Add local HTTPS certificates
    ],
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
      },
    },
    // Expose environment variables to your app
    define: {
      "process.env": env,
    },
  };
});
