// Follow this setup guide to integrate the Deno runtime into your application:
// https://deno.land/manual/examples/deploy_node_server

import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

// Define CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
};

// Define request types
interface EmailRequest {
  to: string;
  subject: string;
  message: string;
}

interface SMSRequest {
  to: string;
  message: string;
}

interface NotificationRequest {
  type: 'email' | 'sms';
  data: EmailRequest | SMSRequest;
}

// Function to send email using SendGrid
async function sendEmail(req: EmailRequest, supabaseClient: any): Promise<{ success: boolean; message: string }> {
  try {
    // Get SendGrid configuration from system_settings
    const { data: configData, error: configError } = await supabaseClient
      .from('system_settings')
      .select('setting_value')
      .eq('setting_name', 'email_service_config')
      .single();

    if (configError || !configData) {
      console.error('Error fetching email config:', configError);
      return { success: false, message: 'Email configuration not found' };
    }

    const config = JSON.parse(configData.setting_value);
    
    // Prepare SendGrid API request
    const sendGridUrl = 'https://api.sendgrid.com/v3/mail/send';
    const response = await fetch(sendGridUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        personalizations: [{ to: [{ email: req.to }] }],
        from: { email: config.fromEmail },
        subject: req.subject,
        content: [
          {
            type: 'text/plain',
            value: req.message,
          },
          {
            type: 'text/html',
            value: req.message.replace(/\n/g, '<br>'),
          },
        ],
      }),
    });

    if (response.ok) {
      return { success: true, message: 'Email sent successfully' };
    } else {
      const errorData = await response.json();
      console.error('SendGrid API error:', errorData);
      return { success: false, message: `SendGrid API error: ${JSON.stringify(errorData)}` };
    }
  } catch (error) {
    console.error('Error sending email:', error);
    return { success: false, message: `Error sending email: ${error.message}` };
  }
}

// Function to send SMS using Twilio
async function sendSMS(req: SMSRequest, supabaseClient: any): Promise<{ success: boolean; message: string }> {
  try {
    // Get Twilio configuration from system_settings
    const { data: configData, error: configError } = await supabaseClient
      .from('system_settings')
      .select('setting_value')
      .eq('setting_name', 'sms_service_config')
      .single();

    if (configError || !configData) {
      console.error('Error fetching SMS config:', configError);
      return { success: false, message: 'SMS configuration not found' };
    }

    const config = JSON.parse(configData.setting_value);
    
    // Prepare Twilio API request
    const twilioUrl = `https://api.twilio.com/2010-04-01/Accounts/${config.accountSid}/Messages.json`;
    const auth = btoa(`${config.accountSid}:${config.authToken}`);
    
    const formData = new URLSearchParams();
    formData.append('To', req.to);
    formData.append('From', config.phoneNumber);
    formData.append('Body', req.message);
    
    const response = await fetch(twilioUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Basic ${auth}`,
      },
      body: formData,
    });
    
    const data = await response.json();
    
    if (response.ok) {
      return { success: true, message: 'SMS sent successfully', sid: data.sid };
    } else {
      console.error('Twilio API error:', data);
      return { success: false, message: `Twilio API error: ${JSON.stringify(data)}` };
    }
  } catch (error) {
    console.error('Error sending SMS:', error);
    return { success: false, message: `Error sending SMS: ${error.message}` };
  }
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }
  
  try {
    const { type, data } = await req.json() as NotificationRequest;
    
    // Create a Supabase client with the Auth context of the logged in user
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      { global: { headers: { Authorization: req.headers.get('Authorization')! } } }
    );
    
    // Get the user from the request
    const {
      data: { user },
    } = await supabaseClient.auth.getUser();
    
    // Only allow authenticated users
    if (!user) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }
    
    let result;
    
    if (type === 'email') {
      result = await sendEmail(data as EmailRequest, supabaseClient);
    } else if (type === 'sms') {
      result = await sendSMS(data as SMSRequest, supabaseClient);
    } else {
      return new Response(
        JSON.stringify({ error: 'Invalid notification type' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }
    
    return new Response(
      JSON.stringify(result),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
});
