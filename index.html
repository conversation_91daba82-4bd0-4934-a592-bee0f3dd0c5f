<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>%VITE_APP_NAME%</title>
    <meta name="description" content="%VITE_APP_DESCRIPTION%" />
    <meta name="author" content="%VITE_COMPANY_NAME%" />

    <!-- Favicon and App Icons -->
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="icon" type="image/png" sizes="192x192" href="/icon-192.png" />
    <link rel="icon" type="image/png" sizes="512x512" href="/icon-512.png" />
    <link rel="apple-touch-icon" href="/icon-192.png" />
    <link rel="manifest" href="/manifest.json" />
    <meta name="theme-color" content="#f39228" />

    <meta property="og:title" content="%VITE_APP_NAME%" />
    <meta property="og:description" content="%VITE_APP_DESCRIPTION%" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="/og-image.svg" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@attendancetracking" />
    <meta name="twitter:image" content="/og-image.svg" />

    <!-- Force dark mode colors -->
    <style>
      .dark nav,
      .dark .bg-primary,
      .dark [class*="dark:bg-[#ff"],
      html.dark nav {
        background-color: #f39228 !important;
      }

      .dark .bg-primary button,
      .dark button.bg-primary {
        background-color: #f39228 !important;
      }

      .dark {
        --primary: 32 89% 56% !important;
      }
    </style>
  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
