<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>%VITE_APP_NAME%</title>
    <meta name="description" content="%VITE_APP_DESCRIPTION%" />
    <meta name="author" content="%VITE_COMPANY_NAME%" />

    <!-- Favicon and App Icons -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="manifest" href="/site.webmanifest" />
    <meta name="theme-color" content="#EE0D09" />

    <meta property="og:title" content="%VITE_APP_NAME%" />
    <meta property="og:description" content="%VITE_APP_DESCRIPTION%" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="/og-image.svg" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@attendancetracking" />
    <meta name="twitter:image" content="/og-image.svg" />

    <!-- Force dark mode colors -->
    <style>
      .dark nav,
      .dark .bg-primary,
      .dark [class*="dark:bg-[#ff"],
      html.dark nav {
        background-color: #f39228 !important;
      }

      .dark .bg-primary button,
      .dark button.bg-primary {
        background-color: #f39228 !important;
      }

      .dark {
        --primary: 32 89% 56% !important;
      }
    </style>
  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
