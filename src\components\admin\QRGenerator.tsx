import { useState, useEffect } from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { mockRooms } from "@/lib/mockData";
import { Room } from "@/lib/types";
import { useToast } from "@/hooks/use-toast";
import { QrCode, RefreshCw } from "lucide-react";
import { useTranslation } from "react-i18next";

export default function QRGenerator() {
  const [selectedRoom, setSelectedRoom] = useState<Room | null>(null);
  const [qrExpiry, setQrExpiry] = useState<Date | null>(null);
  const [timeLeft, setTimeLeft] = useState<number>(0);
  const [qrCodeData, setQrCodeData] = useState<string>("");
  const { toast } = useToast();
  const { t } = useTranslation();

  useEffect(() => {
    // Set default room
    if (mockRooms.length > 0) {
      handleRoomChange(mockRooms[0].id);
    }
  }, []);

  useEffect(() => {
    if (qrExpiry) {
      const interval = setInterval(() => {
        const remaining = Math.max(
          0,
          Math.floor((qrExpiry.getTime() - Date.now()) / 1000)
        );
        setTimeLeft(remaining);

        if (remaining <= 0) {
          clearInterval(interval);
          // Auto-generate a new code when expired
          if (selectedRoom) {
            generateQRCode(selectedRoom);
          }
        }
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [qrExpiry, selectedRoom]);

  const handleRoomChange = (roomId: string) => {
    const room = mockRooms.find((r) => r.id === roomId);
    if (room) {
      setSelectedRoom(room);
      if (room.currentQrCode && room.qrExpiry) {
        setQrCodeData(room.currentQrCode);
        const expiry = new Date(room.qrExpiry);
        setQrExpiry(expiry);
        setTimeLeft(
          Math.max(0, Math.floor((expiry.getTime() - Date.now()) / 1000))
        );
      } else {
        generateQRCode(room);
      }
    }
  };

  const generateQRCode = (room: Room) => {
    // In a real app, this would be an API call to generate a secure QR code
    const newQrData = `room:${room.id}:${Date.now()}:${Math.random()
      .toString(36)
      .substring(2, 10)}`;
    setQrCodeData(newQrData);

    // Set expiry to 5 minutes from now
    const newExpiry = new Date(Date.now() + 5 * 60 * 1000);
    setQrExpiry(newExpiry);
    setTimeLeft(5 * 60);

    toast({
      title: "QR Code Generated",
      description: `New QR code for ${room.name} will expire in 5 minutes`,
    });
  };

  const formatTimeLeft = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardHeader>
        <CardTitle>{t("admin.qrGenerator.title")}</CardTitle>
        <CardDescription>{t("admin.qrGenerator.description")}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <Label htmlFor="room">{t("admin.qrGenerator.selectRoom")}</Label>
          <select
            id="room"
            className="w-full p-2 border rounded-md"
            value={selectedRoom?.id || ""}
            onChange={(e) => handleRoomChange(e.target.value)}
          >
            {mockRooms.map((room) => (
              <option key={room.id} value={room.id}>
                {room.name} - {room.building}, Floor {room.floor}
              </option>
            ))}
          </select>
        </div>

        <div className="border rounded-md p-4">
          <div className="flex flex-col md:flex-row md:justify-between md:items-center mb-4">
            <div>
              <h3 className="text-lg font-medium">{selectedRoom?.name}</h3>
              <p className="text-sm text-muted-foreground">
                {selectedRoom?.building}, Floor {selectedRoom?.floor}
              </p>
            </div>
            <div className="mt-2 md:mt-0">
              <Button
                onClick={() => selectedRoom && generateQRCode(selectedRoom)}
                className="flex items-center gap-1"
              >
                <RefreshCw size={16} />{" "}
                {t("admin.qrGenerator.regenerateQRCode")}
              </Button>
            </div>
          </div>

          <div className="flex flex-col md:flex-row items-center space-y-4 md:space-y-0 md:space-x-6">
            <div className="flex flex-col items-center">
              {qrCodeData ? (
                <div className="border-4 border-primary p-4 rounded-md bg-white">
                  <div className="flex items-center justify-center w-48 h-48 bg-white">
                    <QrCode size={200} />
                  </div>
                </div>
              ) : (
                <div className="border-4 border-gray-200 p-4 rounded-md w-56 h-56 flex items-center justify-center">
                  <p className="text-muted-foreground">No QR code generated</p>
                </div>
              )}

              <div className="mt-2 text-center">
                {timeLeft > 0 ? (
                  <div className="flex flex-col items-center">
                    <span className="font-medium">
                      {t("admin.qrGenerator.expiresIn")}
                    </span>
                    <span
                      className={`text-xl font-bold ${
                        timeLeft < 60
                          ? "text-red-600 animate-pulse"
                          : "text-primary"
                      }`}
                    >
                      {formatTimeLeft(timeLeft)}
                    </span>
                  </div>
                ) : (
                  <span className="text-destructive font-medium">Expired</span>
                )}
              </div>
            </div>

            <div className="flex-1 space-y-4">
              <div>
                <Label htmlFor="qr-data">
                  {t("admin.qrGenerator.qrCodeData")}
                </Label>
                <Input
                  id="qr-data"
                  value={qrCodeData}
                  readOnly
                  className="font-mono text-sm"
                />
              </div>

              <div>
                <Label htmlFor="room-capacity">
                  {t("admin.qrGenerator.roomCapacity")}
                </Label>
                <div className="flex items-center">
                  <Input
                    id="room-capacity"
                    value={selectedRoom?.capacity || 0}
                    readOnly
                  />
                  <span className="ml-2">
                    {t("admin.qrGenerator.students")}
                  </span>
                </div>
              </div>

              <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                <h4 className="font-medium text-blue-800">
                  {t("admin.qrGenerator.qrCodeSecurityFeatures")}
                </h4>
                <ul className="text-sm text-blue-700 list-disc list-inside mt-1">
                  <li>{t("admin.qrGenerator.timeLimitedExpiration")}</li>
                  <li>{t("admin.qrGenerator.encryptedRoomIdentifier")}</li>
                  <li>{t("admin.qrGenerator.timestampsToPrevent")}</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between text-sm text-muted-foreground">
        <div>System automatically regenerates QR codes every 5 minutes</div>
      </CardFooter>
    </Card>
  );
}
