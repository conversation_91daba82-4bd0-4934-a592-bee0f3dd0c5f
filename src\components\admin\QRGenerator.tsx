import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Room } from "@/lib/types";
import { useToast } from "@/hooks/use-toast";
import { QrCode, RefreshCw, Building2, Home } from "lucide-react";
import { useTranslation } from "react-i18next";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/context/AuthContext";
import QRCode from "react-qr-code";

interface Block {
  id: string;
  name: string;
  school_id: string;
}

export default function QRGenerator() {
  // State for blocks and rooms
  const [blocks, setBlocks] = useState<Block[]>([]);
  const [rooms, setRooms] = useState<Room[]>([]);
  const [selectedBlock, setSelectedBlock] = useState<string>("");
  const [selectedRoom, setSelectedRoom] = useState<Room | null>(null);
  const [loading, setLoading] = useState(true);

  // QR code state
  const [qrExpiry, setQrExpiry] = useState<Date | null>(null);
  const [timeLeft, setTimeLeft] = useState<number>(0);
  const [qrCodeData, setQrCodeData] = useState<string>("");
  const [qrSessionId, setQrSessionId] = useState<string>("");

  const { toast } = useToast();
  const { t } = useTranslation();
  const { profile } = useAuth();

  // Fetch blocks and rooms on component mount
  useEffect(() => {
    const fetchBlocksAndRooms = async () => {
      try {
        setLoading(true);

        // Fetch blocks for the current school
        let blocksQuery = supabase.from("blocks").select("*").order("name");

        // Filter by school_id if available (for school admins)
        if (profile?.school_id && profile?.access_level !== 3) {
          blocksQuery = blocksQuery.eq("school_id", profile.school_id);
        }

        const { data: blocksData, error: blocksError } = await blocksQuery;

        if (blocksError) {
          console.error("Error fetching blocks:", blocksError);
          throw blocksError;
        }

        console.log("Fetched blocks for QR Generator:", blocksData);
        setBlocks(blocksData || []);

        // Fetch rooms for the current school
        let roomsQuery = supabase
          .from("rooms")
          .select(
            `
            id,
            name,
            building,
            floor,
            capacity,
            teacher_id,
            block_id,
            school_id,
            current_qr_code,
            qr_expiry,
            blocks (
              id,
              name
            )
          `
          )
          .order("name");

        // Filter by school_id if available (for school admins)
        if (profile?.school_id && profile?.access_level !== 3) {
          roomsQuery = roomsQuery.eq("school_id", profile.school_id);
        }

        const { data: roomsData, error: roomsError } = await roomsQuery;

        if (roomsError) {
          console.error("Error fetching rooms:", roomsError);
          throw roomsError;
        }

        console.log("Fetched rooms for QR Generator:", roomsData);
        setRooms(roomsData || []);
      } catch (error) {
        console.error("Error fetching blocks and rooms:", error);
        toast({
          title: "Error",
          description: "Failed to load blocks and rooms",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchBlocksAndRooms();
  }, [profile?.school_id, profile?.access_level]);

  // QR code expiry timer
  useEffect(() => {
    if (qrExpiry) {
      const interval = setInterval(() => {
        const remaining = Math.max(
          0,
          Math.floor((qrExpiry.getTime() - Date.now()) / 1000)
        );
        setTimeLeft(remaining);

        if (remaining <= 0) {
          clearInterval(interval);
          // Auto-generate a new code when expired
          if (selectedRoom) {
            generateQRCode(selectedRoom);
          }
        }
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [qrExpiry, selectedRoom]);

  // Handle block selection
  const handleBlockChange = (blockId: string) => {
    setSelectedBlock(blockId);
    setSelectedRoom(null);
    setQrCodeData("");
    setQrExpiry(null);
    setTimeLeft(0);
  };

  // Handle room selection
  const handleRoomChange = (roomId: string) => {
    const room = rooms.find((r) => r.id === roomId);
    if (room) {
      setSelectedRoom(room);

      // Check if room has existing QR code that's still valid
      if (room.current_qr_code && room.qr_expiry) {
        const expiry = new Date(room.qr_expiry);
        if (expiry.getTime() > Date.now()) {
          // Use existing QR code
          setQrCodeData(room.current_qr_code);
          setQrExpiry(expiry);
          setTimeLeft(
            Math.max(0, Math.floor((expiry.getTime() - Date.now()) / 1000))
          );
          return;
        }
      }

      // Generate new QR code if none exists or expired
      generateQRCode(room);
    }
  };

  // Generate QR code for a room
  const generateQRCode = async (room: Room) => {
    try {
      // Create QR session ID
      const sessionId = crypto.randomUUID();
      setQrSessionId(sessionId);

      // Set expiry to 5 minutes from now
      const newExpiry = new Date(Date.now() + 5 * 60 * 1000);

      // Create secure QR code data
      const qrData = JSON.stringify({
        room_id: room.id,
        session_id: sessionId,
        timestamp: new Date().toISOString(),
        expires_at: newExpiry.toISOString(),
        school_id: room.school_id,
        block_id: room.block_id,
        signature: `qr_${room.id}_${Date.now()}`, // In production, this would be a real cryptographic signature
      });

      setQrCodeData(qrData);
      setQrExpiry(newExpiry);
      setTimeLeft(5 * 60);

      // Update room in database with new QR code
      const { error } = await supabase
        .from("rooms")
        .update({
          current_qr_code: qrData,
          qr_expiry: newExpiry.toISOString(),
        })
        .eq("id", room.id);

      if (error) {
        console.error("Error updating room QR code:", error);
        throw error;
      }

      toast({
        title: "QR Code Generated",
        description: `New QR code for ${room.name} will expire in 5 minutes`,
      });
    } catch (error) {
      console.error("Error generating QR code:", error);
      toast({
        title: "Error",
        description: "Failed to generate QR code",
        variant: "destructive",
      });
    }
  };

  const formatTimeLeft = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  // Get rooms for selected block
  const filteredRooms = selectedBlock
    ? rooms.filter((room) => room.block_id === selectedBlock)
    : [];

  if (loading) {
    return (
      <Card className="w-full max-w-3xl mx-auto">
        <CardHeader>
          <CardTitle>{t("admin.qrGenerator.title")}</CardTitle>
          <CardDescription>
            {t("admin.qrGenerator.description")}
          </CardDescription>
        </CardHeader>
        <CardContent className="flex items-center justify-center p-8">
          <div className="text-center">
            <QrCode className="h-8 w-8 animate-spin mx-auto mb-2" />
            <p>Loading blocks and rooms...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardHeader>
        <CardTitle>{t("admin.qrGenerator.title")}</CardTitle>
        <CardDescription>{t("admin.qrGenerator.description")}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Block Selection */}
        <div className="space-y-2">
          <Label htmlFor="block" className="flex items-center gap-2">
            <Building2 className="h-4 w-4" />
            Select Block
          </Label>
          <Select value={selectedBlock} onValueChange={handleBlockChange}>
            <SelectTrigger>
              <SelectValue placeholder="Choose a block..." />
            </SelectTrigger>
            <SelectContent>
              {blocks.map((block) => (
                <SelectItem key={block.id} value={block.id}>
                  Block {block.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Room Selection */}
        {selectedBlock && (
          <div className="space-y-2">
            <Label htmlFor="room" className="flex items-center gap-2">
              <Home className="h-4 w-4" />
              Select Room
            </Label>
            <Select
              value={selectedRoom?.id || ""}
              onValueChange={handleRoomChange}
            >
              <SelectTrigger>
                <SelectValue placeholder="Choose a room..." />
              </SelectTrigger>
              <SelectContent>
                {filteredRooms.map((room) => (
                  <SelectItem key={room.id} value={room.id}>
                    {room.name} {room.building && `- ${room.building}`}{" "}
                    {room.floor && `Floor ${room.floor}`}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        {/* QR Code Display */}
        {selectedRoom && (
          <div className="border rounded-md p-4">
            <div className="flex flex-col md:flex-row md:justify-between md:items-center mb-4">
              <div>
                <h3 className="text-lg font-medium">{selectedRoom.name}</h3>
                <p className="text-sm text-muted-foreground">
                  Block{" "}
                  {blocks.find((b) => b.id === selectedRoom.block_id)?.name}
                  {selectedRoom.building && ` - ${selectedRoom.building}`}
                  {selectedRoom.floor && `, Floor ${selectedRoom.floor}`}
                </p>
              </div>
              <div className="mt-2 md:mt-0">
                <Button
                  onClick={() => generateQRCode(selectedRoom)}
                  className="flex items-center gap-1"
                >
                  <RefreshCw size={16} />
                  {t("admin.qrGenerator.regenerateQRCode")}
                </Button>
              </div>
            </div>

            <div className="flex flex-col md:flex-row items-center space-y-4 md:space-y-0 md:space-x-6">
              <div className="flex flex-col items-center">
                {qrCodeData ? (
                  <div className="border-4 border-primary p-4 rounded-md bg-white">
                    <QRCode
                      value={qrCodeData}
                      size={200}
                      style={{
                        height: "auto",
                        maxWidth: "100%",
                        width: "100%",
                      }}
                    />
                  </div>
                ) : (
                  <div className="border-4 border-gray-200 p-4 rounded-md w-56 h-56 flex items-center justify-center">
                    <p className="text-muted-foreground">
                      No QR code generated
                    </p>
                  </div>
                )}

                <div className="mt-2 text-center">
                  {timeLeft > 0 ? (
                    <div className="flex flex-col items-center">
                      <span className="font-medium">
                        {t("admin.qrGenerator.expiresIn")}
                      </span>
                      <span
                        className={`text-xl font-bold ${
                          timeLeft < 60
                            ? "text-red-600 animate-pulse"
                            : "text-primary"
                        }`}
                      >
                        {formatTimeLeft(timeLeft)}
                      </span>
                    </div>
                  ) : (
                    <span className="text-destructive font-medium">
                      Expired
                    </span>
                  )}
                </div>
              </div>

              <div className="flex-1 space-y-4">
                <div>
                  <Label htmlFor="qr-data">QR Code Data (JSON)</Label>
                  <textarea
                    id="qr-data"
                    value={
                      qrCodeData
                        ? JSON.stringify(JSON.parse(qrCodeData), null, 2)
                        : ""
                    }
                    readOnly
                    className="w-full p-2 border rounded-md font-mono text-xs h-32 resize-none"
                  />
                </div>

                <div>
                  <Label htmlFor="room-capacity">
                    {t("admin.qrGenerator.roomCapacity")}
                  </Label>
                  <div className="flex items-center">
                    <Input
                      id="room-capacity"
                      value={selectedRoom?.capacity || 0}
                      readOnly
                    />
                    <span className="ml-2">
                      {t("admin.qrGenerator.students")}
                    </span>
                  </div>
                </div>

                <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                  <h4 className="font-medium text-blue-800">
                    {t("admin.qrGenerator.qrCodeSecurityFeatures")}
                  </h4>
                  <ul className="text-sm text-blue-700 list-disc list-inside mt-1">
                    <li>{t("admin.qrGenerator.timeLimitedExpiration")}</li>
                    <li>Room and block validation</li>
                    <li>Session-based tracking</li>
                    <li>{t("admin.qrGenerator.timestampsToPrevent")}</li>
                  </ul>
                </div>

                <div className="p-3 bg-green-50 border border-green-200 rounded-md">
                  <h4 className="font-medium text-green-800">
                    Room Information
                  </h4>
                  <div className="text-sm text-green-700 mt-1 space-y-1">
                    <p>
                      <strong>Room ID:</strong> {selectedRoom.id}
                    </p>
                    <p>
                      <strong>Block:</strong>{" "}
                      {blocks.find((b) => b.id === selectedRoom.block_id)?.name}
                    </p>
                    <p>
                      <strong>Capacity:</strong> {selectedRoom.capacity}{" "}
                      students
                    </p>
                    {qrSessionId && (
                      <p>
                        <strong>Session ID:</strong> {qrSessionId}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-between text-sm text-muted-foreground">
        <div>
          {selectedRoom
            ? "QR codes automatically expire after 5 minutes for security"
            : "Select a block and room to generate QR codes"}
        </div>
      </CardFooter>
    </Card>
  );
}
