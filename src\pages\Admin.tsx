import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import Navbar from "@/components/shared/Navbar";
import QRGenerator from "@/components/admin/QRGenerator";
import UserManagement from "@/components/admin/UserManagement";
import AdminAlerts from "@/components/admin/AdminAlerts";
import EnhancedFraudDetection from "@/components/admin/EnhancedFraudDetection";
import AdminStudentDirectory from "@/components/admin/AdminStudentDirectory";
import AdminSettings from "@/components/admin/AdminSettings";
import AdminExcusesManagement from "@/components/admin/AdminExcusesManagement";
import AdminParentContacts from "@/pages/AdminParentContacts";
import SchoolManagement from "@/components/admin/SchoolManagement";
import SchoolContext from "@/components/admin/SchoolContext";
import SchoolSettings from "@/components/admin/SchoolSettings";
import AuditLogs from "@/components/admin/AuditLogs";
import { Admin as AdminType } from "@/lib/types";
import { useAuth } from "@/context/AuthContext";
import { useSchool } from "@/context/SchoolContext";
import { TabProvider } from "@/context/TabContext";
import DashboardMessage from "@/components/shared/DashboardMessage";
import Footer from "@/components/shared/Footer";
import { motion, AnimatePresence } from "framer-motion";
import FeedbackForm from "@/components/shared/FeedbackForm";
import SimpleCarousel from "@/components/shared/SimpleCarousel";
import { useTranslation } from "react-i18next";
import {
  Bell,
  Users,
  Settings,
  FileText,
  UserPlus,
  User,
  Building2,
  Shield,
  ArrowLeft,
  QrCode,
} from "lucide-react";
import AdminProfile from "@/components/admin/AdminProfile";

export default function Admin() {
  const { profile, loading } = useAuth();
  const { currentSchool } = useSchool();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState("users");

  // If loading, show loading indicator
  if (loading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <div className="flex-1 flex items-center justify-center">
          <p>{t("common.loading")}</p>
        </div>
      </div>
    );
  }

  // If not an admin, redirect to login
  if (!profile || profile.role !== "admin") {
    navigate("/login");
    return null;
  }

  // Check if admin profile is incomplete
  const adminProfile = profile as AdminType;
  const isProfileIncomplete = !adminProfile.position || !adminProfile.school;

  // Check if user is a system admin (access_level 3)
  const isSystemAdmin = adminProfile.accessLevel === 3;

  return (
    <TabProvider defaultTab="users">
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <div className="flex-1">
          {isSystemAdmin && (
            <div className="container mx-auto px-4 flex justify-start items-center pt-6">
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigate("/system-admin")}
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                {t("admin.backToSystemAdmin")}
              </Button>

              {isSystemAdmin && currentSchool && (
                <Badge
                  variant="outline"
                  className="bg-blue-100 text-blue-800 border-blue-200 ml-4"
                >
                  {t("admin.systemAdminView")}
                </Badge>
              )}
            </div>
          )}

          {isProfileIncomplete && (
            <div className="container mx-auto px-4 py-6">
              <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg
                      className="h-5 w-5 text-yellow-400"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-yellow-700">
                      {t("admin.profile.incompleteProfile")}
                      <a
                        href="/admin/profile"
                        className="font-medium underline text-yellow-700 hover:text-yellow-600 ml-2"
                      >
                        {t("profile.completeProfile")}
                      </a>
                      <span className="ml-2">{t("common.or")}</span>
                      <button
                        onClick={() =>
                          document
                            .querySelector('[value="profile"]')
                            ?.dispatchEvent(new MouseEvent("click"))
                        }
                        className="font-medium underline text-yellow-700 hover:text-yellow-600 ml-1"
                      >
                        {t("admin.profile.goToProfileTab")}
                      </button>
                    </p>
                  </div>
                </div>
              </div>

              <h1 className="text-2xl font-bold">
                {t("admin.dashboard.title")}
                {currentSchool && (
                  <span className="text-muted-foreground ml-2 font-normal">
                    {currentSchool.name}
                  </span>
                )}
              </h1>
            </div>
          )}

          {!isProfileIncomplete && (
            <>
              {/* Dashboard Carousel with title overlay - directly below navbar */}
              <SimpleCarousel userType="admin" />

              {/* Display dashboard message below carousel */}
              <div className="container mx-auto px-4 mt-6">
                <DashboardMessage userType="admin" />
              </div>
            </>
          )}

          {/* Floating feedback button */}
          {!isProfileIncomplete && <FeedbackForm variant="fab" />}

          <div className="container mx-auto px-4">
            <Tabs
              defaultValue={activeTab}
              onValueChange={(value) => setActiveTab(value)}
              className="w-full"
            >
              <Card className="mb-6 p-2">
                {/* First row of tabs */}
                <TabsList className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-1 mb-2">
                  <TabsTrigger
                    value="users"
                    className="flex items-center justify-center gap-2 px-3 py-2 h-10"
                  >
                    <Users className="h-4 w-4" />
                    <span className="text-center">{t("admin.tabs.users")}</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="students"
                    className="flex items-center justify-center gap-2 px-3 py-2 h-10"
                  >
                    <User className="h-4 w-4" />
                    <span className="text-center">
                      {t("admin.tabs.students")}
                    </span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="schools"
                    className="flex items-center justify-center gap-2 px-3 py-2 h-10"
                  >
                    <Building2 className="h-4 w-4" />
                    <span className="text-center">
                      {t("admin.tabs.schools")}
                    </span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="qrcodes"
                    className="flex items-center justify-center gap-2 px-3 py-2 h-10"
                  >
                    <QrCode className="h-4 w-4" />
                    <span className="text-center">
                      {t("admin.tabs.qrCodes")}
                    </span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="excuses"
                    className="flex items-center justify-center gap-2 px-3 py-2 h-10"
                  >
                    <FileText className="h-4 w-4" />
                    <span className="text-center">
                      {t("admin.tabs.excuses")}
                    </span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="parents"
                    className="flex items-center justify-center gap-2 px-3 py-2 h-10"
                  >
                    <UserPlus className="h-4 w-4" />
                    <span className="text-center">
                      {t("admin.tabs.parents")}
                    </span>
                  </TabsTrigger>
                </TabsList>

                {/* Second row of tabs */}
                <TabsList className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-1">
                  <TabsTrigger
                    value="settings"
                    className="flex items-center justify-center gap-2 px-3 py-2 h-10"
                  >
                    <Settings className="h-4 w-4" />
                    <span className="text-center">{t("common.settings")}</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="school-settings"
                    className="flex items-center justify-center gap-2 px-3 py-2 h-10"
                  >
                    <Settings className="h-4 w-4" />
                    <span className="text-center">
                      {t("admin.tabs.schoolSettings")}
                    </span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="profile"
                    className="flex items-center justify-center gap-2 px-3 py-2 h-10"
                  >
                    <User className="h-4 w-4" />
                    <span className="text-center">{t("common.profile")}</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="fraud"
                    className="flex items-center justify-center gap-2 px-3 py-2 h-10"
                  >
                    <Shield className="h-4 w-4" />
                    <span className="text-center">{t("admin.tabs.fraud")}</span>
                    <Badge
                      variant="outline"
                      className="ml-1 bg-red-100 text-red-800 border-red-200 text-[10px] px-1 py-0 h-4"
                    >
                      Alert
                    </Badge>
                  </TabsTrigger>
                  <TabsTrigger
                    value="alerts"
                    className="flex items-center justify-center gap-2 px-3 py-2 h-10"
                  >
                    <Bell className="h-4 w-4" />
                    <span className="text-center">
                      {t("admin.tabs.alerts")}
                    </span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="audit-logs"
                    className="flex items-center justify-center gap-2 px-3 py-2 h-10"
                  >
                    <Shield className="h-4 w-4" />
                    <span className="text-center">
                      {t("admin.tabs.auditLogs")}
                    </span>
                  </TabsTrigger>
                </TabsList>
              </Card>

              <AnimatePresence mode="wait">
                <motion.div
                  key={activeTab}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.2 }}
                >
                  <TabsContent value="users" className="mt-0">
                    <div className="grid gap-6">
                      <SchoolContext />
                      <UserManagement />
                    </div>
                  </TabsContent>

                  <TabsContent value="students" className="mt-0">
                    <AdminStudentDirectory />
                  </TabsContent>

                  <TabsContent value="schools" className="mt-0">
                    <SchoolManagement />
                  </TabsContent>

                  <TabsContent value="qrcodes" className="mt-0">
                    <QRGenerator />
                  </TabsContent>

                  <TabsContent value="fraud" className="mt-0">
                    <EnhancedFraudDetection />
                  </TabsContent>

                  <TabsContent value="excuses" className="mt-0">
                    <AdminExcusesManagement />
                  </TabsContent>

                  <TabsContent value="parents" className="mt-0">
                    <AdminParentContacts />
                  </TabsContent>

                  <TabsContent value="alerts" className="mt-0">
                    <AdminAlerts />
                  </TabsContent>

                  <TabsContent value="settings" className="mt-0">
                    <AdminSettings />
                  </TabsContent>

                  <TabsContent value="school-settings" className="mt-0">
                    <div className="grid gap-6">
                      <SchoolContext />
                      <SchoolSettings />
                    </div>
                  </TabsContent>

                  <TabsContent value="audit-logs" className="mt-0">
                    <div className="grid gap-6">
                      <SchoolContext />
                      <AuditLogs />
                    </div>
                  </TabsContent>

                  <TabsContent value="profile" className="mt-0">
                    <AdminProfile />
                  </TabsContent>
                </motion.div>
              </AnimatePresence>
            </Tabs>
          </div>
        </div>
        {!isProfileIncomplete && <Footer />}
      </div>
    </TabProvider>
  );
}
