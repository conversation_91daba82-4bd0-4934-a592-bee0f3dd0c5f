import { supabase } from "@/lib/supabase";
import { ParentContact } from "@/lib/types/parent-contact";
import {
  sendEmailWithSendGrid,
  sendSMSWithTwilio,
  getEmailServiceConfig,
  getSMSServiceConfig,
} from "./external-notification-services";

interface NotificationData {
  subject: string;
  message: string;
  studentName: string;
  excuseId: string;
  startDate: string;
  endDate: string;
  reason: string;
}

// Send email using real email service
const sendEmail = async (
  to: string,
  subject: string,
  message: string
): Promise<boolean> => {
  console.log(`[EMAIL SERVICE] Sending email to ${to}`);

  // Check if email service is configured
  const emailConfig = await getEmailServiceConfig();
  if (!emailConfig) {
    console.warn("Email service not configured. Email will not be sent.");
    return false;
  }

  // Send email using SendGrid
  return await sendEmailWithSendGrid(to, subject, message);
};

// Send SMS using real SMS service
const sendSMS = async (to: string, message: string): Promise<boolean> => {
  console.log(`[SMS SERVICE] Sending SMS to ${to}`);

  // Check if SMS service is configured
  const smsConfig = await getSMSServiceConfig();
  if (!smsConfig) {
    console.warn("SMS service not configured. SMS will not be sent.");
    return false;
  }

  // Send SMS using Twilio
  return await sendSMSWithTwilio(to, message);
};

// Format the notification message
const formatMessage = (data: NotificationData, isEmail: boolean): string => {
  const dateOptions: Intl.DateTimeFormatOptions = {
    weekday: "long",
    year: "numeric",
    month: "long",
    day: "numeric",
  };

  const startDate = new Date(data.startDate).toLocaleDateString(
    undefined,
    dateOptions
  );
  const endDate = new Date(data.endDate).toLocaleDateString(
    undefined,
    dateOptions
  );

  if (isEmail) {
    return `
Dear Parent/Guardian,

This is to inform you that your child, ${data.studentName}, has submitted a request for absence from school.

Request Details:
- Start Date: ${startDate}
- End Date: ${endDate}
- Reason: ${data.reason}

This request is currently pending approval from school administration. You will be notified once the request has been reviewed.

If you did not expect this request or have any concerns, please contact the school administration immediately.

Thank you,
Campus Guardian Attendance System
    `;
  } else {
    // Shorter message for SMS
    return `NOTICE: ${data.studentName} has requested absence from ${startDate} to ${endDate}. Reason: ${data.reason}. Contact school for questions.`;
  }
};

// Send notifications to parents based on their preferences
export const notifyParents = async (
  studentId: string,
  notificationData: NotificationData
): Promise<{ success: boolean; message: string }> => {
  try {
    // Fetch parent contacts for the student
    const { data: parentContacts, error } = await supabase
      .from("parent_contacts")
      .select("*")
      .eq("student_id", studentId)
      .eq("notifications_enabled", true);

    if (error) throw error;

    if (!parentContacts || parentContacts.length === 0) {
      console.log(
        `No parent contacts found for student ${studentId} or notifications disabled`
      );
      return {
        success: false,
        message: "No parent contacts found or notifications disabled",
      };
    }

    const results = await Promise.all(
      parentContacts.map(async (contact: ParentContact) => {
        const { notification_method, email, phone, parent_name } = contact;

        // Skip if notification method is none
        if (notification_method === "none") {
          return {
            success: true,
            message: "Notifications disabled for this contact",
          };
        }

        // Prepare personalized message
        const emailMessage = formatMessage(notificationData, true);
        const smsMessage = formatMessage(notificationData, false);

        // Send notifications based on preference
        if (notification_method === "email" || notification_method === "both") {
          if (email) {
            try {
              const success = await sendEmail(
                email,
                notificationData.subject,
                emailMessage
              );

              // Log the notification
              await logNotification(
                studentId,
                notificationData.excuseId,
                "email",
                email,
                success
              );
            } catch (error: any) {
              console.error("Error sending email notification:", error);
              await logNotification(
                studentId,
                notificationData.excuseId,
                "email",
                email,
                false,
                error.message
              );
            }
          }
        }

        if (notification_method === "sms" || notification_method === "both") {
          if (phone) {
            try {
              const success = await sendSMS(phone, smsMessage);

              // Log the notification
              await logNotification(
                studentId,
                notificationData.excuseId,
                "sms",
                phone,
                success
              );
            } catch (error: any) {
              console.error("Error sending SMS notification:", error);
              await logNotification(
                studentId,
                notificationData.excuseId,
                "sms",
                phone,
                false,
                error.message
              );
            }
          }
        }

        return {
          success: true,
          message: `Notifications sent to ${parent_name}`,
        };
      })
    );

    // Check if all notifications were sent successfully
    const allSuccessful = results.every((result) => result.success);

    return {
      success: allSuccessful,
      message: allSuccessful
        ? "All notifications sent successfully"
        : "Some notifications failed to send",
    };
  } catch (error: any) {
    console.error("Error sending parent notifications:", error);
    return { success: false, message: `Error: ${error.message}` };
  }
};

// Log notification events for auditing
export const logNotification = async (
  studentId: string,
  excuseId: string,
  notificationType: "email" | "sms",
  recipient: string,
  success: boolean,
  errorMessage?: string
): Promise<void> => {
  try {
    await supabase.from("notification_logs").insert({
      student_id: studentId,
      excuse_id: excuseId,
      notification_type: notificationType,
      recipient,
      success,
      error_message: errorMessage,
      created_at: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error logging notification:", error);
  }
};
