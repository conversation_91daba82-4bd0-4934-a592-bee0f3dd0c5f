import { supabase } from './supabase';

// Convert ArrayBuffer to Base64URL string
const bufferToBase64URL = (buffer: ArrayBuffer): string => {
  // Convert ArrayBuffer to Base64
  const base64 = btoa(String.fromCharCode(...new Uint8Array(buffer)));
  // Convert Base64 to Base64URL
  return base64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
};

// Convert Base64URL string to ArrayBuffer
const base64URLToBuffer = (base64url: string): ArrayBuffer => {
  // Convert Base64URL to Base64
  const base64 = base64url.replace(/-/g, '+').replace(/_/g, '/');
  // Add padding if needed
  const padded = base64.padEnd(base64.length + (4 - (base64.length % 4)) % 4, '=');
  // Convert Base64 to binary string
  const binary = atob(padded);
  // Convert binary string to <PERSON>rray<PERSON>uffer
  const buffer = new ArrayBuffer(binary.length);
  const bytes = new Uint8Array(buffer);
  for (let i = 0; i < binary.length; i++) {
    bytes[i] = binary.charCodeAt(i);
  }
  return buffer;
};

// Get the domain without port number
const getDomain = () => {
  const hostname = window.location.hostname;
  // For localhost development
  if (hostname === 'localhost' || hostname === '127.0.0.1') {
    return hostname;
  }
  return hostname;
};

// Check if WebAuthn is available
export const isWebAuthnAvailable = () => {
  return window.PublicKeyCredential !== undefined &&
    typeof window.PublicKeyCredential === 'function';
};

export async function startRegistration(userId: string, username: string) {
  try {
    if (!isWebAuthnAvailable()) {
      throw new Error('WebAuthn is not supported in this browser');
    }

    // 1. Get registration options from server
    const challenge = new Uint8Array(32);
    crypto.getRandomValues(challenge);

    const publicKeyCredentialCreationOptions: PublicKeyCredentialCreationOptions = {
      challenge,
      rp: {
        name: 'Campus Guardian',
        id: getDomain()
      },
      user: {
        id: new TextEncoder().encode(userId),
        name: username,
        displayName: username
      },
      pubKeyCredParams: [
        { alg: -7, type: 'public-key' },   // ES256
        { alg: -257, type: 'public-key' }  // RS256
      ],
      authenticatorSelection: {
        // Remove platform restriction to support both platform and cross-platform authenticators
        authenticatorAttachment: undefined,
        userVerification: "preferred",
        requireResidentKey: false
      },
      timeout: 60000,
      attestation: 'none'
    };

    // 2. Create credentials
    const credential = await navigator.credentials.create({
      publicKey: publicKeyCredentialCreationOptions
    }) as PublicKeyCredential;

    if (!credential) {
      throw new Error('No credentials returned from authenticator');
    }

    const response = credential.response as AuthenticatorAttestationResponse;

    // 3. Format the response data
    const credentialData = {
      id: credential.id,
      rawId: bufferToBase64URL(credential.rawId),
      response: {
        attestationObject: bufferToBase64URL(response.attestationObject),
        clientDataJSON: bufferToBase64URL(response.clientDataJSON)
      },
      type: credential.type
    };

    // 4. Store credential in Supabase
    const { error } = await supabase
      .from('biometric_credentials')
      .insert([{
        user_id: userId,
        credential_id: credentialData.id,
        public_key: credentialData.rawId,
        counter: 0,
        created_at: new Date().toISOString()
      }]);

    if (error) throw error;

    return credentialData;
  } catch (error) {
    console.error('Error in biometric registration:', error);
    throw error;
  }
}

export async function startAuthentication(userId: string) {
  try {
    if (!isWebAuthnAvailable()) {
      throw new Error('WebAuthn is not supported in this browser');
    }

    // 1. Get the user's credentials from the database
    const { data: credentials, error } = await supabase
      .from('biometric_credentials')
      .select('credential_id, public_key, counter')
      .eq('user_id', userId)
      .single()
      .throwOnError();

    if (error) {
      console.error('Error fetching credentials:', error);
      if (error.code === 'PGRST116') {
        throw new Error('No biometric credentials found. Please register your biometrics first.');
      }
      throw new Error('Failed to fetch biometric credentials. Please try registering again.');
    }
    
    if (!credentials) {
      throw new Error('No biometric credentials found. Please register your biometrics first.');
    }

    // 2. Create authentication options
    const challenge = new Uint8Array(32);
    crypto.getRandomValues(challenge);

    const publicKeyCredentialRequestOptions: PublicKeyCredentialRequestOptions = {
      challenge,
      allowCredentials: [{
        id: base64URLToBuffer(credentials.public_key),
        type: 'public-key',
        transports: ['internal', 'hybrid', 'usb', 'ble', 'nfc']
      }],
      timeout: 60000,
      userVerification: "preferred",
      rpId: getDomain()
    };

    // 3. Request authentication
    const assertion = await navigator.credentials.get({
      publicKey: publicKeyCredentialRequestOptions
    }) as PublicKeyCredential;

    if (!assertion) {
      throw new Error('No credentials returned from authenticator');
    }

    const response = assertion.response as AuthenticatorAssertionResponse;

    // 4. Format the response data
    const assertionData = {
      id: assertion.id,
      rawId: bufferToBase64URL(assertion.rawId),
      response: {
        authenticatorData: bufferToBase64URL(response.authenticatorData),
        clientDataJSON: bufferToBase64URL(response.clientDataJSON),
        signature: bufferToBase64URL(response.signature),
        userHandle: response.userHandle ? bufferToBase64URL(response.userHandle) : null
      },
      type: assertion.type
    };

    // 5. Update the credential counter in the database
    const { error: updateError } = await supabase
      .from('biometric_credentials')
      .update({ counter: (credentials.counter || 0) + 1 })
      .eq('credential_id', credentials.credential_id)
      .throwOnError();

    if (updateError) {
      console.error('Error updating credential counter:', updateError);
      // Don't throw here, as the authentication was successful
    }

    return assertionData;
  } catch (error) {
    console.error('Error in biometric authentication:', error);
    if (error instanceof Error) {
      throw new Error(`Authentication failed: ${error.message}`);
    }
    throw new Error('Authentication failed. Please try again.');
  }
}

export async function isPasskeyAvailable(userId: string): Promise<boolean> {
  try {
    // Check if WebAuthn is supported
    if (!window.PublicKeyCredential) {
      return false;
    }

    // Check if any authenticator is available (not just platform)
    try {
      const platformAuth = await PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable();
      // Even if platform authenticator is not available, we'll allow cross-platform ones
      if (!platformAuth) {
        console.log('Platform authenticator not available, falling back to cross-platform');
      }
    } catch (error) {
      console.log('Error checking platform authenticator, falling back to cross-platform');
    }

    // Check if user has registered credentials
    const { data: credentials, error } = await supabase
      .from('biometric_credentials')
      .select('credential_id')
      .eq('user_id', userId)
      .single();

    if (error || !credentials) {
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error checking passkey availability:', error);
    return false;
  }
}

export function isBiometricsAvailable(): boolean {
  return window.PublicKeyCredential !== undefined &&
         PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable !== undefined;
} 