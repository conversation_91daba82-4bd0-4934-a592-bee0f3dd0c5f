import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardT<PERSON>le,
  CardFooter,
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/lib/supabase";
import { useAuth } from "@/context/AuthContext";
import { useSchool } from "@/context/SchoolContext";
import { useTranslation } from "react-i18next";
import {
  Loader2,
  Save,
  Upload,
  RefreshCw,
  Palette,
  Copy,
  Check,
  AlertTriangle,
  ShieldAlert,
  Info,
  Image as ImageIcon,
  Plus,
} from "lucide-react";
import { <PERSON>ert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { ColorPicker } from "@/components/ui/color-picker";
import { School } from "@/lib/types";
import { Badge } from "@/components/ui/badge";
import SimpleCarouselManager from "@/components/admin/SimpleCarouselManager";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

export default function SchoolSettings() {
  const { toast } = useToast();
  const { profile } = useAuth();
  const { currentSchool, refreshSchool } = useSchool();
  const [activeTab, setActiveTab] = useState("branding");
  const { t } = useTranslation();
  const [saving, setSaving] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);

  // Branding state
  const [schoolName, setSchoolName] = useState("");
  const [primaryColor, setPrimaryColor] = useState("#4f46e5");
  const [secondaryColor, setSecondaryColor] = useState("#f97316");
  const [logoUrl, setLogoUrl] = useState("");
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState("");

  // Notification settings state
  const [emailNotificationsEnabled, setEmailNotificationsEnabled] =
    useState(true);
  const [smsNotificationsEnabled, setSmsNotificationsEnabled] = useState(false);

  // Security settings state
  const [invitationCode, setInvitationCode] = useState("");
  const [invitationExpiry, setInvitationExpiry] = useState<Date | null>(null);
  const [showInvitationCode, setShowInvitationCode] = useState(false);
  const [codeCopied, setCodeCopied] = useState(false);

  // System override states
  const [systemOverrides, setSystemOverrides] = useState<any[]>([]);
  const [emailOverridden, setEmailOverridden] = useState(false);
  const [smsOverridden, setSmsOverridden] = useState(false);
  const [locationVerificationOverridden, setLocationVerificationOverridden] =
    useState(false);
  const [biometricVerificationOverridden, setBiometricVerificationOverridden] =
    useState(false);
  const [pinVerificationOverridden, setPinVerificationOverridden] =
    useState(false);
  const [wifiVerificationOverridden, setWifiVerificationOverridden] =
    useState(false);

  // Dashboard message states
  const [customStudentMessage, setCustomStudentMessage] = useState("");
  const [customTeacherMessage, setCustomTeacherMessage] = useState("");

  useEffect(() => {
    if (currentSchool) {
      // Load basic school info
      setSchoolName(currentSchool.name || "");

      // Set default branding values from the schools table
      setPrimaryColor(currentSchool.primaryColor || "#4f46e5");
      setSecondaryColor(currentSchool.secondaryColor || "#f97316");
      setLogoUrl(currentSchool.logoUrl || "");
      setLogoPreview(currentSchool.logoUrl || "");

      // Load invitation code - ensure it's properly set and logged
      console.log("Current school data:", currentSchool);
      if (currentSchool.invitationCode) {
        console.log(
          "Setting invitation code from school data:",
          currentSchool.invitationCode
        );
        setInvitationCode(currentSchool.invitationCode);
      } else {
        console.log("No invitation code found in school data");
        setInvitationCode("");
      }

      // Load branding data from the dedicated school_branding table
      loadBrandingSettings();

      // Load other settings from school_settings table
      loadSchoolSettings();
    }
  }, [currentSchool]);

  // Load branding settings from the dedicated school_branding table
  const loadBrandingSettings = async () => {
    if (!currentSchool?.id) return;

    try {
      // Try to get branding data from the dedicated table first
      const { data: brandingData, error: brandingError } = await supabase
        .from("school_branding")
        .select("*")
        .eq("school_id", currentSchool.id)
        .maybeSingle();

      console.log("Branding data query result:", {
        brandingData,
        brandingError,
      });

      if (brandingData && !brandingError) {
        console.log(
          "Loaded branding data from school_branding table:",
          brandingData
        );
        // Update state with the branding data
        setPrimaryColor(
          brandingData.primary_color || currentSchool.primaryColor || "#4f46e5"
        );
        setSecondaryColor(
          brandingData.secondary_color ||
            currentSchool.secondaryColor ||
            "#f97316"
        );

        if (brandingData.logo_url) {
          setLogoUrl(brandingData.logo_url);
          setLogoPreview(brandingData.logo_url);
        }

        // Apply the theme immediately
        try {
          const { hexToHSL } = await import("@/lib/utils/color-utils");

          // Apply primary color
          const primaryHsl = hexToHSL(brandingData.primary_color);
          document.documentElement.style.setProperty("--primary", primaryHsl);
          document.documentElement.style.setProperty(
            "--primary-color",
            brandingData.primary_color
          );

          // Apply secondary color
          const secondaryHsl = hexToHSL(brandingData.secondary_color);
          document.documentElement.style.setProperty(
            "--secondary",
            secondaryHsl
          );
          document.documentElement.style.setProperty(
            "--secondary-color",
            brandingData.secondary_color
          );
        } catch (themeError) {
          console.error("Error applying theme from branding data:", themeError);
        }
      } else if (brandingError && brandingError.code !== "PGRST116") {
        // PGRST116 is "not found" error, which is expected if no record exists yet
        console.error("Error loading branding data:", brandingError);
      } else {
        console.log(
          "No branding data found in school_branding table, using defaults from schools table"
        );
      }
    } catch (error) {
      console.error("Error in loadBrandingSettings:", error);
    }
  };

  const loadSchoolSettings = async () => {
    if (!currentSchool?.id) return;

    try {
      // Load school settings
      const { data, error } = await supabase
        .from("school_settings")
        .select("*")
        .eq("school_id", currentSchool.id)
        .single();

      if (error && error.code !== "PGRST116") {
        console.error("Error loading school settings:", error);
        return;
      }

      if (data) {
        // Set notification settings
        setEmailNotificationsEnabled(data.email_notifications_enabled ?? true);
        setSmsNotificationsEnabled(data.sms_notifications_enabled ?? false);

        // Set invitation expiry if available
        if (data.invitation_expiry) {
          setInvitationExpiry(new Date(data.invitation_expiry));
        }

        // Set dashboard messages if available
        setCustomStudentMessage(data.custom_student_message || "");
        setCustomTeacherMessage(data.custom_teacher_message || "");
      }

      // Load system overrides
      const { data: overridesData, error: overridesError } = await supabase
        .from("system_school_settings_overrides")
        .select("*")
        .or(`school_id.eq.${currentSchool.id},applies_to_all.eq.true`);

      if (overridesError) {
        console.error("Error loading system overrides:", overridesError);
        return;
      }

      setSystemOverrides(overridesData || []);

      // Check for overrides and apply them
      if (overridesData && overridesData.length > 0) {
        // Process email notifications override
        const emailOverride = overridesData.find(
          (o) =>
            o.setting_name === "email_notifications_enabled" &&
            o.override_enabled
        );
        if (emailOverride) {
          setEmailOverridden(true);
          setEmailNotificationsEnabled(emailOverride.setting_value.value);
        } else {
          setEmailOverridden(false);
        }

        // Process SMS notifications override
        const smsOverride = overridesData.find(
          (o) =>
            o.setting_name === "sms_notifications_enabled" && o.override_enabled
        );
        if (smsOverride) {
          setSmsOverridden(true);
          setSmsNotificationsEnabled(smsOverride.setting_value.value);
        } else {
          setSmsOverridden(false);
        }

        // Process location verification override
        const locationOverride = overridesData.find(
          (o) =>
            o.setting_name === "require_location_verification" &&
            o.override_enabled
        );
        if (locationOverride) {
          setLocationVerificationOverridden(true);
        } else {
          setLocationVerificationOverridden(false);
        }

        // Process biometric verification override
        const biometricOverride = overridesData.find(
          (o) =>
            o.setting_name === "require_biometric_verification" &&
            o.override_enabled
        );
        if (biometricOverride) {
          setBiometricVerificationOverridden(true);
        } else {
          setBiometricVerificationOverridden(false);
        }

        // Process PIN verification override
        const pinOverride = overridesData.find(
          (o) =>
            o.setting_name === "allow_pin_verification" && o.override_enabled
        );
        if (pinOverride) {
          setPinVerificationOverridden(true);
        } else {
          setPinVerificationOverridden(false);
        }

        // Process WiFi verification override
        const wifiOverride = overridesData.find(
          (o) =>
            o.setting_name === "allow_wifi_verification" && o.override_enabled
        );
        if (wifiOverride) {
          setWifiVerificationOverridden(true);
        } else {
          setWifiVerificationOverridden(false);
        }
      } else {
        // Reset all override flags if no overrides found
        setEmailOverridden(false);
        setSmsOverridden(false);
        setLocationVerificationOverridden(false);
        setBiometricVerificationOverridden(false);
        setPinVerificationOverridden(false);
        setWifiVerificationOverridden(false);
      }
    } catch (error) {
      console.error("Error loading school settings:", error);
    }
  };

  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    setLogoFile(file);

    // Create preview
    const reader = new FileReader();
    reader.onloadend = () => {
      setLogoPreview(reader.result as string);
    };
    reader.readAsDataURL(file);
  };

  const uploadLogo = async (): Promise<string | null> => {
    if (!logoFile || !currentSchool?.id) return null;

    setUploading(true);
    try {
      // Create a unique file path with a consistent naming pattern for easier management
      const fileExt = logoFile.name.split(".").pop();
      const fileName = `school-logo-${Date.now()}.${fileExt}`;
      const filePath = `school-logos/${currentSchool.id}/${fileName}`;

      // Upload to Supabase Storage
      console.log("Starting logo upload process...");

      // First check if the bucket exists, if not create it
      const { data: buckets, error: bucketsError } =
        await supabase.storage.listBuckets();

      if (bucketsError) {
        console.error("Error listing buckets:", bucketsError);
        throw bucketsError;
      }

      console.log("Available buckets:", buckets);
      const publicBucket = buckets?.find((bucket) => bucket.name === "public");

      // Use the school-logos bucket that we've created in the database
      const bucketName = "school-logos";
      console.log("Using school-logos bucket for logo upload");

      // If there's an existing logo, delete it to clean up storage
      if (logoUrl) {
        try {
          // Check if the URL is valid and has the expected format
          if (logoUrl.includes("/storage/v1/object/")) {
            // Extract the path from the URL
            const urlObj = new URL(logoUrl);
            const pathParts = urlObj.pathname.split("/");
            // The path should be in the format /storage/v1/object/public/[path]
            // or /storage/v1/object/school-logos/[path]

            // Find the bucket name in the path
            const bucketIndex = pathParts.findIndex(
              (part) => part === "public" || part === "school-logos"
            );

            if (bucketIndex !== -1) {
              const oldBucketName = pathParts[bucketIndex];
              const oldPath = pathParts.slice(bucketIndex + 1).join("/");

              if (oldPath) {
                console.log(
                  "Deleting old logo from bucket:",
                  oldBucketName,
                  "path:",
                  oldPath
                );
                // Delete the old file
                const { error: deleteError } = await supabase.storage
                  .from(oldBucketName)
                  .remove([oldPath]);

                if (deleteError) {
                  console.error("Error deleting old logo:", deleteError);
                  // Continue with upload even if delete fails
                } else {
                  console.log("Old logo deleted successfully");
                }
              }
            } else {
              console.log("Could not determine bucket name from URL:", logoUrl);
            }
          } else {
            console.log("Logo URL does not match expected format:", logoUrl);
          }
        } catch (deleteError) {
          console.error("Error processing old logo URL:", deleteError);
          // Continue with upload even if delete fails
        }
      }

      // Now upload the new file
      console.log(`Uploading file to ${bucketName}/${filePath}`);

      try {
        // Log the current user to help debug permissions
        const {
          data: { user },
        } = await supabase.auth.getUser();
        console.log("Current user:", user?.id);

        // Check if bucket exists by listing buckets instead of using getBucket
        try {
          const { data: buckets } = await supabase.storage.listBuckets();
          const bucket = buckets?.find((b) => b.name === bucketName);
          console.log("Bucket found:", bucket ? "Yes" : "No", bucket);
        } catch (bucketError) {
          console.log("Error checking bucket:", bucketError);
          // Continue with upload even if bucket check fails
        }

        // Upload the file
        const { error: uploadError } = await supabase.storage
          .from(bucketName)
          .upload(filePath, logoFile, {
            cacheControl: "3600",
            upsert: true,
          });

        if (uploadError) {
          console.error("Error uploading file:", uploadError);
          throw uploadError;
        }

        console.log("File uploaded successfully");

        // Get public URL
        const { data } = supabase.storage
          .from(bucketName)
          .getPublicUrl(filePath);

        console.log("Generated public URL:", data.publicUrl);

        // Ensure the SchoolThemeProvider is updated with the new logo URL
        try {
          const { useSchoolTheme } = await import(
            "@/components/providers/SchoolThemeProvider"
          );
          // This might fail if called outside of a component context
          const schoolTheme = useSchoolTheme();
          schoolTheme.setLogoUrl(data.publicUrl);
          console.log("Updated SchoolThemeProvider with new logo URL");
        } catch (providerError) {
          console.log(
            "Could not update SchoolThemeProvider directly (expected if outside component context):",
            providerError
          );
        }

        // Return the URL for use in the save function
        return data.publicUrl;
      } catch (innerError) {
        console.error("Error in file upload process:", innerError);
        throw innerError;
      }
    } catch (error) {
      console.error("Error uploading logo:", error);
      toast({
        title: t("admin.schoolSettings.uploadFailed", "Upload Failed"),
        description: t(
          "admin.schoolSettings.uploadFailedDescription",
          "There was an error uploading your logo. Please try again."
        ),
        variant: "destructive",
      });
      return null;
    } finally {
      setUploading(false);
    }
  };

  const saveBrandingSettings = async () => {
    if (!currentSchool?.id) return;

    setSaving(true);
    setSaveSuccess(false);
    try {
      // Upload logo if a new one was selected and get the URL
      let finalLogoUrl = logoUrl;
      if (logoFile) {
        const uploadedUrl = await uploadLogo();
        if (uploadedUrl) {
          finalLogoUrl = uploadedUrl;
          setLogoUrl(uploadedUrl); // Update state with the new URL
        }
      }

      // Prepare the update data
      const updateData = {
        name: schoolName,
        primary_color: primaryColor,
        secondary_color: secondaryColor,
        logo_url: finalLogoUrl,
        updated_at: new Date().toISOString(),
      };

      console.log("Updating school with data:", updateData);

      // Update school record
      const { error, data } = await supabase
        .from("schools")
        .update(updateData)
        .eq("id", currentSchool.id)
        .select();

      // Check if a branding record already exists for this school
      const { data: existingBranding, error: checkError } = await supabase
        .from("school_branding")
        .select("id")
        .eq("school_id", currentSchool.id)
        .maybeSingle();

      console.log("Checking for existing branding:", {
        existingBranding,
        checkError,
      });

      let brandingError;

      if (existingBranding?.id) {
        // Update existing record
        console.log("Updating existing branding record:", existingBranding.id);
        const { error } = await supabase
          .from("school_branding")
          .update({
            primary_color: primaryColor,
            secondary_color: secondaryColor,
            logo_url: finalLogoUrl,
            updated_at: new Date().toISOString(),
          })
          .eq("id", existingBranding.id);

        brandingError = error;
      } else {
        // Insert new record
        console.log(
          "Creating new branding record for school:",
          currentSchool.id
        );
        const { error } = await supabase.from("school_branding").insert({
          school_id: currentSchool.id,
          primary_color: primaryColor,
          secondary_color: secondaryColor,
          logo_url: finalLogoUrl,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        });

        brandingError = error;
      }

      if (error) {
        throw error;
      }

      if (brandingError) {
        console.error("Error updating school_branding table:", brandingError);
        // Continue with the process even if the branding table update fails
        // The main schools table update is more important
      }

      // Apply theme changes immediately
      try {
        // Import the hexToHSL function dynamically
        const { hexToHSL } = await import("@/lib/utils/color-utils");

        // Convert hex to HSL for CSS variables
        const primaryHsl = hexToHSL(primaryColor);
        const secondaryHsl = hexToHSL(secondaryColor);

        console.log("Applying theme after save:", {
          primaryColor,
          secondaryColor,
          primaryHsl,
          secondaryHsl,
        });

        // Apply to document root
        document.documentElement.style.setProperty("--primary", primaryHsl);
        document.documentElement.style.setProperty(
          "--primary-color",
          primaryColor
        );

        document.documentElement.style.setProperty("--secondary", secondaryHsl);
        document.documentElement.style.setProperty(
          "--secondary-color",
          secondaryColor
        );

        // Update theme-color meta tag for mobile browsers
        const metaThemeColor = document.querySelector(
          'meta[name="theme-color"]'
        );
        if (metaThemeColor) {
          metaThemeColor.setAttribute("content", primaryColor);
        }

        // Force a CSS refresh
        document.documentElement.classList.add("theme-refreshing");
        setTimeout(() => {
          document.documentElement.classList.remove("theme-refreshing");
        }, 100);

        console.log("Theme applied successfully after save");

        // Try to use the SchoolThemeProvider as well (as a backup)
        try {
          const { useSchoolTheme } = await import(
            "@/components/providers/SchoolThemeProvider"
          );
          // This might fail if called outside of a component context
          const schoolTheme = useSchoolTheme();
          await schoolTheme.applyTheme(primaryColor, secondaryColor);
          console.log("Theme also applied via SchoolThemeProvider");
        } catch (providerError) {
          console.log(
            "Could not use SchoolThemeProvider (expected if outside component context):",
            providerError
          );
          // This is expected to fail when called outside of a component context
        }
      } catch (themeError) {
        console.error("Error applying theme:", themeError);
        // Continue with the save process even if theme application fails
      }

      // Show success toast notification
      toast({
        title: "Settings Saved",
        description:
          "Your school branding settings have been updated successfully.",
        variant: "default",
      });

      // Also show a Sonner toast for more visibility
      import("sonner")
        .then(({ toast: sonnerToast }) => {
          sonnerToast.success("Branding Settings Saved", {
            description:
              "Your school branding settings have been updated successfully.",
            duration: 5000,
          });
        })
        .catch((err) => console.error("Error showing Sonner toast:", err));

      // Refresh school context to update the UI
      await refreshSchool();

      // Clear the logo file state after successful save
      setLogoFile(null);

      // Reset the file input
      const fileInput = document.getElementById("logo") as HTMLInputElement;
      if (fileInput) {
        fileInput.value = "";
      }

      // Set save success state to show success UI
      setSaveSuccess(true);

      // Reset success state after 5 seconds
      setTimeout(() => {
        setSaveSuccess(false);
      }, 5000);
    } catch (error) {
      console.error("Error saving branding settings:", error);
      toast({
        title: "Save Failed",
        description:
          "There was an error saving your settings. Please try again.",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  const saveNotificationSettings = async () => {
    if (!currentSchool?.id) return;

    setSaving(true);
    try {
      // Upsert school settings
      const { error } = await supabase.from("school_settings").upsert({
        school_id: currentSchool.id,
        email_notifications_enabled: emailNotificationsEnabled,
        sms_notifications_enabled: smsNotificationsEnabled,
        updated_at: new Date().toISOString(),
      });

      if (error) {
        throw error;
      }

      toast({
        title: "Settings Saved",
        description:
          "Your school notification settings have been updated successfully.",
      });
    } catch (error) {
      console.error("Error saving notification settings:", error);
      toast({
        title: "Save Failed",
        description:
          "There was an error saving your settings. Please try again.",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  const saveDashboardMessages = async () => {
    if (!currentSchool?.id) return;

    setSaving(true);
    try {
      // First check if a school_settings record already exists
      const { data: existingSettings, error: fetchError } = await supabase
        .from("school_settings")
        .select("id")
        .eq("school_id", currentSchool.id)
        .single();

      if (fetchError && fetchError.code !== "PGRST116") {
        // PGRST116 is "not found" error
        throw fetchError;
      }

      const updateData = {
        school_id: currentSchool.id,
        custom_student_message: customStudentMessage.trim() || null,
        custom_teacher_message: customTeacherMessage.trim() || null,
        updated_at: new Date().toISOString(),
      };

      let error;

      if (existingSettings) {
        // Update existing record
        const result = await supabase
          .from("school_settings")
          .update(updateData)
          .eq("id", existingSettings.id);

        error = result.error;
      } else {
        // Create new record
        const result = await supabase
          .from("school_settings")
          .insert(updateData);

        error = result.error;
      }

      if (error) {
        throw error;
      }

      // Clear any system admin messages from the system_school_settings_overrides table
      // This ensures school admin messages take precedence
      try {
        // We don't need to wait for this to complete
        if (customStudentMessage.trim()) {
          await supabase
            .from("system_school_settings_overrides")
            .delete()
            .eq("setting_name", "custom_student_message")
            .eq("school_id", currentSchool.id);
        }

        if (customTeacherMessage.trim()) {
          await supabase
            .from("system_school_settings_overrides")
            .delete()
            .eq("setting_name", "custom_teacher_message")
            .eq("school_id", currentSchool.id);
        }
      } catch (clearError) {
        console.error("Error clearing system overrides:", clearError);
        // Continue even if this fails
      }

      // Show a more prominent success notification
      try {
        // Import and use Sonner toast for a more visible notification
        const { toast: sonnerToast } = await import("sonner");
        sonnerToast.success("Dashboard Messages Saved", {
          description:
            "Your dashboard messages have been updated successfully.",
          duration: 8000,
          position: "top-center",
          style: {
            background: "#10b981",
            color: "white",
            border: "none",
            fontSize: "1rem",
          },
        });
      } catch (err) {
        console.error("Error showing Sonner toast:", err);

        // Fallback to regular toast if Sonner fails
        toast({
          title: "Messages Saved",
          description:
            "Your dashboard messages have been updated successfully.",
          variant: "success",
        });
      }

      // Force the loading state to end after a short delay
      setTimeout(() => {
        setSaving(false);
      }, 1000);
    } catch (error) {
      console.error("Error saving dashboard messages:", error);
      toast({
        title: "Save Failed",
        description:
          "There was an error saving your messages. Please try again.",
        variant: "destructive",
      });
    } finally {
      // We don't set saving to false here because we're using the timeout in the success case
      // In case of error, we still want to end the loading state immediately
      if (error) {
        setSaving(false);
      }
    }
  };

  const regenerateInvitationCode = async () => {
    if (!currentSchool?.id) return;

    setSaving(true);
    try {
      // Generate new code
      const newCode =
        "INV-" + Math.random().toString(36).substring(2, 10).toUpperCase();

      // No expiry date - code will persist indefinitely
      const expiry = null;

      // Update school record
      const { error: schoolError } = await supabase
        .from("schools")
        .update({
          invitation_code: newCode,
          updated_at: new Date().toISOString(),
        })
        .eq("id", currentSchool.id);

      if (schoolError) {
        throw schoolError;
      }

      // First check if a school_settings record already exists
      const { data: existingSettings, error: fetchError } = await supabase
        .from("school_settings")
        .select("id")
        .eq("school_id", currentSchool.id)
        .single();

      if (fetchError && fetchError.code !== "PGRST116") {
        // PGRST116 is "not found" error
        throw fetchError;
      }

      let settingsError;

      if (existingSettings) {
        // Update existing record
        const { error } = await supabase
          .from("school_settings")
          .update({
            invitation_expiry: null, // No expiry date
            updated_at: new Date().toISOString(),
          })
          .eq("id", existingSettings.id);

        settingsError = error;
      } else {
        // Create new record
        const { error } = await supabase.from("school_settings").insert({
          school_id: currentSchool.id,
          invitation_expiry: null, // No expiry date
          updated_at: new Date().toISOString(),
        });

        settingsError = error;
      }

      if (settingsError) {
        throw settingsError;
      }

      // Update local state first
      setInvitationCode(newCode);
      setInvitationExpiry(expiry);

      // Automatically show the code when regenerated
      setShowInvitationCode(true);

      toast({
        title: "Invitation Code Regenerated",
        description:
          "A new invitation code has been generated for your school.",
      });

      // Manually refresh the school context to ensure the invitation code is properly saved
      // This is important to make sure the code persists after page reload
      await refreshSchool();

      // Log the updated school data to verify the invitation code is properly saved
      console.log("School refreshed after regenerating invitation code");
    } catch (error) {
      console.error("Error regenerating invitation code:", error);
      toast({
        title: "Regeneration Failed",
        description:
          "There was an error generating a new invitation code. Please try again.",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  if (!currentSchool) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>School Settings</CardTitle>
          <CardDescription>
            Customize your school settings and appearance
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center p-8">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("admin.schoolSettings.title")}</CardTitle>
        <CardDescription>
          {t("admin.schoolSettings.description")}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2 sm:grid-cols-5 gap-1 sm:gap-0">
            <TabsTrigger value="branding" className="text-xs sm:text-sm">
              {t("admin.schoolSettings.branding")}
            </TabsTrigger>
            <TabsTrigger value="notifications" className="text-xs sm:text-sm">
              {t("admin.schoolSettings.notifications")}
            </TabsTrigger>
            <TabsTrigger value="security" className="text-xs sm:text-sm">
              {t("admin.schoolSettings.security")}
            </TabsTrigger>
            <TabsTrigger value="carousel" className="text-xs sm:text-sm">
              {t("admin.schoolSettings.carousel")}
            </TabsTrigger>
            <TabsTrigger value="messages" className="text-xs sm:text-sm">
              {t("admin.schoolSettings.messages")}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="branding" className="space-y-4 pt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
              <div className="space-y-3 sm:space-y-4">
                <div className="space-y-1 sm:space-y-2">
                  <Label htmlFor="schoolName" className="text-xs sm:text-sm">
                    {t("admin.schoolSettings.schoolName")}
                  </Label>
                  <Input
                    id="schoolName"
                    value={schoolName}
                    onChange={(e) => setSchoolName(e.target.value)}
                    placeholder={t("admin.schoolSettings.enterSchoolName")}
                    className="h-9 sm:h-10 text-xs sm:text-sm"
                  />
                </div>

                <div className="space-y-1 sm:space-y-2">
                  <Label
                    htmlFor="primaryColor"
                    className="text-xs sm:text-sm flex items-center"
                  >
                    {t("admin.schoolSettings.primaryColor")}
                    <span
                      className="ml-2 inline-block w-3 h-3 sm:w-4 sm:h-4 rounded-full"
                      style={{ backgroundColor: primaryColor }}
                    ></span>
                  </Label>
                  <div className="flex gap-2">
                    <Input
                      id="primaryColor"
                      type="color"
                      value={primaryColor}
                      onChange={(e) => {
                        const newColor = e.target.value;
                        setPrimaryColor(newColor);

                        // Preview the color change immediately
                        try {
                          // Import the hexToHSL function dynamically
                          import("@/lib/utils/color-utils")
                            .then(({ hexToHSL }) => {
                              // Convert hex to HSL for CSS variables
                              const primaryHsl = hexToHSL(newColor);

                              console.log("Previewing primary color:", {
                                newColor,
                                primaryHsl,
                              });

                              // Apply to document root
                              const root = document.documentElement;
                              root.style.setProperty("--primary", primaryHsl);
                              root.style.setProperty(
                                "--primary-color",
                                newColor
                              );

                              // Force a CSS refresh
                              root.classList.add("theme-refreshing");
                              setTimeout(() => {
                                root.classList.remove("theme-refreshing");
                              }, 100);
                            })
                            .catch((error) => {
                              console.error(
                                "Error importing color utilities:",
                                error
                              );
                            });
                        } catch (error) {
                          console.error(
                            "Error previewing primary color:",
                            error
                          );
                        }
                      }}
                      className="w-10 sm:w-12 h-8 sm:h-10 p-1 cursor-pointer"
                    />
                    <Input
                      value={primaryColor}
                      onChange={(e) => {
                        const newColor = e.target.value;
                        setPrimaryColor(newColor);

                        // Only apply if it's a valid hex color
                        if (/^#([0-9A-F]{3}){1,2}$/i.test(newColor)) {
                          try {
                            // Import the hexToHSL function dynamically
                            import("@/lib/utils/color-utils")
                              .then(({ hexToHSL }) => {
                                // Convert hex to HSL for CSS variables
                                const primaryHsl = hexToHSL(newColor);

                                console.log(
                                  "Previewing primary color from text input:",
                                  { newColor, primaryHsl }
                                );

                                // Apply to document root
                                const root = document.documentElement;
                                root.style.setProperty("--primary", primaryHsl);
                                root.style.setProperty(
                                  "--primary-color",
                                  newColor
                                );

                                // Force a CSS refresh
                                root.classList.add("theme-refreshing");
                                setTimeout(() => {
                                  root.classList.remove("theme-refreshing");
                                }, 100);
                              })
                              .catch((error) => {
                                console.error(
                                  "Error importing color utilities:",
                                  error
                                );
                              });
                          } catch (error) {
                            console.error(
                              "Error previewing primary color:",
                              error
                            );
                          }
                        }
                      }}
                      className="flex-1 h-8 sm:h-10 text-xs sm:text-sm"
                    />
                  </div>
                  <div className="flex items-center gap-2 mt-1">
                    <div className="w-4 h-4 sm:w-6 sm:h-6 rounded-full bg-primary"></div>
                    <span className="text-xs text-muted-foreground">
                      {t(
                        "admin.schoolSettings.primaryElementsWillUseThisColor"
                      )}
                    </span>
                  </div>
                </div>

                <div className="space-y-1 sm:space-y-2">
                  <Label
                    htmlFor="secondaryColor"
                    className="text-xs sm:text-sm flex items-center"
                  >
                    {t("admin.schoolSettings.secondaryColor")}
                    <span
                      className="ml-2 inline-block w-3 h-3 sm:w-4 sm:h-4 rounded-full"
                      style={{ backgroundColor: secondaryColor }}
                    ></span>
                  </Label>
                  <div className="flex gap-2">
                    <Input
                      id="secondaryColor"
                      type="color"
                      value={secondaryColor}
                      onChange={(e) => {
                        const newColor = e.target.value;
                        setSecondaryColor(newColor);

                        // Preview the color change immediately
                        try {
                          // Import the hexToHSL function dynamically
                          import("@/lib/utils/color-utils")
                            .then(({ hexToHSL }) => {
                              // Convert hex to HSL for CSS variables
                              const secondaryHsl = hexToHSL(newColor);

                              console.log("Previewing secondary color:", {
                                newColor,
                                secondaryHsl,
                              });

                              // Apply to document root
                              const root = document.documentElement;
                              root.style.setProperty(
                                "--secondary",
                                secondaryHsl
                              );
                              root.style.setProperty(
                                "--secondary-color",
                                newColor
                              );

                              // Force a CSS refresh
                              root.classList.add("theme-refreshing");
                              setTimeout(() => {
                                root.classList.remove("theme-refreshing");
                              }, 100);
                            })
                            .catch((error) => {
                              console.error(
                                "Error importing color utilities:",
                                error
                              );
                            });
                        } catch (error) {
                          console.error(
                            "Error previewing secondary color:",
                            error
                          );
                        }
                      }}
                      className="w-10 sm:w-12 h-8 sm:h-10 p-1 cursor-pointer"
                    />
                    <Input
                      value={secondaryColor}
                      onChange={(e) => {
                        const newColor = e.target.value;
                        setSecondaryColor(newColor);

                        // Only apply if it's a valid hex color
                        if (/^#([0-9A-F]{3}){1,2}$/i.test(newColor)) {
                          try {
                            // Import the hexToHSL function dynamically
                            import("@/lib/utils/color-utils")
                              .then(({ hexToHSL }) => {
                                // Convert hex to HSL for CSS variables
                                const secondaryHsl = hexToHSL(newColor);

                                console.log(
                                  "Previewing secondary color from text input:",
                                  { newColor, secondaryHsl }
                                );

                                // Apply to document root
                                const root = document.documentElement;
                                root.style.setProperty(
                                  "--secondary",
                                  secondaryHsl
                                );
                                root.style.setProperty(
                                  "--secondary-color",
                                  newColor
                                );

                                // Force a CSS refresh
                                root.classList.add("theme-refreshing");
                                setTimeout(() => {
                                  root.classList.remove("theme-refreshing");
                                }, 100);
                              })
                              .catch((error) => {
                                console.error(
                                  "Error importing color utilities:",
                                  error
                                );
                              });
                          } catch (error) {
                            console.error(
                              "Error previewing secondary color:",
                              error
                            );
                          }
                        }
                      }}
                      className="flex-1 h-8 sm:h-10 text-xs sm:text-sm"
                    />
                  </div>
                  <div className="flex items-center gap-2 mt-1">
                    <div className="w-4 h-4 sm:w-6 sm:h-6 rounded-full bg-secondary"></div>
                    <span className="text-xs text-muted-foreground">
                      {t(
                        "admin.schoolSettings.secondaryElementsWillUseThisColor"
                      )}
                    </span>
                  </div>
                </div>

                <div className="p-3 sm:p-4 bg-primary/5 border border-primary/20 rounded-md mt-3 sm:mt-4">
                  <div className="flex items-center gap-2">
                    <Info className="h-3 w-3 sm:h-4 sm:w-4 text-primary" />
                    <p className="text-xs sm:text-sm font-medium text-primary">
                      {t("admin.schoolSettings.colorPreview")}
                    </p>
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    {t("admin.schoolSettings.colorsArePreviewedInRealTime")}
                  </p>
                  <div className="flex gap-2 mt-3">
                    <Button
                      size="sm"
                      className="bg-primary text-primary-foreground hover:bg-primary/90 text-xs sm:text-sm h-7 sm:h-8"
                    >
                      {t("admin.schoolSettings.primaryButton")}
                    </Button>
                    <Button
                      size="sm"
                      variant="secondary"
                      className="bg-secondary text-secondary-foreground hover:bg-secondary/90 text-xs sm:text-sm h-7 sm:h-8"
                    >
                      {t("admin.schoolSettings.secondaryButton")}
                    </Button>
                  </div>
                </div>
              </div>

              <div className="space-y-3 sm:space-y-4">
                <div className="space-y-1 sm:space-y-2">
                  <Label htmlFor="logo" className="text-xs sm:text-sm">
                    {t("admin.schoolSettings.schoolLogo")}
                  </Label>
                  <div className="flex flex-col items-center p-3 sm:p-4 border-2 border-dashed rounded-md">
                    {logoPreview ? (
                      <div className="relative w-full h-24 sm:h-32 mb-3 sm:mb-4">
                        <img
                          src={logoPreview}
                          alt="School logo preview"
                          className="w-full h-full object-contain"
                        />
                        {logoFile && (
                          <Badge className="absolute top-2 right-2 bg-primary text-primary-foreground text-xs">
                            {t("admin.schoolSettings.newLogoSelected")}
                          </Badge>
                        )}
                      </div>
                    ) : (
                      <div className="flex flex-col items-center justify-center h-24 sm:h-32 w-full mb-3 sm:mb-4 bg-muted/20">
                        <Palette className="h-8 w-8 sm:h-10 sm:w-10 text-muted-foreground mb-2" />
                        <p className="text-xs sm:text-sm text-muted-foreground">
                          {t("admin.schoolSettings.noLogoUploaded")}
                        </p>
                      </div>
                    )}

                    <Input
                      id="logo"
                      type="file"
                      accept="image/*"
                      onChange={handleLogoChange}
                      className="hidden"
                    />
                    <div className="flex gap-2">
                      <Label
                        htmlFor="logo"
                        className="cursor-pointer inline-flex items-center justify-center whitespace-nowrap rounded-md text-xs sm:text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-8 sm:h-10 px-3 sm:px-4 py-1 sm:py-2"
                      >
                        <Upload className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
                        {logoPreview
                          ? t("admin.schoolSettings.changeLogo")
                          : t("admin.schoolSettings.chooseLogo")}
                      </Label>

                      {logoPreview && !logoFile && (
                        <Button
                          variant="outline"
                          className="h-8 sm:h-10 px-3 sm:px-4 py-1 sm:py-2 text-destructive border-destructive/20 hover:bg-destructive/10 text-xs sm:text-sm"
                          onClick={async () => {
                            // Show confirmation dialog
                            if (
                              confirm(
                                t(
                                  "admin.schoolSettings.confirmRemoveLogo",
                                  "Are you sure you want to remove the school logo? This action cannot be undone."
                                )
                              )
                            ) {
                              try {
                                // Delete the logo file from storage
                                if (logoUrl) {
                                  try {
                                    // Extract the path from the URL
                                    const urlObj = new URL(logoUrl);
                                    const pathParts =
                                      urlObj.pathname.split("/");
                                    // The path should be in the format /storage/v1/object/public/[path]
                                    const oldPath = pathParts
                                      .slice(5)
                                      .join("/");

                                    if (oldPath) {
                                      console.log("Deleting logo:", oldPath);
                                      // Delete the file
                                      const { error: deleteError } =
                                        await supabase.storage
                                          .from("public")
                                          .remove([oldPath]);

                                      if (deleteError) {
                                        console.error(
                                          "Error deleting logo:",
                                          deleteError
                                        );
                                        // Continue even if delete fails
                                      } else {
                                        console.log(
                                          "Logo deleted successfully from storage"
                                        );
                                      }
                                    }
                                  } catch (deleteError) {
                                    console.error(
                                      "Error processing logo URL for deletion:",
                                      deleteError
                                    );
                                  }
                                }

                                // Clear the logo URL in the UI
                                setLogoPreview("");
                                setLogoUrl("");

                                // Update the database to remove the logo URL
                                if (currentSchool?.id) {
                                  // Update schools table
                                  const { error: schoolError } = await supabase
                                    .from("schools")
                                    .update({
                                      logo_url: null,
                                      updated_at: new Date().toISOString(),
                                    })
                                    .eq("id", currentSchool.id);

                                  if (schoolError) {
                                    console.error(
                                      "Error updating school record:",
                                      schoolError
                                    );
                                  }

                                  // Update school_branding table
                                  const { error: brandingError } =
                                    await supabase
                                      .from("school_branding")
                                      .update({
                                        logo_url: null,
                                        updated_at: new Date().toISOString(),
                                      })
                                      .eq("school_id", currentSchool.id);

                                  if (brandingError) {
                                    console.error(
                                      "Error updating branding record:",
                                      brandingError
                                    );
                                  }
                                }

                                // Show success message
                                toast({
                                  title: t(
                                    "admin.schoolSettings.logoRemoved",
                                    "Logo Removed"
                                  ),
                                  description: t(
                                    "admin.schoolSettings.logoRemovedSuccess",
                                    "Your school logo has been removed successfully."
                                  ),
                                });

                                // Also show a Sonner toast
                                import("sonner").then(
                                  ({ toast: sonnerToast }) => {
                                    sonnerToast.success(
                                      t(
                                        "admin.schoolSettings.logoRemoved",
                                        "Logo Removed"
                                      ),
                                      {
                                        description: t(
                                          "admin.schoolSettings.logoRemovedSuccess",
                                          "Your school logo has been removed successfully."
                                        ),
                                      }
                                    );
                                  }
                                );

                                // Refresh school context
                                refreshSchool();
                              } catch (error) {
                                console.error("Error removing logo:", error);
                                toast({
                                  title: t("common.error", "Error"),
                                  description: t(
                                    "admin.schoolSettings.logoRemoveError",
                                    "There was an error removing the logo. Please try again."
                                  ),
                                  variant: "destructive",
                                });
                              }
                            }
                          }}
                        >
                          {t("admin.schoolSettings.remove")}
                        </Button>
                      )}

                      {logoFile && (
                        <Button
                          variant="outline"
                          className="h-8 sm:h-10 px-3 sm:px-4 py-1 sm:py-2 text-destructive border-destructive/20 hover:bg-destructive/10 text-xs sm:text-sm"
                          onClick={() => {
                            setLogoFile(null);
                            // If we had a previous logo, restore it
                            if (logoUrl) {
                              setLogoPreview(logoUrl);
                            } else {
                              setLogoPreview("");
                            }

                            // Reset the file input
                            const fileInput = document.getElementById(
                              "logo"
                            ) as HTMLInputElement;
                            if (fileInput) {
                              fileInput.value = "";
                            }
                          }}
                        >
                          {t("common.cancel")}
                        </Button>
                      )}
                    </div>
                  </div>

                  {logoFile && (
                    <div className="mt-2 p-2 sm:p-3 bg-primary/5 border border-primary/20 rounded-md">
                      <div className="flex items-center gap-2">
                        <Info className="h-3 w-3 sm:h-4 sm:w-4 text-primary" />
                        <p className="text-xs sm:text-sm font-medium text-primary">
                          {t("admin.schoolSettings.newLogoSelected")}
                        </p>
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        {t("admin.schoolSettings.clickSaveBrandingToUpload")}
                      </p>
                    </div>
                  )}
                </div>

                <div className="p-3 sm:p-4 bg-muted/20 rounded-md">
                  <p className="text-xs sm:text-sm text-muted-foreground">
                    {t("admin.schoolSettings.yourSchoolColorsAndLogo")}
                  </p>
                </div>
              </div>
            </div>

            <div className="flex justify-end gap-2">
              {saveSuccess && (
                <div className="hidden sm:flex items-center bg-green-100 text-green-700 px-3 py-2 rounded-md">
                  <Check className="mr-2 h-3 w-3 sm:h-4 sm:w-4" />
                  <span className="text-xs sm:text-sm font-medium">
                    {t("admin.schoolSettings.savedSuccessfully")}
                  </span>
                </div>
              )}
              <Button
                onClick={saveBrandingSettings}
                disabled={saving}
                className={`text-xs sm:text-sm h-8 sm:h-10 px-3 sm:px-4 py-1 sm:py-2 ${
                  saveSuccess
                    ? "bg-green-600 hover:bg-green-700 text-white"
                    : "bg-primary hover:bg-primary/90 text-primary-foreground"
                }`}
              >
                {saving ? (
                  <>
                    <Loader2 className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4 animate-spin" />
                    {uploading
                      ? t("admin.schoolSettings.uploading")
                      : t("admin.schoolSettings.saving")}
                  </>
                ) : saveSuccess ? (
                  <>
                    <Check className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
                    {t("admin.schoolSettings.saved")}
                  </>
                ) : (
                  <>
                    <Save className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
                    {t("admin.schoolSettings.saveBranding")}
                  </>
                )}
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="notifications" className="space-y-4 pt-4">
            {systemOverrides.length > 0 &&
              (emailOverridden || smsOverridden) && (
                <Alert className="mb-4">
                  <ShieldAlert className="h-4 w-4" />
                  <AlertTitle>
                    {t("admin.schoolSettings.systemAdminOverride")}
                  </AlertTitle>
                  <AlertDescription>
                    {t("admin.schoolSettings.settingsOverriddenBySystemAdmin")}
                  </AlertDescription>
                </Alert>
              )}

            <div className="space-y-6">
              <div className="flex items-center justify-between space-x-2">
                <div className="space-y-0.5">
                  <div className="flex items-center gap-2">
                    <Label htmlFor="emailNotifications">
                      {t("admin.schoolSettings.emailNotifications")}
                    </Label>
                    {emailOverridden && (
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Badge
                              variant="outline"
                              className="gap-1 border-amber-500 text-amber-500"
                            >
                              <ShieldAlert className="h-3 w-3" />
                              <span className="text-xs">
                                {t("admin.schoolSettings.overridden")}
                              </span>
                            </Badge>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>
                              {t(
                                "admin.schoolSettings.settingControlledBySystemAdmin"
                              )}
                            </p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    )}
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {t(
                      "admin.schoolSettings.enableEmailNotificationsForStudentsAndParents"
                    )}
                  </p>
                </div>
                <Switch
                  id="emailNotifications"
                  checked={emailNotificationsEnabled}
                  onCheckedChange={setEmailNotificationsEnabled}
                  disabled={emailOverridden}
                />
              </div>

              <div className="flex items-center justify-between space-x-2">
                <div className="space-y-0.5">
                  <div className="flex items-center gap-2">
                    <Label htmlFor="smsNotifications">
                      {t("admin.schoolSettings.smsNotifications")}
                    </Label>
                    {smsOverridden && (
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Badge
                              variant="outline"
                              className="gap-1 border-amber-500 text-amber-500"
                            >
                              <ShieldAlert className="h-3 w-3" />
                              <span className="text-xs">
                                {t("admin.schoolSettings.overridden")}
                              </span>
                            </Badge>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>
                              {t(
                                "admin.schoolSettings.settingControlledBySystemAdmin"
                              )}
                            </p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    )}
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {t(
                      "admin.schoolSettings.enableSmsNotificationsForStudentsAndParents"
                    )}
                  </p>
                </div>
                <Switch
                  id="smsNotifications"
                  checked={smsNotificationsEnabled}
                  onCheckedChange={setSmsNotificationsEnabled}
                  disabled={smsOverridden}
                />
              </div>

              <div className="p-4 bg-muted/20 rounded-md">
                <p className="text-sm text-muted-foreground">
                  {t("admin.schoolSettings.notificationPreferencesDescription")}
                </p>
              </div>
            </div>

            <div className="flex justify-end">
              <Button
                onClick={saveNotificationSettings}
                disabled={saving || (emailOverridden && smsOverridden)}
                variant={
                  emailOverridden && smsOverridden ? "outline" : "default"
                }
              >
                {saving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {t("admin.schoolSettings.saving")}
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    {t("admin.schoolSettings.saveNotificationSettings")}
                  </>
                )}
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="carousel" className="space-y-4 pt-4">
            <SimpleCarouselManager />
          </TabsContent>

          <TabsContent value="security" className="space-y-4 pt-4">
            <div className="space-y-6">
              <div className="space-y-2">
                <Label>{t("admin.schoolSettings.schoolInvitationCode")}</Label>

                {!invitationCode && (
                  <Alert variant="destructive" className="mb-4">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertTitle>
                      {t("admin.schoolSettings.missingInvitationCode")}
                    </AlertTitle>
                    <AlertDescription>
                      {t(
                        "admin.schoolSettings.missingInvitationCodeDescription"
                      )}
                    </AlertDescription>
                  </Alert>
                )}

                <div className="flex gap-2">
                  <Input
                    type={showInvitationCode ? "text" : "password"}
                    value={invitationCode}
                    readOnly
                    className={`font-mono ${
                      showInvitationCode
                        ? "bg-primary/5 border-primary text-primary font-medium"
                        : ""
                    }`}
                    placeholder={
                      !invitationCode
                        ? t("admin.schoolSettings.noInvitationCodeGeneratedYet")
                        : ""
                    }
                  />
                  <Button
                    variant="outline"
                    onClick={() => setShowInvitationCode(!showInvitationCode)}
                    disabled={!invitationCode}
                  >
                    {showInvitationCode
                      ? t("admin.schoolSettings.hide")
                      : t("admin.schoolSettings.show")}
                  </Button>
                </div>
                {/* Invitation code no longer expires */}
                {showInvitationCode && invitationCode && (
                  <div className="mt-2 p-3 bg-primary/5 border border-primary/20 rounded-md">
                    <div className="flex items-center justify-between">
                      <div className="font-medium text-primary">
                        {t("admin.schoolSettings.currentInvitationCode")}
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="font-mono font-bold text-primary">
                          {invitationCode}
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0"
                          onClick={() => {
                            navigator.clipboard.writeText(invitationCode);
                            setCodeCopied(true);
                            toast({
                              title: t(
                                "admin.schoolSettings.copiedToClipboard"
                              ),
                              description: t(
                                "admin.schoolSettings.invitationCodeCopiedToClipboard"
                              ),
                            });
                            setTimeout(() => setCodeCopied(false), 2000);
                          }}
                        >
                          {codeCopied ? (
                            <Check className="h-4 w-4 text-green-500" />
                          ) : (
                            <Copy className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              <div className="p-4 bg-muted/20 rounded-md">
                <div className="text-sm text-muted-foreground">
                  {t("admin.schoolSettings.invitationCodeDescription")}
                </div>
              </div>

              <div className="flex justify-end">
                <Button
                  onClick={regenerateInvitationCode}
                  disabled={saving}
                  className={
                    !invitationCode ? "bg-primary hover:bg-primary/90" : ""
                  }
                >
                  {saving ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {t("admin.schoolSettings.regenerating")}
                    </>
                  ) : (
                    <>
                      <RefreshCw className="mr-2 h-4 w-4" />
                      {!invitationCode
                        ? t("admin.schoolSettings.generateInvitationCode")
                        : t("admin.schoolSettings.regenerateInvitationCode")}
                    </>
                  )}
                </Button>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="messages" className="space-y-4 pt-4">
            <div className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="studentMessage">
                  {t("admin.schoolSettings.studentDashboardMessage")}
                </Label>
                <Textarea
                  id="studentMessage"
                  value={customStudentMessage}
                  onChange={(e) => setCustomStudentMessage(e.target.value)}
                  placeholder={t(
                    "admin.schoolSettings.studentMessagePlaceholder"
                  )}
                  className="min-h-[100px] resize-y"
                />
                <p className="text-sm text-muted-foreground">
                  {t("admin.schoolSettings.studentMessageDescription")}
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="teacherMessage">
                  {t("admin.schoolSettings.teacherDashboardMessage")}
                </Label>
                <Textarea
                  id="teacherMessage"
                  value={customTeacherMessage}
                  onChange={(e) => setCustomTeacherMessage(e.target.value)}
                  placeholder={t(
                    "admin.schoolSettings.teacherMessagePlaceholder"
                  )}
                  className="min-h-[100px] resize-y"
                />
                <p className="text-sm text-muted-foreground">
                  {t("admin.schoolSettings.teacherMessageDescription")}
                </p>
              </div>

              <div className="p-4 bg-primary/5 border border-primary/20 rounded-md">
                <div className="flex items-center gap-2">
                  <Info className="h-4 w-4 text-primary" />
                  <p className="text-sm font-medium text-primary">
                    {t("admin.schoolSettings.dashboardMessages")}
                  </p>
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  {t("admin.schoolSettings.dashboardMessagesInfo")}
                </p>
                <p className="text-xs text-muted-foreground mt-1">
                  {t("admin.schoolSettings.emptyMessageInfo")}
                </p>
              </div>

              <div className="flex justify-end">
                <Button onClick={saveDashboardMessages} disabled={saving}>
                  {saving ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {t("admin.schoolSettings.saving")}
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      {t("admin.schoolSettings.saveMessages")}
                    </>
                  )}
                </Button>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
