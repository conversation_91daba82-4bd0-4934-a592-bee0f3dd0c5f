import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/lib/supabase";
import {
  ShieldCheck,
  RefreshCw,
  Copy,
  Check,
  AlertTriangle,
  Eye,
  EyeOff,
} from "lucide-react";
import {
  getSystemAdminCodeRequired,
  updateSystemAdminCodeRequired,
  getSystemAdminCode,
  updateSystemAdminCode,
} from "@/lib/services/system-admin-code-service";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { toast as sonnerToast } from "sonner";

export default function SystemAdminCodeManager() {
  const [newCode, setNewCode] = useState("");
  const [currentCode, setCurrentCode] = useState("");
  const [loading, setLoading] = useState(false);
  const [copied, setCopied] = useState(false);
  const [isCodeRequired, setIsCodeRequired] = useState(true);
  const [updatingRequirement, setUpdatingRequirement] = useState(false);
  const [showCurrentCode, setShowCurrentCode] = useState(false);
  const { toast } = useToast();

  // Fetch code requirement on component mount
  useEffect(() => {
    const fetchCodeRequirement = async () => {
      try {
        const required = await getSystemAdminCodeRequired();
        setIsCodeRequired(required);
      } catch (error) {
        console.error("Error fetching code requirement:", error);
      }
    };

    fetchCodeRequirement();
  }, []);

  // Fetch current code when component mounts or when code is required
  useEffect(() => {
    if (isCodeRequired) {
      fetchCurrentCode();
    }
  }, [isCodeRequired]);

  // Function to toggle code requirement
  const handleToggleCodeRequirement = async (required: boolean) => {
    setUpdatingRequirement(true);
    try {
      const success = await updateSystemAdminCodeRequired(required);
      if (success) {
        setIsCodeRequired(required);
        toast({
          title: "Success",
          description: `System admin code is now ${
            required ? "required" : "not required"
          } for new accounts.`,
        });
      } else {
        toast({
          title: "Error",
          description: "Failed to update code requirement setting.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error updating code requirement:", error);
      toast({
        title: "Error",
        description: "An unexpected error occurred.",
        variant: "destructive",
      });
    } finally {
      setUpdatingRequirement(false);
    }
  };

  // Function to fetch the current system admin code
  const fetchCurrentCode = async () => {
    setLoading(true);
    try {
      const code = await getSystemAdminCode();
      if (code) {
        setCurrentCode(code);
        // Hide the code by default for security
        setShowCurrentCode(false);

        // Show warning if still using default code
        if (code === "INITIAL_SYSTEM_SETUP_CODE") {
          toast({
            title: "Security Warning",
            description:
              "You are still using the default system admin code. Please generate and update to a secure code immediately.",
            variant: "destructive",
          });
        }

        // Show success toast
        sonnerToast.success("Code Retrieved!", {
          description:
            "Current invitation code has been fetched. Click the eye icon to view it.",
          duration: 4000,
        });
      } else {
        setCurrentCode("No code set");
        setShowCurrentCode(true); // Show "No code set" message
        toast({
          title: "No Code Set",
          description:
            "No system admin code has been set. Please generate one.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error fetching system admin code:", error);
      toast({
        title: "Error",
        description: "Failed to fetch the current system admin code.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Function to generate a random code
  const generateRandomCode = () => {
    const characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    let result = "";
    for (let i = 0; i < 12; i++) {
      result += characters.charAt(
        Math.floor(Math.random() * characters.length)
      );
    }
    setNewCode(result);

    // Show Sonner toast for code generation
    sonnerToast.info("New Code Generated!", {
      description: `Generated secure code: ${result}. Click "Update" to save it.`,
      duration: 4000,
    });
  };

  // Function to update the system admin code
  const updateSystemAdminCodeHandler = async () => {
    if (!newCode) {
      toast({
        title: "Error",
        description: "Please enter a new code or generate one.",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    try {
      const success = await updateSystemAdminCode(newCode);

      if (success) {
        setCurrentCode(newCode);
        setNewCode("");

        // Show Sonner toast for success
        sonnerToast.success("System Admin Code Updated!", {
          description: `New invitation code has been set successfully. Old codes are now invalid.`,
          duration: 5000,
        });
      } else {
        sonnerToast.error("Update Failed", {
          description:
            "Failed to update the system admin code. Please try again.",
          duration: 4000,
        });
      }
    } catch (error) {
      console.error("Error updating system admin code:", error);
      sonnerToast.error("Update Failed", {
        description: "An unexpected error occurred while updating the code.",
        duration: 4000,
      });
    } finally {
      setLoading(false);
    }
  };

  // Function to copy the current code to clipboard
  const copyToClipboard = () => {
    navigator.clipboard.writeText(currentCode);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);

    // Show Sonner toast for copy success
    sonnerToast.success("Code Copied!", {
      description: "System admin code has been copied to your clipboard.",
      duration: 3000,
    });
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center gap-2">
          <ShieldCheck className="h-5 w-5 text-primary" />
          <CardTitle>System Admin Code</CardTitle>
        </div>
        <CardDescription>
          Manage the code required for creating system administrator accounts
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Code Requirement Toggle */}
        <div className="space-y-3">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
            <div className="space-y-1 flex-1">
              <Label htmlFor="codeRequired">Require Invitation Code</Label>
              <p className="text-sm text-muted-foreground">
                Control whether new system admin accounts require an invitation
                code
              </p>
            </div>
            <div className="flex justify-end sm:justify-start">
              <Switch
                id="codeRequired"
                checked={isCodeRequired}
                onCheckedChange={handleToggleCodeRequirement}
                disabled={updatingRequirement}
              />
            </div>
          </div>

          {!isCodeRequired && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>Security Warning:</strong> When disabled, anyone can
                create system admin accounts without an invitation code. This
                significantly reduces security. Only disable this in controlled
                environments.
              </AlertDescription>
            </Alert>
          )}
        </div>

        {/* Current Code Section - only show when code is required */}
        {isCodeRequired && (
          <div className="space-y-2">
            <Label htmlFor="currentCode">Current Code</Label>
            <div className="flex flex-col sm:flex-row gap-2">
              <div className="relative flex-1">
                <Input
                  id="currentCode"
                  type={showCurrentCode ? "text" : "password"}
                  value={currentCode || "Click 'Fetch Current Code' to view"}
                  readOnly
                  className="font-mono pr-10"
                />
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setShowCurrentCode(!showCurrentCode)}
                  disabled={!currentCode}
                  title={showCurrentCode ? "Hide code" : "Show code"}
                  className="absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8 shrink-0"
                >
                  {showCurrentCode ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={copyToClipboard}
                  disabled={!currentCode}
                  title="Copy to clipboard"
                  className="shrink-0"
                >
                  {copied ? (
                    <Check className="h-4 w-4" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
                <Button
                  variant="outline"
                  onClick={fetchCurrentCode}
                  disabled={loading}
                  className="shrink-0"
                >
                  {loading ? (
                    <RefreshCw className="h-4 w-4 animate-spin" />
                  ) : (
                    <span className="hidden sm:inline">Fetch Current Code</span>
                  )}
                  {loading ? null : <RefreshCw className="h-4 w-4 sm:hidden" />}
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* New Code Section - only show when code is required */}
        {isCodeRequired && (
          <div className="space-y-2">
            <Label htmlFor="newCode">New Code</Label>
            <div className="flex flex-col sm:flex-row gap-2">
              <Input
                id="newCode"
                value={newCode}
                onChange={(e) => setNewCode(e.target.value)}
                placeholder="Enter new code or generate one"
                className="font-mono flex-1"
              />
              <Button
                variant="outline"
                onClick={generateRandomCode}
                disabled={loading}
                className="shrink-0"
              >
                <span className="hidden sm:inline">Generate</span>
                <span className="sm:hidden">Generate Code</span>
              </Button>
            </div>
          </div>
        )}
      </CardContent>
      {isCodeRequired && (
        <CardFooter className="pt-4">
          <Button
            onClick={updateSystemAdminCodeHandler}
            disabled={loading || !newCode}
            className="w-full"
            size="lg"
          >
            {loading ? (
              <>
                <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                <span className="hidden sm:inline">Updating...</span>
                <span className="sm:hidden">Updating</span>
              </>
            ) : (
              <>
                <span className="hidden sm:inline">
                  Update System Admin Code
                </span>
                <span className="sm:hidden">Update Code</span>
              </>
            )}
          </Button>
        </CardFooter>
      )}
    </Card>
  );
}
