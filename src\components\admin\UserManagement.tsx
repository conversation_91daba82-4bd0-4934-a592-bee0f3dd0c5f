import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useTranslation } from "react-i18next";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
// Removed unused AlertDialog imports
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useToast } from "@/hooks/use-toast";
import { User } from "@/lib/types";
import { supabase } from "@/lib/supabase";
import {
  Settings,
  Search,
  UserPlus,
  Filter,
  Users,
  GraduationCap,
  BookOpen,
  ShieldAlert,
  ShieldCheck,
  AlertTriangle,
  AlertCircle,
} from "lucide-react";
import UserForm from "@/components/admin/UserForm";
import UserDetails from "@/components/admin/UserDetails";
import SchoolAdminDeleteUserDialog from "@/components/admin/SchoolAdminDeleteUserDialog";
import UserMaintenanceDialog from "@/components/admin/UserMaintenanceDialog";
import UserBlockDialog from "@/components/admin/UserBlockDialog";
import { useAuth } from "@/context/AuthContext";
import { getProfiles } from "@/lib/api/queries";

export default function UserManagement() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedRole, setSelectedRole] = useState<
    "all" | "student" | "teacher" | "admin"
  >("all");
  const [activeTab, setActiveTab] = useState("all");
  const { t } = useTranslation();
  const [showAddUser, setShowAddUser] = useState(false);
  const [userToDelete, setUserToDelete] = useState<User | null>(null);
  const [userToMaintenance, setUserToMaintenance] = useState<User | null>(null);
  const [userToBlock, setUserToBlock] = useState<User | null>(null);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const { toast } = useToast();
  const { session, profile } = useAuth();

  // Fetch users from Supabase with school context
  const fetchUsers = async () => {
    setLoading(true);
    try {
      if (!session) {
        throw new Error("Authentication required to access user management");
      }

      // Use the school-based query function
      const data = await getProfiles(profile);

      if (!data || data.length === 0) {
        setUsers([]);
        return;
      }

      const transformedUsers = data.map((user) => {
        let role = (user.role || "student") as "student" | "teacher" | "admin";
        if (!["student", "teacher", "admin"].includes(role)) {
          role = "student";
        }

        return {
          id: user.id,
          user_id: user.user_id,
          name: user.name || "",
          email: user.email || "",
          role: role,
          created_at: user.created_at,
          photoUrl: user.photo_url,
          status:
            user.is_blocked === true
              ? "blocked"
              : user.maintenance_mode === true
              ? "maintenance"
              : "active",
          studentId: user.student_id,
          course: user.course,
          blockName: user.block_name,
          roomNumber: user.room_number,
          teacherId: user.teacher_id,
          department: user.department,
          subject: user.subject,
          position: user.position,
          biometricRegistered: user.biometric_registered,
          pin: user.pin,
          school_id: user.school_id,
          school: user.school,
          accessLevel: user.access_level,
          is_deleted: user.is_deleted,
          deleted_at: user.deleted_at,
          is_blocked: user.is_blocked,
          blocked_at: user.blocked_at,
          maintenance_mode: user.maintenance_mode,
          profile_completed: user.profile_completed,
        };
      });

      setUsers(transformedUsers);
    } catch (error: any) {
      console.error("Error in fetchUsers:", error);
      toast({
        title: "Error",
        description:
          error.message || "Failed to fetch users. Please try again.",
        variant: "destructive",
      });
      setUsers([]);
    } finally {
      setLoading(false);
    }
  };

  // Fetch users when the session is available
  useEffect(() => {
    if (session) {
      fetchUsers();
    }
  }, [session]);

  // Synchronize the tab selection with the selected role
  useEffect(() => {
    setActiveTab(selectedRole);
  }, [selectedRole]);

  // Synchronize the selected role with the active tab
  useEffect(() => {
    setSelectedRole(activeTab as "all" | "student" | "teacher" | "admin");
  }, [activeTab]);

  const handleResetPassword = async (user: User) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(user.email);

      if (error) throw error;

      toast({
        title: "Password Reset Email Sent",
        description: `A password reset link has been sent to ${user.email}`,
      });
    } catch (error: any) {
      console.error("Error resetting password:", error);
      toast({
        title: "Error",
        description: "Failed to send password reset email. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Filter users based on search query and role
  const filteredUsers = users.filter((user) => {
    const matchesSearch =
      searchQuery.toLowerCase().trim() === "" ||
      user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (user.role === "student" &&
        user.studentId?.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (user.role === "teacher" &&
        user.teacherId?.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesRole = selectedRole === "all" || user.role === selectedRole;

    return matchesSearch && matchesRole;
  });

  // Separate users by role
  const students = filteredUsers.filter((user) => user.role === "student");
  const teachers = filteredUsers.filter((user) => user.role === "teacher");
  const admins = filteredUsers.filter((user) => user.role === "admin");

  // Calculate user statistics from the unfiltered users list
  const userStats = {
    total: users.length,
    students: users.filter((u) => u.role === "student").length,
    teachers: users.filter((u) => u.role === "teacher").length,
    admins: users.filter((u) => u.role === "admin").length,
  };

  const renderUserTable = (users: User[], showRole = false) => (
    <div className="rounded-md border overflow-x-auto shadow-sm hover:shadow-md transition-shadow duration-200 max-w-full">
      <Table className="w-full" style={{ minWidth: '300px' }}>
        <TableHeader>
          <TableRow>
            <TableHead className="whitespace-nowrap">{t("admin.userManagement.user")}</TableHead>
            {showRole && (
              <TableHead className="whitespace-nowrap hidden sm:table-cell">{t("admin.userManagement.role")}</TableHead>
            )}
            <TableHead className="whitespace-nowrap hidden sm:table-cell">{t("admin.userManagement.id")}</TableHead>
            <TableHead className="whitespace-nowrap">{t("admin.userManagement.status")}</TableHead>
            <TableHead className="w-[60px] sm:w-[100px] whitespace-nowrap">
              {t("admin.userManagement.actions")}
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {users.length === 0 ? (
            <TableRow>
              <TableCell colSpan={showRole ? 5 : 4} className="text-center">
                {searchQuery ? (
                  <div className="flex flex-col items-center justify-center text-muted-foreground py-8">
                    <Search className="h-8 w-8 mb-2 opacity-50" />
                    <p>
                      {t("admin.userManagement.noUsersMatching")} "{searchQuery}
                      "
                    </p>
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center text-muted-foreground py-8">
                    <Users className="h-8 w-8 mb-2 opacity-50" />
                    <p>{t("admin.userManagement.noUsersFound")}</p>
                  </div>
                )}
              </TableCell>
            </TableRow>
          ) : (
            users.map((user) => (
              <TableRow key={user.id}>
                <TableCell className="py-2 sm:py-4">
                  <button
                    className="flex items-center gap-2 hover:bg-accent hover:text-accent-foreground p-1 rounded-md w-full"
                    onClick={() => setSelectedUser(user)}
                  >
                    <Avatar className="h-7 w-7 sm:h-8 sm:w-8 flex-shrink-0">
                      <AvatarImage src={user.photoUrl} alt={user.name} />
                      <AvatarFallback>
                        {user.name.charAt(0).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex flex-col items-start overflow-hidden max-w-[120px] xs:max-w-[160px] sm:max-w-none">
                      <span className="font-medium text-sm sm:text-base truncate w-full">{user.name}</span>
                      <span className="text-xs sm:text-sm text-muted-foreground truncate w-full">
                        {user.email}
                      </span>
                    </div>
                  </button>
                </TableCell>
                {showRole && (
                  <TableCell className="capitalize hidden sm:table-cell">{user.role}</TableCell>
                )}
                <TableCell className="hidden sm:table-cell">
                  {user.role === "student"
                    ? user.studentId
                    : user.role === "teacher"
                    ? user.teacherId
                    : "N/A"}
                </TableCell>
                <TableCell>
                  {user.is_blocked === true ? (
                    <span className="inline-flex items-center rounded-full px-1.5 sm:px-2 py-0.5 sm:py-1 text-[10px] sm:text-xs font-medium bg-red-100 text-red-800 ring-1 ring-inset ring-red-600/20 whitespace-nowrap">
                      {t("admin.userManagement.blocked")}
                    </span>
                  ) : user.maintenance_mode === true ? (
                    <span className="inline-flex items-center rounded-full px-1.5 sm:px-2 py-0.5 sm:py-1 text-[10px] sm:text-xs font-medium bg-amber-100 text-amber-800 ring-1 ring-inset ring-amber-600/20 whitespace-nowrap">
                      {t("admin.userManagement.maintenance")}
                    </span>
                  ) : (
                    <span className="inline-flex items-center rounded-full px-1.5 sm:px-2 py-0.5 sm:py-1 text-[10px] sm:text-xs font-medium bg-green-100 text-green-800 ring-1 ring-inset ring-green-600/20 whitespace-nowrap">
                      {t("admin.schoolContext.active")}
                    </span>
                  )}
                </TableCell>
                <TableCell className="py-2 sm:py-4">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-7 w-7 sm:h-8 sm:w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <Settings className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        onClick={() => handleResetPassword(user)}
                      >
                        {t("admin.userManagement.resetPassword")}
                      </DropdownMenuItem>

                      <DropdownMenuSeparator />

                      {user.is_blocked === true ? (
                        <DropdownMenuItem
                          onClick={() => setUserToBlock(user)}
                          className="text-green-600"
                        >
                          <ShieldCheck className="h-4 w-4 mr-2" />
                          {t("admin.userManagement.unblockUser")}
                        </DropdownMenuItem>
                      ) : (
                        <DropdownMenuItem
                          onClick={() => setUserToBlock(user)}
                          className="text-red-600"
                        >
                          <ShieldAlert className="h-4 w-4 mr-2" />
                          {t("admin.userManagement.blockUser")}
                        </DropdownMenuItem>
                      )}

                      {user.maintenance_mode === true ? (
                        <DropdownMenuItem
                          onClick={() => setUserToMaintenance(user)}
                          className="text-green-600"
                        >
                          <AlertCircle className="h-4 w-4 mr-2" />
                          {t("admin.userManagement.disableMaintenanceMode")}
                        </DropdownMenuItem>
                      ) : (
                        <DropdownMenuItem
                          onClick={() => setUserToMaintenance(user)}
                          className="text-amber-600"
                        >
                          <AlertTriangle className="h-4 w-4 mr-2" />
                          {t("admin.userManagement.enableMaintenanceMode")}
                        </DropdownMenuItem>
                      )}

                      <DropdownMenuSeparator />

                      <DropdownMenuItem
                        onClick={() => setUserToDelete(user)}
                        className="text-red-600"
                      >
                        {t("admin.userManagement.deleteUser")}
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );

  return (
    <div className="space-y-4 xs:space-y-6 w-full max-w-full mx-auto px-0 overflow-hidden">
      {/* Statistics Cards */}
      <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-4 gap-1.5 xs:gap-2 sm:gap-3 md:gap-4 pb-2 w-full overflow-x-visible">
        <Card className="flex-1 shadow-sm hover:shadow-md transition-shadow duration-200 w-full min-w-[100px]">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1 sm:pb-2 px-2 xs:px-3 sm:px-6">
            <CardTitle className="text-[10px] xs:text-xs sm:text-sm font-medium">
              {t("admin.userManagement.totalUsers")}
            </CardTitle>
            <Users className="h-3 w-3 sm:h-4 sm:w-4 text-primary" />
          </CardHeader>
          <CardContent className="px-2 xs:px-3 sm:px-6 py-1 sm:py-2">
            <div className="text-base xs:text-lg sm:text-2xl font-bold">
              {userStats.total}
            </div>
          </CardContent>
        </Card>
        <Card className="flex-1 shadow-sm hover:shadow-md transition-shadow duration-200 w-full min-w-[100px]">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1 sm:pb-2 px-2 xs:px-3 sm:px-6">
            <CardTitle className="text-[10px] xs:text-xs sm:text-sm font-medium">
              {t("admin.userManagement.students")}
            </CardTitle>
            <GraduationCap className="h-3 w-3 sm:h-4 sm:w-4 text-primary" />
          </CardHeader>
          <CardContent className="px-2 xs:px-3 sm:px-6 py-1 sm:py-2">
            <div className="text-base xs:text-lg sm:text-2xl font-bold">
              {userStats.students}
            </div>
          </CardContent>
        </Card>
        <Card className="flex-1 shadow-sm hover:shadow-md transition-shadow duration-200 w-full min-w-[100px]">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1 sm:pb-2 px-2 xs:px-3 sm:px-6">
            <CardTitle className="text-[10px] xs:text-xs sm:text-sm font-medium">
              {t("admin.userManagement.teachers")}
            </CardTitle>
            <BookOpen className="h-3 w-3 sm:h-4 sm:w-4 text-primary" />
          </CardHeader>
          <CardContent className="px-2 xs:px-3 sm:px-6 py-1 sm:py-2">
            <div className="text-base xs:text-lg sm:text-2xl font-bold">
              {userStats.teachers}
            </div>
          </CardContent>
        </Card>
        <Card className="flex-1 shadow-sm hover:shadow-md transition-shadow duration-200 w-full min-w-[100px]">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1 sm:pb-2 px-2 xs:px-3 sm:px-6">
            <CardTitle className="text-[10px] xs:text-xs sm:text-sm font-medium">
              {t("admin.userManagement.administrators")}
            </CardTitle>
            <Users className="h-3 w-3 sm:h-4 sm:w-4 text-primary" />
          </CardHeader>
          <CardContent className="px-2 xs:px-3 sm:px-6 py-1 sm:py-2">
            <div className="text-base xs:text-lg sm:text-2xl font-bold">
              {userStats.admins}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Card with Tabs */}
      <Card className="shadow-sm hover:shadow-md transition-shadow duration-200 border-t-4 border-t-primary w-full max-w-full mx-auto overflow-hidden">
        <CardHeader className="px-2 xs:px-4 sm:px-6 py-3 xs:py-4 sm:py-6">
          <div className="flex flex-col xs:flex-row justify-between items-start xs:items-center gap-2 xs:gap-4 mb-4">
            <div>
              <CardTitle className="text-sm xs:text-base sm:text-lg flex items-center gap-2">
                <Users className="h-3 w-3 xs:h-4 xs:w-4 sm:h-5 sm:w-5 text-primary hidden xs:inline" />
                {t("admin.userManagement.title")}
              </CardTitle>
              <CardDescription className="text-[10px] xs:text-xs sm:text-sm mt-1">
                {t("admin.userManagement.description")}
              </CardDescription>
            </div>
            <Button
              onClick={() => setShowAddUser(true)}
              className="flex gap-1 xs:gap-2 text-[10px] xs:text-xs sm:text-sm py-1 xs:py-1.5 sm:py-2 h-auto transition-all hover:scale-105"
              size="sm"
            >
              <UserPlus className="h-3 w-3 sm:h-4 sm:w-4" />
              {t("admin.userManagement.addUser")}
            </Button>
          </div>
        </CardHeader>
        
        <CardContent className="px-2 xs:px-4 sm:px-6">
          {/* Search Bar */}
          <div className="flex flex-col xs:flex-row items-start xs:items-center gap-2 xs:gap-3 sm:gap-4 mb-3 xs:mb-4 sm:mb-6 w-full max-w-full">
            <div className="relative flex-1 w-full">
              <Search className="absolute left-2 top-2.5 h-3 w-3 xs:h-3.5 xs:w-3.5 sm:h-4 sm:w-4 text-primary" />
              <Input
                placeholder={t("admin.userManagement.searchPlaceholder")}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-7 xs:pl-8 text-[10px] xs:text-xs sm:text-sm h-8 xs:h-9 sm:h-10 border-primary/20 focus-visible:ring-primary/30 transition-all"
              />
            </div>
          </div>

          {loading ? (
            <div className="text-center py-4">Loading users...</div>
          ) : (
            <Tabs 
              defaultValue="all" 
              value={activeTab} 
              onValueChange={setActiveTab}
              className="w-full"
            >
              <TabsList className="grid grid-cols-4 mb-6 w-full max-w-full overflow-x-auto">
                <TabsTrigger 
                  value="all" 
                  className="flex items-center justify-center gap-1 xs:gap-2 text-[10px] xs:text-xs sm:text-sm"
                  onClick={() => setSelectedRole("all")}
                >
                  <Users className="h-3 w-3 xs:h-3.5 xs:w-3.5 sm:h-4 sm:w-4" />
                  <span className="hidden xs:inline">{t("admin.userManagement.allRoles")}</span>
                  <span className="xs:hidden">All</span>
                </TabsTrigger>
                <TabsTrigger 
                  value="student" 
                  className="flex items-center justify-center gap-1 xs:gap-2 text-[10px] xs:text-xs sm:text-sm"
                  onClick={() => setSelectedRole("student")}
                >
                  <GraduationCap className="h-3 w-3 xs:h-3.5 xs:w-3.5 sm:h-4 sm:w-4" />
                  <span className="hidden xs:inline">{t("admin.userManagement.students")}</span>
                  <span className="xs:hidden">Students</span>
                </TabsTrigger>
                <TabsTrigger 
                  value="teacher" 
                  className="flex items-center justify-center gap-1 xs:gap-2 text-[10px] xs:text-xs sm:text-sm"
                  onClick={() => setSelectedRole("teacher")}
                >
                  <BookOpen className="h-3 w-3 xs:h-3.5 xs:w-3.5 sm:h-4 sm:w-4" />
                  <span className="hidden xs:inline">{t("admin.userManagement.teachers")}</span>
                  <span className="xs:hidden">Teachers</span>
                </TabsTrigger>
                <TabsTrigger 
                  value="admin" 
                  className="flex items-center justify-center gap-1 xs:gap-2 text-[10px] xs:text-xs sm:text-sm"
                  onClick={() => setSelectedRole("admin")}
                >
                  <ShieldCheck className="h-3 w-3 xs:h-3.5 xs:w-3.5 sm:h-4 sm:w-4" />
                  <span className="hidden xs:inline">{t("admin.userManagement.administrators")}</span>
                  <span className="xs:hidden">Admins</span>
                </TabsTrigger>
              </TabsList>

              <div className="space-y-6 sm:space-y-8 max-w-full overflow-x-hidden">
                <TabsContent value="all" className="mt-0">
                  {/* Students Section */}
                  <div>
                    <h3 className="text-sm xs:text-base sm:text-lg font-semibold mb-2 xs:mb-3 sm:mb-4 flex items-center gap-1 xs:gap-2 bg-primary/5 p-1.5 xs:p-2 rounded-md border-l-4 border-primary">
                      <GraduationCap className="h-3 w-3 xs:h-4 xs:w-4 sm:h-5 sm:w-5 text-primary" />
                      {t("admin.userManagement.students")} ({students.length})
                    </h3>
                    {renderUserTable(students)}
                  </div>

                  {/* Teachers Section */}
                  <div className="mt-6 sm:mt-8">
                    <h3 className="text-sm xs:text-base sm:text-lg font-semibold mb-2 xs:mb-3 sm:mb-4 flex items-center gap-1 xs:gap-2 bg-primary/5 p-1.5 xs:p-2 rounded-md border-l-4 border-primary">
                      <BookOpen className="h-3 w-3 xs:h-4 xs:w-4 sm:h-5 sm:w-5 text-primary" />
                      {t("admin.userManagement.teachers")} ({teachers.length})
                    </h3>
                    {renderUserTable(teachers)}
                  </div>

                  {/* Administrators Section */}
                  <div className="mt-6 sm:mt-8">
                    <h3 className="text-sm xs:text-base sm:text-lg font-semibold mb-2 xs:mb-3 sm:mb-4 flex items-center gap-1 xs:gap-2 bg-primary/5 p-1.5 xs:p-2 rounded-md border-l-4 border-primary">
                      <ShieldCheck className="h-3 w-3 xs:h-4 xs:w-4 sm:h-5 sm:w-5 text-primary" />
                      {t("admin.userManagement.administrators")} ({admins.length})
                    </h3>
                    {renderUserTable(admins)}
                  </div>
                </TabsContent>

                <TabsContent value="student" className="mt-0">
                  <div>
                    <h3 className="text-sm xs:text-base sm:text-lg font-semibold mb-2 xs:mb-3 sm:mb-4 flex items-center gap-1 xs:gap-2 bg-primary/5 p-1.5 xs:p-2 rounded-md border-l-4 border-primary">
                      <GraduationCap className="h-3 w-3 xs:h-4 xs:w-4 sm:h-5 sm:w-5 text-primary" />
                      {t("admin.userManagement.students")} ({students.length})
                    </h3>
                    {renderUserTable(students, true)}
                  </div>
                </TabsContent>

                <TabsContent value="teacher" className="mt-0">
                  <div>
                    <h3 className="text-sm xs:text-base sm:text-lg font-semibold mb-2 xs:mb-3 sm:mb-4 flex items-center gap-1 xs:gap-2 bg-primary/5 p-1.5 xs:p-2 rounded-md border-l-4 border-primary">
                      <BookOpen className="h-3 w-3 xs:h-4 xs:w-4 sm:h-5 sm:w-5 text-primary" />
                      {t("admin.userManagement.teachers")} ({teachers.length})
                    </h3>
                    {renderUserTable(teachers, true)}
                  </div>
                </TabsContent>

                <TabsContent value="admin" className="mt-0">
                  <div>
                    <h3 className="text-sm xs:text-base sm:text-lg font-semibold mb-2 xs:mb-3 sm:mb-4 flex items-center gap-1 xs:gap-2 bg-primary/5 p-1.5 xs:p-2 rounded-md border-l-4 border-primary">
                      <ShieldCheck className="h-3 w-3 xs:h-4 xs:w-4 sm:h-5 sm:w-5 text-primary" />
                      {t("admin.userManagement.administrators")} ({admins.length})
                    </h3>
                    {renderUserTable(admins, true)}
                  </div>
                </TabsContent>
              </div>
            </Tabs>
          )}
        </CardContent>
      </Card>

      {/* Add User Dialog */}
      {showAddUser && (
        <UserForm
          onClose={() => setShowAddUser(false)}
          onSuccess={() => {
            setShowAddUser(false);
            fetchUsers();
          }}
        />
      )}

      {/* User Details Dialog */}
      {selectedUser && (
        <UserDetails
          user={selectedUser}
          open={!!selectedUser}
          onClose={() => setSelectedUser(null)}
          onUpdate={fetchUsers}
        />
      )}

      {/* Delete User Dialog */}
      <SchoolAdminDeleteUserDialog
        user={userToDelete}
        open={!!userToDelete}
        onOpenChange={() => setUserToDelete(null)}
        onUserDeleted={fetchUsers}
      />

      {/* Maintenance Mode Dialog */}
      <UserMaintenanceDialog
        user={userToMaintenance}
        open={!!userToMaintenance}
        onOpenChange={() => setUserToMaintenance(null)}
        onUserUpdated={fetchUsers}
      />

      {/* Block User Dialog */}
      <UserBlockDialog
        user={userToBlock}
        open={!!userToBlock}
        onOpenChange={() => setUserToBlock(null)}
        onUserUpdated={fetchUsers}
      />
    </div>
  );
}
