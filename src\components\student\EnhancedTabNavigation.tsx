import React from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Ta<PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { useTranslation } from "react-i18next";

// Import custom icons
import {
  QrCodeIcon,
  ClockIcon,
  FileTextIcon,
  UserIcon,
  BellIcon,
} from "./icons/TabIcons";

interface EnhancedTabNavigationProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
  unreadCount: number;
  requiresProfileSetup: boolean;
}

export default function EnhancedTabNavigation({
  activeTab,
  setActiveTab,
  unreadCount,
  requiresProfileSetup,
}: EnhancedTabNavigationProps) {
  const { t } = useTranslation();

  // Define tab items with their properties
  const tabItems = [
    {
      id: "scan",
      label: t("attendance.scan"),
      icon: <QrCodeIcon />,
      disabled: requiresProfileSetup,
      color: "hsl(var(--primary))",
    },
    {
      id: "history",
      label: t("attendance.history"),
      icon: <ClockIcon />,
      disabled: requiresProfileSetup,
      color: "hsl(var(--primary))",
    },
    {
      id: "excuses",
      label: t("attendance.excuses"),
      icon: <FileTextIcon />,
      disabled: requiresProfileSetup,
      color: "hsl(var(--primary))",
    },
    {
      id: "profile",
      label: t("common.profile"),
      icon: <UserIcon />,
      disabled: false,
      color: "hsl(var(--primary))",
    },
    {
      id: "notifications",
      label: t("notifications.alerts"),
      icon: <BellIcon />,
      disabled: requiresProfileSetup,
      color: "hsl(var(--primary))",
      badge:
        unreadCount > 0 && !localStorage.getItem("notificationsViewed")
          ? unreadCount > 9
            ? "9+"
            : unreadCount.toString()
          : null,
    },
  ];

  return (
    <TabsList className="w-full max-w-md mx-auto mb-6 grid grid-cols-5 p-1 h-auto bg-muted/80 backdrop-blur-sm">
      {tabItems.map((tab) => (
        <TabsTrigger
          key={tab.id}
          value={tab.id}
          disabled={tab.disabled}
          onClick={() => setActiveTab(tab.id)}
          className={`
            relative flex flex-col items-center gap-1 py-3 px-1
            transition-all duration-300 overflow-hidden
            ${activeTab === tab.id ? "text-white" : "text-muted-foreground"}
          `}
        >
          {/* Background pill for active tab */}
          {activeTab === tab.id && (
            <motion.div
              layoutId="activeTabBackground"
              className="absolute inset-0 bg-primary rounded-md"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ type: "spring", stiffness: 500, damping: 30 }}
            />
          )}

          {/* Icon with animation */}
          <motion.div
            className="relative z-10"
            initial={{ scale: 0.8 }}
            animate={{
              scale: activeTab === tab.id ? 1 : 0.8,
              y: activeTab === tab.id ? -2 : 0,
            }}
            transition={{ type: "spring", stiffness: 500, damping: 30 }}
          >
            {React.cloneElement(tab.icon as React.ReactElement, {
              size: 18,
              className:
                activeTab === tab.id ? "text-white" : "text-muted-foreground",
            })}

            {/* Badge for notifications */}
            <AnimatePresence>
              {tab.badge && (
                <motion.div
                  initial={{ scale: 0.5, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  exit={{ scale: 0.5, opacity: 0 }}
                  className="absolute -top-2 -right-2 bg-secondary text-[10px] text-white w-4 h-4 rounded-full flex items-center justify-center font-medium"
                >
                  {tab.badge}
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>

          {/* Label with animation */}
          <motion.span
            className={`text-xs sm:text-sm relative z-10 ${
              activeTab === tab.id ? "text-white" : ""
            }`}
            initial={{ opacity: 0.7 }}
            animate={{
              opacity: activeTab === tab.id ? 1 : 0.7,
              y: activeTab === tab.id ? 0 : 2,
            }}
            transition={{ type: "spring", stiffness: 500, damping: 30 }}
          >
            {tab.label}
          </motion.span>
        </TabsTrigger>
      ))}
    </TabsList>
  );
}
