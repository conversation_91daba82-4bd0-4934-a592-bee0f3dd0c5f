# SMS Sending Edge Function

This Supabase Edge Function handles sending SMS messages via Twilio's API. It's designed to be a secure way to send SMS messages from the browser without exposing <PERSON><PERSON><PERSON> credentials to the client.

## Deployment

To deploy this function to your Supabase project:

1. Install the Supabase CLI if you haven't already:
   ```bash
   npm install -g supabase
   ```

2. Login to your Supabase account:
   ```bash
   supabase login
   ```

3. Link your project:
   ```bash
   supabase link --project-ref your-project-ref
   ```

4. Deploy the function:
   ```bash
   supabase functions deploy send-sms
   ```

## Configuration

The function requires the following environment variables:

- `SUPABASE_URL`: Your Supabase project URL (set automatically)
- `SUPABASE_SERVICE_ROLE_KEY`: Your Supabase service role key (set automatically)

## CORS Configuration

By default, the function allows requests from any origin. If you want to restrict this to specific domains, modify the CORS headers in the function code.

## Usage

Call the function from your client code:

```typescript
const { data, error } = await supabase.functions.invoke('send-sms', {
  body: { 
    to: '+**********', 
    message: 'Your message here' 
  }
});

if (error) {
  console.error('Error sending SMS:', error);
} else {
  console.log('SMS sent successfully:', data);
}
```

## Security

This function uses the Supabase service role key to access the database, which has full access to your database. The function is secure as long as your Supabase project is secure.

The function retrieves Twilio credentials from the `system_settings` table, which should be protected by Row Level Security policies that only allow admin access.
