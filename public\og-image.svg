<svg width="1200" height="630" viewBox="0 0 1200 630" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="1200" height="630" fill="#08194A"/>
  <rect x="50" y="50" width="1100" height="530" rx="20" fill="#0a1f56" stroke="#EE0D09" stroke-width="2"/>

  <!-- Main Title -->
  <text x="600" y="280" font-family="Arial, sans-serif" font-size="48" font-weight="bold" fill="#EE0D09" text-anchor="middle">
    Attendance Tracking System
  </text>
  
  <!-- Subtitle -->
  <text x="600" y="340" font-family="Arial, sans-serif" font-size="24" fill="#ffffff" text-anchor="middle">
    Secure and efficient attendance tracking for educational institutions
  </text>
  
  <!-- Features -->
  <text x="600" y="420" font-family="Arial, sans-serif" font-size="18" fill="#cccccc" text-anchor="middle">
    ✓ Real-time Tracking  ✓ Parent Notifications  ✓ Multi-language Support
  </text>
  
  <!-- Bottom text -->
  <text x="600" y="520" font-family="Arial, sans-serif" font-size="16" fill="#888888" text-anchor="middle">
    Built with React, TypeScript, and Supabase
  </text>
</svg>
