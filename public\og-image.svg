<svg width="1200" height="630" viewBox="0 0 1200 630" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="1200" height="630" fill="#08194A"/>
  <rect x="50" y="50" width="1100" height="530" rx="20" fill="#0a1f56" stroke="#EE0D09" stroke-width="2"/>

  <!-- Logo (scaled down version) -->
  <g transform="translate(100, 150)">
    <!-- Background circle -->
    <circle cx="80" cy="80" r="70" fill="#EE0D09" stroke="#ffffff" stroke-width="4"/>

    <!-- Clipboard -->
    <g transform="translate(40, 30)">
      <rect x="10" y="20" width="60" height="90" rx="6" fill="#ffffff" stroke="#08194A" stroke-width="3"/>
      <rect x="25" y="15" width="30" height="15" rx="3" fill="#08194A"/>

      <!-- Checkmarks -->
      <g stroke="#EE0D09" stroke-width="4" fill="none" stroke-linecap="round" stroke-linejoin="round">
        <path d="M18 45l6 6 12-12"/>
        <path d="M18 60l6 6 12-12"/>
        <path d="M18 75l6 6 12-12"/>
        <path d="M18 90l6 6 12-12"/>
      </g>

      <!-- Lines -->
      <g fill="#08194A">
        <rect x="40" y="44" width="20" height="1.5" rx="0.75"/>
        <rect x="40" y="59" width="25" height="1.5" rx="0.75"/>
        <rect x="40" y="74" width="15" height="1.5" rx="0.75"/>
        <rect x="40" y="89" width="22" height="1.5" rx="0.75"/>
      </g>
    </g>
  </g>

  <!-- Main Title -->
  <text x="600" y="280" font-family="Arial, sans-serif" font-size="48" font-weight="bold" fill="#EE0D09" text-anchor="middle">
    Attendance Tracking System
  </text>
  
  <!-- Subtitle -->
  <text x="600" y="340" font-family="Arial, sans-serif" font-size="24" fill="#ffffff" text-anchor="middle">
    Secure and efficient attendance tracking for educational institutions
  </text>
  
  <!-- Features -->
  <text x="600" y="420" font-family="Arial, sans-serif" font-size="18" fill="#cccccc" text-anchor="middle">
    ✓ Real-time Tracking  ✓ Parent Notifications  ✓ Multi-language Support
  </text>
  
  <!-- Bottom text -->
  <text x="600" y="520" font-family="Arial, sans-serif" font-size="16" fill="#888888" text-anchor="middle">
    Built with React, TypeScript, and Supabase
  </text>
</svg>
