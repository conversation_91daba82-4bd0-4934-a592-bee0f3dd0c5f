import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON>Title } from "@/components/ui/dialog";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { User } from "@/lib/types";
import { UserCircle2, Mail, BookOpen, Building2, MapPin, Key, Calendar, Pencil } from "lucide-react";
import UserEditForm from "./UserEditForm";
import { useAuth } from "@/context/AuthContext";

interface UserDetailsProps {
  user: User;
  open: boolean;
  onClose: () => void;
  onUpdate: () => void;
}

export default function UserDetails({ user, open, onClose, onUpdate }: UserDetailsProps) {
  const [showEditForm, setShowEditForm] = useState(false);
  const { session } = useAuth();
  const isAdmin = session?.user?.user_metadata?.role === "admin";

  const renderStudentDetails = () => (
    <>
      <div className="flex items-center gap-2 text-muted-foreground">
        <BookOpen className="h-4 w-4" />
        <span>Course: {user.course || "Not set"}</span>
      </div>
      <div className="flex items-center gap-2 text-muted-foreground">
        <Building2 className="h-4 w-4" />
        <span>Block: {user.blockName || "Not set"}</span>
      </div>
      <div className="flex items-center gap-2 text-muted-foreground">
        <MapPin className="h-4 w-4" />
        <span>Room: {user.roomNumber || "Not set"}</span>
      </div>
      <div className="flex items-center gap-2 text-muted-foreground">
        <Key className="h-4 w-4" />
        <span>Student ID: {user.studentId || "Not set"}</span>
      </div>
    </>
  );

  const renderTeacherDetails = () => (
    <>
      <div className="flex items-center gap-2 text-muted-foreground">
        <Building2 className="h-4 w-4" />
        <span>Department: {user.department || "Not set"}</span>
      </div>
      <div className="flex items-center gap-2 text-muted-foreground">
        <BookOpen className="h-4 w-4" />
        <span>Subject: {user.subject || "Not set"}</span>
      </div>
      <div className="flex items-center gap-2 text-muted-foreground">
        <Key className="h-4 w-4" />
        <span>Teacher ID: {user.teacherId || "Not set"}</span>
      </div>
      <div className="flex items-center gap-2 text-muted-foreground">
        <MapPin className="h-4 w-4" />
        <span>Position: {user.position || "Not set"}</span>
      </div>
    </>
  );

  const handleEdit = () => {
    setShowEditForm(true);
  };

  const handleEditClose = () => {
    setShowEditForm(false);
  };

  const handleUpdate = () => {
    onUpdate();
    handleEditClose();
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onClose}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex justify-between items-center">
              <span>User Details</span>
              {isAdmin && (
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-2"
                  onClick={handleEdit}
                >
                  <Pencil className="h-4 w-4" />
                  Edit
                </Button>
              )}
            </DialogTitle>
          </DialogHeader>
          
          <div className="flex flex-col items-center gap-4 mb-6">
            <Avatar className="w-24 h-24">
              <AvatarImage src={user.photoUrl} alt={user.name} />
              <AvatarFallback>{user.name.charAt(0).toUpperCase()}</AvatarFallback>
            </Avatar>
            <div className="text-center">
              <h2 className="text-xl font-semibold">{user.name}</h2>
              <p className="text-muted-foreground capitalize">{user.role}</p>
            </div>
          </div>

          <div className="grid gap-6">
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-4">
                  <div className="flex items-center gap-2 text-muted-foreground">
                    <UserCircle2 className="h-4 w-4" />
                    <span>Name: {user.name}</span>
                  </div>
                  <div className="flex items-center gap-2 text-muted-foreground">
                    <Mail className="h-4 w-4" />
                    <span>Email: {user.email}</span>
                  </div>
                  <div className="flex items-center gap-2 text-muted-foreground">
                    <Calendar className="h-4 w-4" />
                    <span>Joined: {new Date(user.created_at || "").toLocaleDateString()}</span>
                  </div>
                  {user.role === "student" && renderStudentDetails()}
                  {user.role === "teacher" && renderTeacherDetails()}
                </div>
              </CardContent>
            </Card>
          </div>
        </DialogContent>
      </Dialog>

      {showEditForm && (
        <UserEditForm
          user={user}
          open={showEditForm}
          onClose={handleEditClose}
          onUpdate={handleUpdate}
        />
      )}
    </>
  );
} 