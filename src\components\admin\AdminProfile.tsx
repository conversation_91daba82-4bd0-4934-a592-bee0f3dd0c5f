import { useState, useRef, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle, Camera, Pencil, Globe } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/context/AuthContext";
import { supabase } from "@/lib/supabase";
import { Admin } from "@/lib/types";
import { useTranslation } from "react-i18next";
import LanguageToggle from "@/components/shared/LanguageToggle";

// Define the form schema with Zod
const profileSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters." }),
  position: z.string().min(2, { message: "Position is required." }),
  school: z.string().min(2, { message: "School name is required." }),
  adminId: z.string().min(2, { message: "Admin ID is required." }),
});

type ProfileFormValues = z.infer<typeof profileSchema>;

export default function AdminProfile({
  isSetupMode = false,
}: {
  isSetupMode?: boolean;
}) {
  const [isEditing, setIsEditing] = useState(isSetupMode);
  const [submitAttempted, setSubmitAttempted] = useState(false);
  const [uploadingPhoto, setUploadingPhoto] = useState(false);
  const [photoPreview, setPhotoPreview] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  const { profile, updateProfile } = useAuth();
  const navigate = useNavigate();
  const { t } = useTranslation();

  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      name: "",
      position: "",
      school: "",
      adminId: "",
    },
  });

  // Update form with existing profile data when available
  useEffect(() => {
    if (profile && profile.role === "admin") {
      const adminProfile = profile as Admin;

      // Update form values
      form.reset({
        name: adminProfile.name || "",
        position: adminProfile.position || "",
        school: adminProfile.school || "",
        adminId: adminProfile.adminId || "",
      });

      // Set photo preview if exists
      if (adminProfile.photoUrl) {
        setPhotoPreview(adminProfile.photoUrl);
      }
    }
  }, [profile, form]);

  const handlePhotoClick = () => {
    fileInputRef.current?.click();
  };

  const handlePhotoChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.translateError(
        t,
        "profile.fileTooLarge",
        "profile.selectSmallerImage"
      );
      return;
    }

    // Check file type
    if (!file.type.startsWith("image/")) {
      toast.translateError(
        t,
        "profile.invalidFileType",
        "profile.selectImageFile"
      );
      return;
    }

    try {
      setUploadingPhoto(true);

      // Create a preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setPhotoPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);

      // Upload to Supabase Storage
      const fileExt = file.name.split(".").pop();
      const fileName = `admin-${profile?.id}-${Date.now()}.${fileExt}`;
      const filePath = `profile-photos/${fileName}`;

      const { error: uploadError } = await supabase.storage
        .from("images")
        .upload(filePath, file);

      if (uploadError) throw uploadError;

      // Get public URL
      const { data: publicUrlData } = supabase.storage
        .from("images")
        .getPublicUrl(filePath);

      // Update profile with new photo URL
      await updateProfile({
        photoUrl: publicUrlData.publicUrl,
      });

      toast.translateSuccess(
        t,
        "profile.photoUpdated",
        "profile.photoUpdatedDesc"
      );
    } catch (error) {
      console.error("Error uploading photo:", error);
      toast.translateError(
        t,
        "profile.uploadFailed",
        "profile.failedToUploadPhoto"
      );
    } finally {
      setUploadingPhoto(false);
    }
  };

  const onSubmit = async (data: ProfileFormValues) => {
    setSubmitAttempted(true);

    try {
      console.log("Updating admin profile with data:", data);

      // First update the main profile
      await updateProfile({
        name: data.name,
        adminId: data.adminId,
        position: data.position,
        school: data.school,
      });

      // Check if we need to manually insert into admin_profiles table
      // This is a fallback in case the trigger doesn't work
      const { data: adminProfileData, error: adminProfileError } =
        await supabase
          .from("admin_profiles")
          .select("*")
          .eq("id", profile?.id)
          .maybeSingle();

      if (adminProfileError && adminProfileError.code !== "PGRST116") {
        console.error("Error checking admin profile:", adminProfileError);
      }

      // If admin profile doesn't exist, create it
      if (!adminProfileData) {
        const { error: insertError } = await supabase
          .from("admin_profiles")
          .insert({
            id: profile?.id,
            user_id: profile?.id,
            admin_id: data.adminId,
            position: data.position,
            school: data.school,
          });

        if (insertError) {
          console.error("Error creating admin profile:", insertError);
        }
      }

      if (!isSetupMode) {
        setIsEditing(false);
      }

      // If in setup mode, show success toast and redirect to dashboard
      if (isSetupMode) {
        toast.translateSuccess(
          t,
          "profile.setupComplete",
          "profile.setupCompleteDesc"
        );
        setTimeout(() => navigate("/admin"), 500);
      }
    } catch (error) {
      console.error("Error updating profile:", error);
      toast.translateError(t, "common.error", "profile.failedToUpdateProfile");
    } finally {
      setSubmitAttempted(false);
    }
  };

  const adminProfile = profile as Admin;

  // Check if required fields are missing for setup mode
  const isMissingRequiredFields =
    isSetupMode &&
    (!adminProfile.position || !adminProfile.school || !adminProfile.adminId);

  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardHeader>
        <CardTitle>
          {isSetupMode
            ? t("admin.profile.completeYourProfile")
            : t("admin.profile.adminProfile")}
        </CardTitle>
        <CardDescription>
          {isEditing
            ? t("admin.profile.updateProfileInfo")
            : isSetupMode
            ? t("admin.profile.fillRequiredFields")
            : t("admin.profile.viewAndManageProfile")}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {isMissingRequiredFields && (
          <Alert className="mb-4 bg-yellow-50 border-yellow-200">
            <AlertCircle className="h-4 w-4 text-amber-800" />
            <AlertDescription className="text-amber-800">
              {t("admin.profile.fillRequiredFields")}
            </AlertDescription>
          </Alert>
        )}

        {isEditing ? (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="flex flex-col items-center mb-6">
                <div
                  className="relative cursor-pointer group"
                  onClick={handlePhotoClick}
                >
                  <Avatar className="w-24 h-24 border-2 border-primary/20">
                    <AvatarImage src={photoPreview || adminProfile?.photoUrl} />
                    <AvatarFallback className="text-lg">
                      {adminProfile?.name?.charAt(0).toUpperCase() || "A"}
                    </AvatarFallback>
                  </Avatar>
                  <div className="absolute inset-0 flex items-center justify-center bg-black/30 rounded-full opacity-0 group-hover:opacity-100 transition-opacity">
                    <Camera className="h-6 w-6 text-white" />
                  </div>
                  <input
                    type="file"
                    ref={fileInputRef}
                    className="hidden"
                    accept="image/*"
                    onChange={handlePhotoChange}
                    disabled={uploadingPhoto}
                  />
                </div>
                <p className="text-xs text-muted-foreground mt-2">
                  Click to {adminProfile?.photoUrl ? "change" : "upload"} photo
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("common.name")}</FormLabel>
                      <FormControl>
                        <Input
                          placeholder={t("admin.profile.enterFullName")}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="adminId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("admin.profile.adminId")}</FormLabel>
                      <FormControl>
                        <Input
                          placeholder={t("admin.profile.enterAdminId")}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="position"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("admin.profile.position")}</FormLabel>
                      <FormControl>
                        <Input
                          placeholder={t("admin.profile.enterPosition")}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="school"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("admin.profile.schoolName")}</FormLabel>
                      <FormControl>
                        <Input
                          placeholder={t("admin.profile.enterSchoolName")}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="flex justify-end gap-2">
                {!isSetupMode && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsEditing(false)}
                  >
                    {t("common.cancel")}
                  </Button>
                )}
                <Button
                  type="submit"
                  disabled={submitAttempted || uploadingPhoto}
                >
                  {isSetupMode
                    ? t("profile.completeSetup")
                    : t("common.saveChanges")}
                </Button>
              </div>
            </form>
          </Form>
        ) : (
          <div className="space-y-6">
            <div className="flex items-start gap-6">
              <Avatar className="w-24 h-24 border-2 border-primary/20">
                <AvatarImage src={adminProfile?.photoUrl} />
                <AvatarFallback className="text-lg">
                  {adminProfile?.name?.charAt(0).toUpperCase() || "A"}
                </AvatarFallback>
              </Avatar>

              <div className="space-y-4 flex-1">
                <div className="space-y-1">
                  <p className="text-2xl font-semibold">{adminProfile?.name}</p>
                  <p className="text-muted-foreground">
                    {t("admin.profile.adminId")}: {adminProfile?.adminId}
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="text-muted-foreground">
                      {t("admin.profile.position")}
                    </Label>
                    <p>{adminProfile?.position || "-"}</p>
                  </div>
                  <div>
                    <Label className="text-muted-foreground">
                      {t("common.email")}
                    </Label>
                    <p>{adminProfile?.email}</p>
                  </div>
                  <div>
                    <Label className="text-muted-foreground">
                      {t("common.school")}
                    </Label>
                    <p>{adminProfile?.school || "-"}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Language Settings Section */}
            <div className="mt-6 border-t pt-4">
              <div className="flex items-center gap-2 mb-3">
                <Globe size={18} className="text-primary" />
                <h3 className="text-lg font-medium">
                  {t("settings.languageSettings")}
                </h3>
              </div>
              <LanguageToggle variant="default" showLabel={true} />
            </div>
          </div>
        )}
      </CardContent>
      {!isEditing && !isSetupMode && (
        <CardFooter className="flex justify-end">
          <Button
            variant="outline"
            className="flex items-center gap-2"
            onClick={() => setIsEditing(true)}
          >
            <Pencil className="h-4 w-4" />
            {t("common.edit")} {t("common.profile")}
          </Button>
        </CardFooter>
      )}
    </Card>
  );
}
