interface Coordinates {
  latitude: number;
  longitude: number;
}

export interface LocationVerificationResult {
  isWithinRadius: boolean;
  message: string;
  alertType: 'warning' | 'error' | 'info';
  alertTitle: string;
}

/**
 * Calculates the distance between two points using the Haversine formula
 */
export function calculateDistance(point1: Coordinates, point2: Coordinates): number {
  const R = 6371000; // Earth's radius in meters
  const lat1 = point1.latitude * Math.PI / 180;
  const lat2 = point2.latitude * Math.PI / 180;
  const deltaLat = (point2.latitude - point1.latitude) * Math.PI / 180;
  const deltaLon = (point2.longitude - point1.longitude) * Math.PI / 180;

  const a = Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2) +
    Math.cos(lat1) * Math.cos(lat2) *
    Math.sin(deltaLon / 2) * Math.sin(deltaLon / 2);

  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c; // Distance in meters
}

/**
 * Verifies if a student's location is within acceptable range of the room
 */
export function verifyLocationForAttendance(
  studentLocation: Coordinates,
  roomLocation: Coordinates,
  allowedRadius: number
): LocationVerificationResult {
  const distance = calculateDistance(studentLocation, roomLocation);
  
  // Convert to kilometers for VPN check
  const distanceInKm = distance / 1000;

  // VPN check - if distance is more than 200km
  if (distanceInKm > 200) {
    return {
      isWithinRadius: false,
      message: "It seems you might be using a VPN which is affecting your location. Please disable any VPN services and try again to ensure accurate attendance tracking.",
      alertType: 'error',
      alertTitle: 'VPN Detected'
    };
  }

  // Not in school check - if distance is more than 500m
  if (distance > 500) {
    return {
      isWithinRadius: false,
      message: "You appear to be too far from the school premises. Attendance can only be marked when you're physically present at school.",
      alertType: 'error',
      alertTitle: 'Not at School'
    };
  }

  // Not in room but in school check - if distance is more than allowed radius but less than 200m
  if (distance > allowedRadius && distance <= 200) {
    const extraDistance = Math.round(distance - allowedRadius);
    return {
      isWithinRadius: false,
      message: `You're about ${extraDistance} meters away from your assigned room. Please make sure you're in the correct room before marking attendance.`,
      alertType: 'warning',
      alertTitle: 'Wrong Room Location'
    };
  }

  // Within allowed radius
  if (distance <= allowedRadius) {
    return {
      isWithinRadius: true,
      message: "You're in the correct location. Attendance can be marked.",
      alertType: 'info',
      alertTitle: 'Location Verified'
    };
  }

  // Between 200m and 500m
  return {
    isWithinRadius: false,
    message: "You're not in the correct location. Please make sure you're in your assigned room to mark attendance.",
    alertType: 'error',
    alertTitle: 'Incorrect Location'
  };
} 