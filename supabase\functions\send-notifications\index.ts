// Follow this setup guide to integrate the Deno runtime into your application:
// https://deno.land/manual/examples/deploy_node_server

import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { corsHeaders } from '../_shared/cors.ts';

// Send<PERSON><PERSON> and <PERSON><PERSON><PERSON> would be imported here in a real implementation
// For this example, we'll simulate the API calls

interface EmailRequest {
  to: string;
  subject: string;
  message: string;
  apiKey: string;
  fromEmail: string;
}

interface SMSRequest {
  to: string;
  message: string;
  accountSid: string;
  authToken: string;
  phoneNumber: string;
}

async function sendEmail(req: EmailRequest): Promise<{ success: boolean; message: string }> {
  try {
    // In a real implementation, you would use SendGrid SDK or API here
    console.log(`Sending email to ${req.to} from ${req.fromEmail} with subject: ${req.subject}`);
    
    // Make a fetch request to SendGrid API
    const response = await fetch('https://api.sendgrid.com/v3/mail/send', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${req.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        personalizations: [{ to: [{ email: req.to }] }],
        from: { email: req.fromEmail },
        subject: req.subject,
        content: [
          {
            type: 'text/plain',
            value: req.message,
          },
          {
            type: 'text/html',
            value: req.message.replace(/\n/g, '<br>'),
          },
        ],
      }),
    });

    if (response.ok) {
      return { success: true, message: 'Email sent successfully' };
    } else {
      const errorData = await response.json();
      console.error('SendGrid API error:', errorData);
      return { success: false, message: `SendGrid API error: ${JSON.stringify(errorData)}` };
    }
  } catch (error) {
    console.error('Error sending email:', error);
    return { success: false, message: `Error sending email: ${error.message}` };
  }
}

async function sendSMS(req: SMSRequest): Promise<{ success: boolean; message: string; sid?: string }> {
  try {
    // In a real implementation, you would use Twilio SDK or API here
    console.log(`Sending SMS to ${req.to} from ${req.phoneNumber}`);
    
    // Make a fetch request to Twilio API
    const twilioApiUrl = `https://api.twilio.com/2010-04-01/Accounts/${req.accountSid}/Messages.json`;
    const auth = btoa(`${req.accountSid}:${req.authToken}`);
    
    const formData = new URLSearchParams();
    formData.append('To', req.to);
    formData.append('From', req.phoneNumber);
    formData.append('Body', req.message);
    
    const response = await fetch(twilioApiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Basic ${auth}`,
      },
      body: formData,
    });
    
    const data = await response.json();
    
    if (response.ok) {
      return { success: true, message: 'SMS sent successfully', sid: data.sid };
    } else {
      console.error('Twilio API error:', data);
      return { success: false, message: `Twilio API error: ${JSON.stringify(data)}` };
    }
  } catch (error) {
    console.error('Error sending SMS:', error);
    return { success: false, message: `Error sending SMS: ${error.message}` };
  }
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }
  
  try {
    const { type, ...data } = await req.json();
    
    // Create a Supabase client with the Auth context of the logged in user
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      { global: { headers: { Authorization: req.headers.get('Authorization')! } } }
    );
    
    // Get the user from the request
    const {
      data: { user },
    } = await supabaseClient.auth.getUser();
    
    // Only allow authenticated users
    if (!user) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }
    
    let result;
    
    if (type === 'email') {
      result = await sendEmail(data as EmailRequest);
    } else if (type === 'sms') {
      result = await sendSMS(data as SMSRequest);
    } else {
      return new Response(
        JSON.stringify({ error: 'Invalid notification type' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }
    
    return new Response(
      JSON.stringify(result),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
});
