import { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { useToast } from "@/hooks/use-toast";
import { Camera, Save, School as SchoolIcon, Globe } from "lucide-react";
import { Teacher, School } from "@/lib/types";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { useAuth } from "@/context/AuthContext";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/lib/supabase";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { fetchSchools } from "@/lib/api/schools";
import { useTranslation } from "react-i18next";
import LanguageToggle from "@/components/shared/LanguageToggle";

const profileSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters." }),
  teacherId: z.string().min(2, { message: "Teacher ID is required." }),
  department: z.string().min(1, { message: "Department is required." }),
  position: z.string().min(1, { message: "Position is required." }),
  subject: z.string().min(1, { message: "Subject is required." }),
  school: z.string().min(1, { message: "School selection is required." }),
});

type ProfileFormValues = z.infer<typeof profileSchema>;

export default function TeacherProfile({
  isSetupMode = false,
}: {
  isSetupMode?: boolean;
}) {
  const [isEditing, setIsEditing] = useState(isSetupMode);
  const [submitAttempted, setSubmitAttempted] = useState(false);
  const [uploadingPhoto, setUploadingPhoto] = useState(false);
  const [photoPreview, setPhotoPreview] = useState<string | null>(null);
  const [schools, setSchools] = useState<School[]>([]);
  const [loadingSchools, setLoadingSchools] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  const { profile, updateProfile } = useAuth();
  const navigate = useNavigate();
  const { t } = useTranslation();

  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      name: "",
      teacherId: "",
      department: "",
      position: "",
      subject: "",
      school: "",
    },
  });

  useEffect(() => {
    // Always set to editing mode if in setup mode
    if (isSetupMode) {
      setIsEditing(true);
    }
  }, [isSetupMode]);

  // Fetch schools from admin profiles
  useEffect(() => {
    const getSchools = async () => {
      setLoadingSchools(true);
      try {
        const schoolsList = await fetchSchools();
        setSchools(schoolsList);

        if (schoolsList.length === 0) {
          console.log("No schools found in the database");
          toast({
            title: "No schools found",
            description:
              "No schools have been added by administrators yet. Please contact your administrator to add your school.",
            variant: "destructive",
          });
        } else {
          console.log("Found schools:", schoolsList);
        }
      } catch (error) {
        console.error("Error fetching schools:", error);
        toast({
          title: "Error",
          description: "Failed to fetch schools",
          variant: "destructive",
        });
      } finally {
        setLoadingSchools(false);
      }
    };

    getSchools();
  }, []);

  useEffect(() => {
    if (profile && profile.role === "teacher") {
      const teacherProfile = profile as Teacher;

      // Update form values
      form.reset({
        name: teacherProfile.name || "",
        teacherId: teacherProfile.teacherId || "",
        department: teacherProfile.department || "",
        position: teacherProfile.position || "",
        subject: teacherProfile.subject || "",
        school: teacherProfile.school || "",
      });

      // Set photo preview if exists
      if (teacherProfile.photoUrl) {
        setPhotoPreview(teacherProfile.photoUrl);
      }
    }
  }, [profile, form]);

  const handlePhotoClick = () => {
    fileInputRef.current?.click();
  };

  const handlePhotoChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file || !profile) return;

    try {
      setUploadingPhoto(true);

      // Create a temporary preview
      const objectUrl = URL.createObjectURL(file);
      setPhotoPreview(objectUrl);

      // Upload to Supabase Storage
      const fileExt = file.name.split(".").pop();
      const fileName = `${profile.id}-${Math.random()
        .toString(36)
        .substring(2)}.${fileExt}`;
      const filePath = `teachers/${fileName}`;

      // Upload the file to the images bucket
      const { error: uploadError, data: uploadData } = await supabase.storage
        .from("images")
        .upload(filePath, file);

      if (uploadError) {
        throw uploadError;
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from("images")
        .getPublicUrl(filePath);

      // Save URL to profile
      await updateProfile({
        photoUrl: urlData.publicUrl,
      });

      toast({
        title: "Photo uploaded",
        description: "Your profile photo has been updated.",
      });
    } catch (error: any) {
      console.error("Photo upload error:", error);
      toast({
        title: "Upload failed",
        description:
          error.message || "Failed to upload photo. Please try again.",
        variant: "destructive",
      });

      // Reset preview on error if no previous photo
      if (!profile?.photoUrl) {
        setPhotoPreview(null);
      }
    } finally {
      setUploadingPhoto(false);
    }
  };

  const onSubmit = async (data: ProfileFormValues) => {
    if (!profile) return;

    setSubmitAttempted(true);

    try {
      console.log("Updating teacher profile with data:", data);

      // Get selected school name for display
      const selectedSchool = schools.find((s) => s.id === data.school);

      await updateProfile({
        name: data.name,
        teacherId: data.teacherId,
        department: data.department,
        position: data.position,
        subject: data.subject,
        school_id: data.school,
        school: data.school, // For backward compatibility
        schoolName: selectedSchool?.name || "",
      });

      if (!isSetupMode) {
        setIsEditing(false);
      }

      // If in setup mode, show success toast and redirect to dashboard
      if (isSetupMode) {
        toast({
          title: "Profile setup complete",
          description:
            "Your profile has been successfully set up. You can now access all features.",
        });
        setTimeout(() => navigate("/teacher"), 500);
      }
    } catch (error) {
      console.error("Error updating profile:", error);
      toast({
        title: "Error",
        description: "Failed to update profile. Please try again.",
        variant: "destructive",
      });
    } finally {
      setSubmitAttempted(false);
    }
  };

  if (!profile || profile.role !== "teacher") {
    return (
      <Card className="w-full max-w-3xl mx-auto">
        <CardContent className="p-6">
          <div className="flex justify-center items-center h-40">
            <p>Loading profile...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const teacherProfile = profile as Teacher;

  // Check if required fields are missing for setup mode
  const isMissingRequiredFields =
    isSetupMode &&
    (!teacherProfile.department ||
      !teacherProfile.position ||
      !teacherProfile.subject ||
      !teacherProfile.school);

  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardHeader>
        <CardTitle>
          {isSetupMode
            ? t("teacher.profile.completeYourProfile")
            : t("teacher.profile.teacherProfile")}
        </CardTitle>
        <CardDescription>
          {isEditing
            ? t("teacher.profile.updateProfileInfo")
            : isSetupMode
            ? t("teacher.profile.fillRequiredFields")
            : t("teacher.profile.viewAndManageProfile")}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {isMissingRequiredFields && (
          <Alert className="mb-4 bg-yellow-50 border-yellow-200">
            <AlertCircle className="h-4 w-4 text-amber-800" />
            <AlertDescription className="text-amber-800">
              {t("teacher.profile.fillRequiredFields")}
            </AlertDescription>
          </Alert>
        )}

        {/* Hidden file input */}
        <input
          type="file"
          ref={fileInputRef}
          onChange={handlePhotoChange}
          className="hidden"
          accept="image/*"
        />

        {isEditing ? (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <div className="flex flex-col sm:flex-row items-center gap-4 mb-6">
                <Avatar
                  className="w-24 h-24 border-4 border-primary/20 cursor-pointer"
                  onClick={handlePhotoClick}
                >
                  {photoPreview || teacherProfile.photoUrl ? (
                    <AvatarImage
                      src={photoPreview || teacherProfile.photoUrl}
                      alt={teacherProfile.name}
                    />
                  ) : (
                    <AvatarFallback className="bg-secondary/30 flex items-center justify-center">
                      <Camera size={24} className="text-gray-400" />
                    </AvatarFallback>
                  )}
                </Avatar>
                <div>
                  <Button
                    type="button"
                    variant="outline"
                    className="flex items-center gap-2"
                    onClick={handlePhotoClick}
                    disabled={uploadingPhoto}
                  >
                    <Camera size={16} />{" "}
                    {uploadingPhoto ? "Uploading..." : "Change Photo"}
                  </Button>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Full Name</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="teacherId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Teacher ID</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="department"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Department</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="position"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Position</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="subject"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Subject</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="school"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <SchoolIcon className="h-4 w-4" /> School
                      </FormLabel>
                      <Select
                        disabled={
                          !isEditing || loadingSchools || schools.length === 0
                        }
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue
                              placeholder={
                                loadingSchools
                                  ? "Loading schools..."
                                  : schools.length === 0
                                  ? "No schools available"
                                  : "Select your school"
                              }
                            />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {schools.map((school) => (
                            <SelectItem key={school.id} value={school.id}>
                              {school.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Select the school you are teaching at.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="pt-4 flex justify-end gap-2">
                {!isSetupMode && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsEditing(false)}
                  >
                    Cancel
                  </Button>
                )}
                <Button
                  type="submit"
                  className="gap-2"
                  disabled={submitAttempted}
                >
                  <Save size={16} />
                  {submitAttempted
                    ? t("common.saving")
                    : isSetupMode
                    ? t("profile.completeSetup")
                    : t("common.saveChanges")}
                </Button>
              </div>
            </form>
          </Form>
        ) : (
          <>
            <div className="flex flex-col sm:flex-row items-center sm:items-start gap-6">
              <Avatar
                className="w-24 h-24 border-4 border-primary/20 cursor-pointer"
                onClick={handlePhotoClick}
              >
                {teacherProfile.photoUrl ? (
                  <AvatarImage
                    src={teacherProfile.photoUrl}
                    alt={teacherProfile.name}
                  />
                ) : (
                  <AvatarFallback className="bg-secondary/30 flex items-center justify-center">
                    <Camera size={24} className="text-gray-400" />
                  </AvatarFallback>
                )}
              </Avatar>

              <div className="space-y-4 flex-1">
                <div className="space-y-1">
                  <p className="text-2xl font-semibold">
                    {teacherProfile.name}
                  </p>
                  <p className="text-muted-foreground">
                    {t("teacher.profile.teacherId")}: {teacherProfile.teacherId}
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="text-muted-foreground">
                      {t("teacher.profile.department")}
                    </Label>
                    <p>{teacherProfile.department || "-"}</p>
                  </div>
                  <div>
                    <Label className="text-muted-foreground">
                      {t("common.email")}
                    </Label>
                    <p>{teacherProfile.email}</p>
                  </div>
                  <div>
                    <Label className="text-muted-foreground">
                      {t("teacher.profile.position")}
                    </Label>
                    <p>{teacherProfile.position || "-"}</p>
                  </div>
                  <div>
                    <Label className="text-muted-foreground">
                      {t("teacher.profile.subject")}
                    </Label>
                    <p>{teacherProfile.subject || "-"}</p>
                  </div>
                  <div>
                    <Label className="text-muted-foreground">
                      {t("common.school")}
                    </Label>
                    <p>{teacherProfile.schoolName || "-"}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Language Settings Section */}
            <div className="mt-6 border-t pt-4">
              <div className="flex items-center gap-2 mb-3">
                <Globe size={18} className="text-primary" />
                <h3 className="text-lg font-medium">
                  {t("settings.languageSettings")}
                </h3>
              </div>
              <LanguageToggle variant="default" showLabel={true} />
            </div>

            <div className="border-t mt-6 pt-4 flex justify-end">
              <Button onClick={() => setIsEditing(true)}>
                {t("common.edit")}
              </Button>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}
