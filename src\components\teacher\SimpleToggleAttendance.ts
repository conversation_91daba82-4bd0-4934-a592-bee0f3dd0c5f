import { SupabaseClient } from "@supabase/supabase-client";
import { Student } from "@/types/student";
import { Profile } from "@/types/profile";
import { toast } from "@/components/ui/use-toast";
import { toast as sonnerToast } from "sonner";

// Simple function to toggle attendance status without dealing with rooms
export const toggleAttendanceStatus = async (
  student: Student,
  currentStatus: "present" | "absent" | "late" | "excused",
  supabase: SupabaseClient,
  profile: Profile | null,
  updateLocalState: (
    studentId: string,
    newStatus: "present" | "absent" | "late" | "excused"
  ) => void
) => {
  try {
    const now = new Date();

    // Determine the new status based on current status
    // If current status is present, change to absent
    // If current status is anything else (absent, late, excused), change to present
    const newStatus = currentStatus === "present" ? "absent" : "present";

    // Update local state immediately for better UX
    updateLocalState(student.id, newStatus);

    // Show success message immediately
    toast({
      title: "Status Updated",
      description: `${student.name} has been marked ${newStatus}`,
    });

    // Show additional notification
    sonnerToast.success(`${student.name} marked ${newStatus}`, {
      description: "Attendance record updated successfully",
    });

    // Create a notification for the student
    try {
      const teacherName = profile?.name || "Teacher";

      // Get room information if available
      let roomName = "Unknown";
      let roomId = null;

      if (student.room_id) {
        roomId = student.room_id;
        const { data: roomData } = await supabase
          .from("rooms")
          .select("name")
          .eq("id", roomId)
          .single();

        if (roomData) {
          roomName = roomData.name;
        }
      }

      await supabase.from("notifications").insert({
        student_id: student.id,
        teacher_id: profile?.id,
        title:
          newStatus === "present" ? "✅ Marked Present" : "❌ Marked Absent",
        message:
          newStatus === "present"
            ? `You were marked present in Room ${roomName} by ${teacherName}`
            : `You were marked absent in Room ${roomName} by ${teacherName}. If this is incorrect, please contact your teacher.`,
        type: newStatus === "present" ? "attendance" : "absence",
        read: false,
        timestamp: now.toISOString(),
        metadata: JSON.stringify({
          status: newStatus,
          previous_status: currentStatus,
          updated_by: "teacher",
          room_id: roomId,
          room_name: roomName,
        }),
      });

      console.log("Notification created for student:", student.id);
    } catch (notificationError) {
      console.error("Error creating notification:", notificationError);
      // Continue execution even if notification creation fails
    }
  } catch (error) {
    console.error("Error updating attendance:", error);
    toast({
      title: "Error",
      description: "Failed to update attendance status. Please try again.",
      variant: "destructive",
    });
  }
};
