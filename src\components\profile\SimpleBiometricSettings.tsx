import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Shield, 
  Plus, 
  Trash2, 
  CheckCircle, 
  XCircle,
  Fingerprint,
  AlertTriangle
} from "lucide-react";
import { useAuth } from "@/context/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { isWebAuthnAvailable, isPasskeyAvailable } from "@/lib/webauthn";
import { supabase } from "@/integrations/supabase/client";
import SimpleBiometricAuth from "@/components/auth/SimpleBiometricAuth";

export default function SimpleBiometricSettings() {
  const [hasRegistration, setHasRegistration] = useState(false);
  const [showRegistration, setShowRegistration] = useState(false);
  const [loading, setLoading] = useState(true);
  const [isSupported, setIsSupported] = useState(false);
  
  const { user, profile } = useAuth();
  const { toast } = useToast();

  // Load biometric data on mount
  useEffect(() => {
    const loadBiometricData = async () => {
      if (!user) return;

      try {
        setLoading(true);
        
        // Check if WebAuthn is supported
        const supported = isWebAuthnAvailable();
        setIsSupported(supported);
        
        if (supported) {
          // Check if user has registered biometrics
          const hasPasskey = await isPasskeyAvailable(user.id);
          setHasRegistration(hasPasskey);
        }
      } catch (error) {
        console.error('Error loading biometric data:', error);
        toast({
          title: "Error",
          description: "Failed to load biometric settings",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    loadBiometricData();
  }, [user, toast]);

  // Handle registration success
  const handleRegistrationSuccess = () => {
    setShowRegistration(false);
    setHasRegistration(true);
    
    toast({
      title: "Success",
      description: "Biometric authentication registered successfully!",
    });
  };

  // Handle registration error
  const handleRegistrationError = (error: string) => {
    toast({
      title: "Registration Failed",
      description: error,
      variant: "destructive",
    });
  };

  // Remove biometric registration
  const handleRemove = async () => {
    if (!user) return;

    try {
      // Delete from biometric_credentials table
      const { error } = await supabase
        .from('biometric_credentials')
        .delete()
        .eq('user_id', user.id);

      if (error) throw error;

      setHasRegistration(false);
      toast({
        title: "Success",
        description: "Biometric authentication removed successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to remove biometric registration",
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-8">
          <div className="text-center">
            <Shield className="w-8 h-8 mx-auto text-gray-400 mb-2" />
            <p className="text-gray-600">Loading biometric settings...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!isSupported) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            Biometric Authentication
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <AlertTriangle className="w-12 h-12 mx-auto text-orange-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Biometric Authentication Not Available
            </h3>
            <p className="text-gray-600 mb-4">
              Your device or browser doesn't support biometric authentication, or you need to use HTTPS.
            </p>
            <div className="bg-blue-50 p-4 rounded-lg text-left">
              <p className="text-sm font-medium text-blue-800 mb-2">Requirements:</p>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Modern browser (Chrome, Firefox, Safari, Edge)</li>
                <li>• HTTPS connection (or localhost for development)</li>
                <li>• Device with fingerprint sensor or face recognition</li>
                <li>• WebAuthn support enabled in browser</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            Biometric Authentication
          </CardTitle>
          <p className="text-sm text-gray-600">
            Set up biometric authentication for quick and secure access to your account.
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Current Status */}
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex items-center gap-3">
              <div className={`p-2 rounded-lg ${
                hasRegistration ? 'bg-green-100' : 'bg-gray-100'
              }`}>
                <Fingerprint className={`w-5 h-5 ${
                  hasRegistration ? 'text-green-600' : 'text-gray-600'
                }`} />
              </div>
              <div>
                <p className="font-medium">Biometric Authentication</p>
                <p className="text-sm text-gray-600">
                  {hasRegistration 
                    ? 'Use fingerprint or face recognition to authenticate'
                    : 'Not registered - click to set up biometric authentication'
                  }
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {hasRegistration ? (
                <>
                  <Badge variant="outline" className="text-green-600 border-green-200">
                    <CheckCircle className="w-3 h-3 mr-1" />
                    Active
                  </Badge>
                  <Button
                    onClick={handleRemove}
                    variant="outline"
                    size="sm"
                    className="text-red-600 border-red-200 hover:bg-red-50"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </>
              ) : (
                <Button
                  onClick={() => setShowRegistration(true)}
                  variant="outline"
                  size="sm"
                >
                  <Plus className="w-4 h-4 mr-1" />
                  Register
                </Button>
              )}
            </div>
          </div>

          {/* Benefits */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="flex items-start gap-3">
              <Shield className="w-5 h-5 text-blue-600 mt-0.5" />
              <div className="text-sm">
                <p className="font-medium text-blue-800 mb-1">Benefits of Biometric Authentication</p>
                <ul className="text-blue-700 space-y-1">
                  <li>• Quick attendance marking with just a touch</li>
                  <li>• Enhanced security with hardware-backed authentication</li>
                  <li>• No need to remember PINs or passwords</li>
                  <li>• Works with fingerprint sensors and face recognition</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Security Notice */}
          <div className="bg-green-50 p-4 rounded-lg">
            <div className="flex items-start gap-3">
              <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
              <div className="text-sm">
                <p className="font-medium text-green-800 mb-1">Security & Privacy</p>
                <ul className="text-green-700 space-y-1">
                  <li>• Your biometric data never leaves your device</li>
                  <li>• Only encrypted templates are stored securely</li>
                  <li>• You can remove biometric access anytime</li>
                  <li>• PIN backup is always available</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Biometric Registration Modal */}
      {showRegistration && user && profile && (
        <Card>
          <CardHeader>
            <CardTitle>Register Biometric Authentication</CardTitle>
            <p className="text-sm text-gray-600">
              Follow the prompts to set up fingerprint or face recognition.
            </p>
          </CardHeader>
          <CardContent>
            <SimpleBiometricAuth
              userId={user.id}
              username={profile.name || profile.email}
              mode="register"
              onSuccess={handleRegistrationSuccess}
              onError={handleRegistrationError}
              className="border-0 shadow-none"
            />
            <div className="mt-4">
              <Button
                onClick={() => setShowRegistration(false)}
                variant="outline"
                className="w-full"
              >
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
