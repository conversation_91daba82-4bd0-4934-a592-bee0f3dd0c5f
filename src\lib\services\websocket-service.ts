import { supabase } from '@/integrations/supabase/client';
import { RealtimeChannel } from '@supabase/supabase-js';

export interface AttendanceUpdate {
  type: 'attendance_recorded' | 'qr_generated' | 'qr_expired';
  data: {
    student_id?: string;
    room_id: string;
    block_id?: string;
    school_id: string;
    timestamp: string;
    student_name?: string;
    room_name?: string;
    verification_method?: string;
  };
}

export interface QRUpdate {
  type: 'qr_generated' | 'qr_expired' | 'qr_refreshed';
  data: {
    room_id: string;
    block_id: string;
    school_id: string;
    session_id: string;
    expires_at: string;
    room_name?: string;
  };
}

class WebSocketService {
  private channels: Map<string, RealtimeChannel> = new Map();
  private attendanceCallbacks: Map<string, (update: AttendanceUpdate) => void> = new Map();
  private qrCallbacks: Map<string, (update: QRUpdate) => void> = new Map();

  /**
   * Subscribe to real-time attendance updates for a school
   */
  subscribeToAttendance(
    schoolId: string,
    callback: (update: AttendanceUpdate) => void,
    roomId?: string
  ): () => void {
    const channelName = roomId 
      ? `attendance:${schoolId}:${roomId}` 
      : `attendance:${schoolId}`;
    
    // Store callback
    this.attendanceCallbacks.set(channelName, callback);

    // Create or get existing channel
    let channel = this.channels.get(channelName);
    if (!channel) {
      channel = supabase.channel(channelName);
      this.channels.set(channelName, channel);

      // Subscribe to attendance_records table changes
      channel
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'attendance_records',
            filter: roomId ? `room_id=eq.${roomId}` : `school_id=eq.${schoolId}`,
          },
          (payload) => {
            this.handleAttendanceInsert(payload, channelName);
          }
        )
        .subscribe();
    }

    // Return unsubscribe function
    return () => {
      this.attendanceCallbacks.delete(channelName);
      if (this.attendanceCallbacks.size === 0) {
        channel?.unsubscribe();
        this.channels.delete(channelName);
      }
    };
  }

  /**
   * Subscribe to real-time QR code updates for a school
   */
  subscribeToQRUpdates(
    schoolId: string,
    callback: (update: QRUpdate) => void,
    roomId?: string
  ): () => void {
    const channelName = roomId 
      ? `qr:${schoolId}:${roomId}` 
      : `qr:${schoolId}`;
    
    // Store callback
    this.qrCallbacks.set(channelName, callback);

    // Create or get existing channel
    let channel = this.channels.get(channelName);
    if (!channel) {
      channel = supabase.channel(channelName);
      this.channels.set(channelName, channel);

      // Subscribe to rooms table changes (QR code updates)
      channel
        .on(
          'postgres_changes',
          {
            event: 'UPDATE',
            schema: 'public',
            table: 'rooms',
            filter: roomId ? `id=eq.${roomId}` : `school_id=eq.${schoolId}`,
          },
          (payload) => {
            this.handleQRUpdate(payload, channelName);
          }
        )
        .subscribe();

      // Subscribe to qr_sessions table changes
      channel
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'qr_sessions',
            filter: roomId ? `room_id=eq.${roomId}` : `school_id=eq.${schoolId}`,
          },
          (payload) => {
            this.handleQRSessionUpdate(payload, channelName);
          }
        )
        .subscribe();
    }

    // Return unsubscribe function
    return () => {
      this.qrCallbacks.delete(channelName);
      if (this.qrCallbacks.size === 0) {
        channel?.unsubscribe();
        this.channels.delete(channelName);
      }
    };
  }

  /**
   * Broadcast attendance update to all subscribers
   */
  async broadcastAttendanceUpdate(update: AttendanceUpdate): Promise<void> {
    const channelName = `attendance:${update.data.school_id}`;
    const channel = this.channels.get(channelName);
    
    if (channel) {
      await channel.send({
        type: 'broadcast',
        event: 'attendance_update',
        payload: update,
      });
    }
  }

  /**
   * Broadcast QR update to all subscribers
   */
  async broadcastQRUpdate(update: QRUpdate): Promise<void> {
    const channelName = `qr:${update.data.school_id}`;
    const channel = this.channels.get(channelName);
    
    if (channel) {
      await channel.send({
        type: 'broadcast',
        event: 'qr_update',
        payload: update,
      });
    }
  }

  /**
   * Handle attendance record insertion
   */
  private async handleAttendanceInsert(payload: any, channelName: string): Promise<void> {
    const callback = this.attendanceCallbacks.get(channelName);
    if (!callback) return;

    try {
      // Fetch additional data for the attendance record
      const { data: attendanceData, error } = await supabase
        .from('attendance_records')
        .select(`
          *,
          profiles!student_id (
            first_name,
            last_name
          ),
          rooms (
            name
          )
        `)
        .eq('id', payload.new.id)
        .single();

      if (error || !attendanceData) {
        console.error('Error fetching attendance data:', error);
        return;
      }

      const update: AttendanceUpdate = {
        type: 'attendance_recorded',
        data: {
          student_id: attendanceData.student_id,
          room_id: attendanceData.room_id,
          block_id: attendanceData.block_id,
          school_id: attendanceData.school_id,
          timestamp: attendanceData.timestamp,
          student_name: `${attendanceData.profiles?.first_name} ${attendanceData.profiles?.last_name}`,
          room_name: attendanceData.rooms?.name,
          verification_method: attendanceData.verification_method,
        },
      };

      callback(update);
    } catch (error) {
      console.error('Error handling attendance insert:', error);
    }
  }

  /**
   * Handle QR code update in rooms table
   */
  private async handleQRUpdate(payload: any, channelName: string): Promise<void> {
    const callback = this.qrCallbacks.get(channelName);
    if (!callback) return;

    try {
      // Check if QR code was updated
      if (payload.new.current_qr_code !== payload.old.current_qr_code) {
        // Fetch room data
        const { data: roomData, error } = await supabase
          .from('rooms')
          .select(`
            *,
            blocks (
              name
            )
          `)
          .eq('id', payload.new.id)
          .single();

        if (error || !roomData) {
          console.error('Error fetching room data:', error);
          return;
        }

        // Parse QR data to get session ID
        let sessionId = '';
        try {
          const qrData = JSON.parse(roomData.current_qr_code || '{}');
          sessionId = qrData.session_id || '';
        } catch (e) {
          console.warn('Could not parse QR data');
        }

        const update: QRUpdate = {
          type: payload.new.current_qr_code ? 'qr_generated' : 'qr_expired',
          data: {
            room_id: roomData.id,
            block_id: roomData.block_id,
            school_id: roomData.school_id,
            session_id: sessionId,
            expires_at: roomData.qr_expiry || '',
            room_name: roomData.name,
          },
        };

        callback(update);
      }
    } catch (error) {
      console.error('Error handling QR update:', error);
    }
  }

  /**
   * Handle QR session updates
   */
  private handleQRSessionUpdate(payload: any, channelName: string): void {
    const callback = this.qrCallbacks.get(channelName);
    if (!callback) return;

    let updateType: QRUpdate['type'] = 'qr_generated';
    
    if (payload.eventType === 'INSERT') {
      updateType = 'qr_generated';
    } else if (payload.eventType === 'UPDATE') {
      updateType = payload.new.is_active ? 'qr_refreshed' : 'qr_expired';
    } else if (payload.eventType === 'DELETE') {
      updateType = 'qr_expired';
    }

    const data = payload.new || payload.old;
    const update: QRUpdate = {
      type: updateType,
      data: {
        room_id: data.room_id,
        block_id: data.block_id,
        school_id: data.school_id,
        session_id: data.session_id,
        expires_at: data.expires_at,
      },
    };

    callback(update);
  }

  /**
   * Subscribe to presence updates (who's online)
   */
  subscribeToPresence(
    roomId: string,
    callback: (presence: any) => void
  ): () => void {
    const channelName = `presence:${roomId}`;
    
    let channel = this.channels.get(channelName);
    if (!channel) {
      channel = supabase.channel(channelName);
      this.channels.set(channelName, channel);

      channel
        .on('presence', { event: 'sync' }, () => {
          const state = channel!.presenceState();
          callback(state);
        })
        .on('presence', { event: 'join' }, ({ key, newPresences }) => {
          callback({ type: 'join', key, newPresences });
        })
        .on('presence', { event: 'leave' }, ({ key, leftPresences }) => {
          callback({ type: 'leave', key, leftPresences });
        })
        .subscribe();
    }

    return () => {
      channel?.unsubscribe();
      this.channels.delete(channelName);
    };
  }

  /**
   * Track user presence in a room
   */
  async trackPresence(roomId: string, userInfo: any): Promise<void> {
    const channelName = `presence:${roomId}`;
    const channel = this.channels.get(channelName);
    
    if (channel) {
      await channel.track(userInfo);
    }
  }

  /**
   * Stop tracking user presence
   */
  async untrackPresence(roomId: string): Promise<void> {
    const channelName = `presence:${roomId}`;
    const channel = this.channels.get(channelName);
    
    if (channel) {
      await channel.untrack();
    }
  }

  /**
   * Cleanup all subscriptions
   */
  cleanup(): void {
    this.channels.forEach((channel) => {
      channel.unsubscribe();
    });
    this.channels.clear();
    this.attendanceCallbacks.clear();
    this.qrCallbacks.clear();
  }
}

// Export singleton instance
export const websocketService = new WebSocketService();

// Cleanup on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    websocketService.cleanup();
  });
}
