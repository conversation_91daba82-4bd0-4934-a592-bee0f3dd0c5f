import { useState, useEffect, useRef } from "react";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import QRCode from "react-qr-code";
import { supabase } from "@/integrations/supabase/client";
import { Clock, Users, Wifi, WifiOff, Shield, RefreshCw } from "lucide-react";

interface SimpleTabletDisplayProps {
  roomId: string;
  schoolId: string;
}

interface RoomInfo {
  id: string;
  name: string;
  building?: string;
  floor?: number;
  capacity: number;
  block_name?: string;
}

export default function SimpleTabletDisplay({
  roomId,
  schoolId,
}: SimpleTabletDisplayProps) {
  // QR Code state
  const [qrData, setQrData] = useState<string>("");
  const [qrExpiry, setQrExpiry] = useState<Date | null>(null);
  const [timeLeft, setTimeLeft] = useState<number>(0);
  const [isActive, setIsActive] = useState(false);

  // Room state
  const [roomInfo, setRoomInfo] = useState<RoomInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  // Auto-refresh timer
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const pollRef = useRef<NodeJS.Timeout | null>(null);

  // Keep tablet active and prevent sleep
  useEffect(() => {
    // Mark this as a tablet session
    sessionStorage.setItem("isTabletSession", "true");

    // Prevent page from going to sleep
    const keepAlive = () => {
      // Request wake lock if available
      if ("wakeLock" in navigator) {
        navigator.wakeLock.request("screen").catch(() => {
          // Wake lock not supported or denied
        });
      }
    };

    keepAlive();

    // Keep alive every 30 seconds
    const keepAliveInterval = setInterval(keepAlive, 30000);

    return () => {
      clearInterval(keepAliveInterval);
    };
  }, []);

  // Fetch room information
  useEffect(() => {
    const fetchRoomInfo = async () => {
      try {
        setIsLoading(true);
        const { data: room, error } = await supabase
          .from("rooms")
          .select(
            `
            id,
            name,
            building,
            floor,
            capacity,
            current_qr_code,
            qr_expiry,
            blocks (
              name
            )
          `
          )
          .eq("id", roomId)
          .single();

        if (error) throw error;

        setRoomInfo({
          id: room.id,
          name: room.name,
          building: room.building,
          floor: room.floor,
          capacity: room.capacity,
          block_name: room.blocks?.name,
        });

        // Load existing QR code if available
        if (room.current_qr_code && room.qr_expiry) {
          const expiry = new Date(room.qr_expiry);
          if (expiry.getTime() > Date.now()) {
            setQrData(room.current_qr_code);
            setQrExpiry(expiry);
            setIsActive(true);
            setTimeLeft(Math.floor((expiry.getTime() - Date.now()) / 1000));
          }
        }

        setLastUpdate(new Date());
      } catch (error) {
        console.error("Error fetching room info:", error);
      } finally {
        setIsLoading(false);
      }
    };

    if (roomId) {
      fetchRoomInfo();
    }
  }, [roomId]);

  // Polling for QR code updates
  useEffect(() => {
    if (!roomId) return;

    const pollForUpdates = async () => {
      try {
        const { data: room, error } = await supabase
          .from("rooms")
          .select("current_qr_code, qr_expiry")
          .eq("id", roomId)
          .single();

        if (error) throw error;

        if (room.current_qr_code && room.qr_expiry) {
          const expiry = new Date(room.qr_expiry);
          if (expiry.getTime() > Date.now()) {
            // Only update if QR code changed
            if (room.current_qr_code !== qrData) {
              setQrData(room.current_qr_code);
              setQrExpiry(expiry);
              setIsActive(true);
              setTimeLeft(Math.floor((expiry.getTime() - Date.now()) / 1000));
              setLastUpdate(new Date());
            }
          } else {
            // QR code expired
            setIsActive(false);
            setQrData("");
            setQrExpiry(null);
          }
        } else {
          // No QR code available
          setIsActive(false);
          setQrData("");
          setQrExpiry(null);
        }
      } catch (error) {
        console.error("Error polling for updates:", error);
      }
    };

    // Poll every 10 seconds
    pollRef.current = setInterval(pollForUpdates, 10000);

    return () => {
      if (pollRef.current) {
        clearInterval(pollRef.current);
      }
    };
  }, [roomId, qrData]);

  // Timer for countdown
  useEffect(() => {
    if (qrExpiry && isActive) {
      timerRef.current = setInterval(() => {
        const remaining = Math.max(
          0,
          Math.floor((qrExpiry.getTime() - Date.now()) / 1000)
        );
        setTimeLeft(remaining);

        if (remaining <= 0) {
          setIsActive(false);
          setQrData("");
          setQrExpiry(null);
        }
      }, 1000);

      return () => {
        if (timerRef.current) {
          clearInterval(timerRef.current);
        }
      };
    }
  }, [qrExpiry, isActive]);

  // Format time remaining
  const formatTimeLeft = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <Card className="w-96">
          <CardContent className="flex items-center justify-center p-8">
            <RefreshCw className="w-8 h-8 animate-spin text-blue-600" />
            <span className="ml-3 text-lg">Loading room information...</span>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle className="text-2xl font-bold text-gray-800">
                  {roomInfo?.name || "Loading..."}
                </CardTitle>
                <p className="text-gray-600">
                  {roomInfo?.block_name && `Block ${roomInfo.block_name}`}
                  {roomInfo?.building && ` • ${roomInfo.building}`}
                  {roomInfo?.floor && ` • Floor ${roomInfo.floor}`}
                </p>
              </div>
              <div className="text-right">
                <div className="flex items-center gap-2 text-sm">
                  <Wifi className="w-4 h-4 text-green-600" />
                  <span className="text-green-600">Connected</span>
                  <span className="text-gray-500">
                    Last update: {lastUpdate.toLocaleTimeString()}
                  </span>
                </div>
                <div className="flex items-center gap-2 mt-1">
                  <Users className="w-4 h-4 text-blue-600" />
                  <span className="text-sm text-gray-600">
                    Capacity: {roomInfo?.capacity || 0}
                  </span>
                </div>
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* QR Code Display */}
        <Card className="text-center">
          <CardHeader>
            <CardTitle className="flex items-center justify-center gap-2">
              <Shield className="w-6 h-6 text-blue-600" />
              Attendance QR Code
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {isActive && qrData ? (
              <>
                {/* QR Code */}
                <div className="flex justify-center">
                  <div className="bg-white p-8 rounded-2xl shadow-lg border-4 border-blue-200">
                    <QRCode
                      value={qrData}
                      size={300}
                      style={{
                        height: "auto",
                        maxWidth: "100%",
                        width: "100%",
                      }}
                    />
                  </div>
                </div>

                {/* Timer */}
                <div className="space-y-2">
                  <div className="flex items-center justify-center gap-2">
                    <Clock className="w-5 h-5 text-blue-600" />
                    <span className="text-lg font-medium">Time Remaining:</span>
                  </div>
                  <div
                    className={`text-4xl font-bold ${
                      timeLeft < 60
                        ? "text-red-600 animate-pulse"
                        : "text-blue-600"
                    }`}
                  >
                    {formatTimeLeft(timeLeft)}
                  </div>
                  <Badge variant={timeLeft < 60 ? "destructive" : "default"}>
                    {timeLeft < 60 ? "Expiring Soon" : "Active"}
                  </Badge>
                </div>

                {/* Instructions */}
                <div className="bg-blue-50 p-4 rounded-lg">
                  <p className="text-blue-800 font-medium">
                    📱 Scan this QR code with your phone to mark attendance
                  </p>
                  <p className="text-blue-600 text-sm mt-1">
                    Make sure you're in your assigned room before scanning
                  </p>
                </div>
              </>
            ) : (
              <div className="py-12">
                <div className="text-gray-400 mb-4">
                  <Shield className="w-16 h-16 mx-auto" />
                </div>
                <h3 className="text-xl font-medium text-gray-600 mb-2">
                  No Active QR Code
                </h3>
                <p className="text-gray-500">
                  Waiting for attendance session to begin...
                </p>
                <p className="text-gray-400 text-sm mt-2">
                  Checking for updates every 10 seconds
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
