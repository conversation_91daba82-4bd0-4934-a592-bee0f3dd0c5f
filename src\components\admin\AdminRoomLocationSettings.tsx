import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";
import { Loader2, MapPin, AlertCircle } from "lucide-react";
import { supabase } from "@/lib/supabase";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { createRoomLocationsTable } from "@/lib/migrations.new";
import { useTranslation } from "react-i18next";

interface RoomLocationSettingsProps {
  roomId: string;
}

export function AdminRoomLocationSettings({
  roomId,
}: RoomLocationSettingsProps) {
  const [loading, setLoading] = useState(false);
  const [updating, setUpdating] = useState(false);
  const [locationPermission, setLocationPermission] =
    useState<PermissionState | null>(null);
  const [location, setLocation] = useState<{
    latitude: number;
    longitude: number;
    radius_meters: number;
  } | null>(null);
  const { toast } = useToast();
  const { t } = useTranslation();

  useEffect(() => {
    checkLocationPermission();
    ensureTableExists();
    fetchRoomLocation();
  }, [roomId]);

  const checkLocationPermission = async () => {
    if (!navigator.permissions) {
      console.log("Permissions API not supported");
      return;
    }

    try {
      const permission = await navigator.permissions.query({
        name: "geolocation" as PermissionName,
      });
      setLocationPermission(permission.state);

      permission.onchange = () => {
        setLocationPermission(permission.state);
      };
    } catch (error) {
      console.error("Error checking location permission:", error);
    }
  };

  const getCurrentLocation = (): Promise<GeolocationPosition> => {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error("Geolocation is not supported by this browser."));
        return;
      }

      navigator.geolocation.getCurrentPosition(resolve, reject, {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 0,
      });
    });
  };

  const ensureTableExists = async (): Promise<boolean> => {
    try {
      // Check if the table exists by trying to select from it
      const { error } = await supabase
        .from("room_locations")
        .select("*")
        .limit(1);

      if (error) {
        if (error.code === "PGRST204") {
          // Table doesn't exist, create it
          console.log("Creating room_locations table...");
          await createRoomLocationsTable();
          return true;
        } else {
          throw error;
        }
      }

      return true;
    } catch (error) {
      console.error("Error getting teacher profile:", error);
      return false;
    }
  };

  const fetchRoomLocation = async () => {
    if (!roomId) return;

    setLoading(true);
    try {
      const { data, error } = await supabase
        .from("room_locations")
        .select("*")
        .eq("room_id", roomId)
        .single();

      if (error) {
        if (error.code === "PGRST116") {
          // No location set yet, this is fine
          setLocation(null);
        } else if (error.code === "PGRST204") {
          // Table doesn't exist yet
          console.warn("Room locations table does not exist");
          setLocation(null);
        } else {
          throw error;
        }
      } else if (data) {
        setLocation({
          latitude: Number(data.latitude),
          longitude: Number(data.longitude),
          radius_meters: data.radius_meters,
        });
      }
    } catch (error) {
      console.error("Error fetching room location:", error);
      toast({
        title: "Error",
        description: "Failed to fetch room location. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSetCurrentLocation = async () => {
    try {
      setUpdating(true);
      console.log("Getting current location...");
      const position = await getCurrentLocation();
      console.log("Current position:", position.coords);

      // Check if table exists first
      console.log("Verifying table and permissions...");
      const tableExists = await ensureTableExists();
      if (!tableExists) {
        throw new Error("Room locations table is not properly configured");
      }
      console.log("Table exists and admin has access");

      const locationData = {
        room_id: roomId,
        latitude: position.coords.latitude,
        longitude: position.coords.longitude,
        radius_meters: location?.radius_meters || 50,
      };

      console.log("Saving location data:", locationData);

      // Upsert the location data
      const { data: savedLocation, error } = await supabase
        .from("room_locations")
        .upsert(locationData, {
          onConflict: "room_id",
        })
        .select()
        .single();

      if (error) {
        console.error("Error saving location:", error);
        throw new Error("Failed to save location. Please try again.");
      }

      if (!savedLocation) {
        console.error("Location not found after saving");
        throw new Error("Location was not saved correctly. Please try again.");
      }

      console.log("Location saved successfully:", savedLocation);

      setLocation({
        latitude: position.coords.latitude,
        longitude: position.coords.longitude,
        radius_meters: location?.radius_meters || 50,
      });

      toast({
        title: "Location Updated",
        description: "Room location has been set successfully.",
      });

      // Update permission state after successful location access
      checkLocationPermission();
    } catch (error) {
      console.error("Error setting location:", error);
      let errorMessage = "Failed to set room location";

      if (error instanceof GeolocationPositionError) {
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage =
              "Please allow location access in your browser settings to set room location";
            setLocationPermission("denied");
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage =
              "Location information is unavailable. Please try again";
            break;
          case error.TIMEOUT:
            errorMessage = "Location request timed out. Please try again";
            break;
        }
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setUpdating(false);
    }
  };

  const handleRadiusChange = async (newRadius: number) => {
    if (!location) return;

    try {
      setUpdating(true);

      const { error } = await supabase
        .from("room_locations")
        .update({ radius_meters: newRadius })
        .eq("room_id", roomId);

      if (error) throw error;

      setLocation({
        ...location,
        radius_meters: newRadius,
      });

      toast({
        title: "Radius Updated",
        description: "Attendance radius has been updated successfully",
      });
    } catch (error) {
      console.error("Error updating radius:", error);
      toast({
        title: "Error",
        description: "Failed to update attendance radius",
        variant: "destructive",
      });
    } finally {
      setUpdating(false);
    }
  };

  const renderLocationPermissionMessage = () => {
    if (locationPermission === "denied") {
      return (
        <Alert variant="destructive" className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {t("admin.settings.roomLocationPermissionDenied")}
          </AlertDescription>
        </Alert>
      );
    }
    return null;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("admin.settings.roomLocationSettings")}</CardTitle>
        <CardDescription>
          {t("admin.settings.roomLocationDescription")}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {renderLocationPermissionMessage()}

        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <Button
              onClick={handleSetCurrentLocation}
              disabled={updating || locationPermission === "denied"}
              className="flex items-center gap-2"
            >
              {updating ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <MapPin className="h-4 w-4" />
              )}
              {location
                ? t("admin.settings.updateCurrentLocation")
                : t("admin.settings.setCurrentLocation")}
            </Button>
          </div>

          {location && (
            <>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>{t("admin.settings.latitude")}</Label>
                  <Input type="text" value={location.latitude} readOnly />
                </div>
                <div className="space-y-2">
                  <Label>{t("admin.settings.longitude")}</Label>
                  <Input type="text" value={location.longitude} readOnly />
                </div>
              </div>
              <div className="space-y-2">
                <Label>{t("admin.settings.attendanceRadius")}</Label>
                <Input
                  type="number"
                  value={location.radius_meters}
                  onChange={(e) => handleRadiusChange(Number(e.target.value))}
                  min={10}
                  max={1000}
                />
                <p className="text-sm text-muted-foreground">
                  {t("admin.settings.studentsWithinRadiusRoom")}
                </p>
              </div>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
