export interface School {
  id: string;
  name: string;
  address?: string;
  city?: string;
  state?: string;
  zip?: string;
  country?: string;
  phone?: string;
  email?: string;
  website?: string;
  logoUrl?: string;
  primaryColor?: string;
  secondaryColor?: string;
  invitationCode?: string;
  isActive?: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface User {
  id: string;
  name: string;
  email: string;
  role: "student" | "teacher" | "admin";
  created_at?: string;
  updated_at?: string;
  photoUrl?: string;
  status?: string;
  last_login_at?: string;
  user_id?: string;
  school_id?: string;
  school?: string; // School name for backward compatibility
  invitationCode?: string; // Used during signup for school validation

  // Account status fields
  is_deleted?: boolean;
  deleted_at?: string;
  is_blocked?: boolean;
  blocked_at?: string;
  maintenance_mode?: boolean;
  profile_completed?: boolean;

  // Student specific fields
  studentId?: string;
  course?: string;
  block_id?: string; // ✅ Added missing block_id
  room_id?: string; // ✅ Added missing room_id
  blockName?: string;
  roomNumber?: string;
  biometricRegistered?: boolean;
  pin?: string;

  // Teacher specific fields
  teacherId?: string;
  department?: string;
  subject?: string;
  position?: string;

  // Admin specific fields
  adminId?: string;
  accessLevel?: number; // 1: School admin, 2: District admin, 3: System admin

  // Language preference
  preferred_language?: "en" | "tr";
}

export interface Student {
  id: string;
  user_id: string;
  name: string;
  email: string;
  role: string;
  studentId: string;
  course: string;
  biometricRegistered: boolean;
  block_id: string;
  room_id: string;
  blockName: string;
  roomNumber: string;
  pin: string;
  photoUrl: string;
  school_id?: string;
  school?: string; // School name for backward compatibility
  blocks?: {
    id: string;
    name: string;
  };
  rooms?: {
    id: string;
    name: string;
    block_id: string;
  };
}

export interface Teacher extends User {
  role: "teacher";
  teacherId: string;
  department: string;
  position?: string;
  subject?: string;
  school?: string;
}

export interface Admin extends User {
  role: "admin";
  adminId: string;
  accessLevel?: number;
  position?: string;
  school?: string;
  profileCompleted?: boolean;
}

export interface AttendanceRecord {
  id: string;
  studentId: string;
  roomId: string;
  timestamp: string;
  deviceInfo: string;
  location:
    | {
        latitude: number;
        longitude: number;
      }
    | any;
  verificationMethod: "biometric" | "pin" | "manual";
  status: "present" | "absent" | "late" | "excused";
  roomName?: string;
  buildingName?: string;
  school_id?: string;
}

export interface Block {
  id: string;
  name: string;
  created_at?: string;
  updated_at?: string;
}

export interface Room {
  id: string;
  name: string;
  block_id: string;
  floor: number;
  capacity: number;
  teacher_id: string;
  current_qr_code: string | null;
  qr_expiry: string | null;
  created_at?: string;
  updated_at?: string;
  school_id?: string;
}

export interface FraudCase {
  id: string;
  studentId: string;
  attendanceId: string;
  timestamp: string;
  evidenceType:
    | "location_mismatch"
    | "device_mismatch"
    | "time_pattern"
    | "multiple_scans";
  status: "pending" | "reviewing" | "resolved" | "dismissed";
  notes?: string;
}

export interface Course {
  id: string;
  name: string;
  teacherId: string;
  roomId: string;
  schedule: {
    day:
      | "monday"
      | "tuesday"
      | "wednesday"
      | "thursday"
      | "friday"
      | "saturday"
      | "sunday";
    startTime: string;
    endTime: string;
  }[];
}

export interface ThemeColors {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  foreground: string;
  muted: string;
  card: string;
  border: string;
}

export interface ThemeSettings {
  lightTheme: ThemeColors;
  darkTheme: ThemeColors;
  applyToAllSchools: boolean;
  targetSchoolId: string | null;
  overrideSchoolCustomizations?: boolean;
  updated_at?: string;
}

export interface Excuse {
  id: string;
  student_id: string;
  room_id: string;
  start_date: string;
  end_date: string;
  start_time: string;
  end_time: string;
  reason: string;
  status: "pending" | "approved" | "rejected";
  teacher_id?: string;
  created_at: string;
  updated_at: string;
  notes?: string;
  school_id?: string;

  // Joined fields
  studentName?: string;
  roomName?: string;
  teacherName?: string;
}
