import { useState, useEffect } from "react";
import { useExcuses } from "@/hooks/useExcuses";
import { useAuth } from "@/context/AuthContext";
import { supabase } from "@/lib/supabase";
import { format } from "date-fns";
import { calculateDaysBetween, formatDuration } from "@/lib/date-utils";
import { useToast } from "@/hooks/use-toast";
import { Excuse } from "@/lib/types";
import { useTranslation } from "react-i18next";
import {
  CheckCircle,
  XCircle,
  Clock,
  FileText,
  Calendar,
  Search,
  Filter,
  User,
  DoorClosed,
  X,
  Check,
  Download,
  BarChart,
  Trash2,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { Skeleton } from "@/components/ui/skeleton";

export default function AdminExcusesManagement() {
  const { profile } = useAuth();
  const { toast } = useToast();
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [selectedExcuse, setSelectedExcuse] = useState<Excuse | null>(null);
  const [teacherNotes, setTeacherNotes] = useState("");
  const [teachers, setTeachers] = useState<{ id: string; name: string }[]>([]);
  const [selectedTeacher, setSelectedTeacher] = useState<string>("all");
  const [confirmDelete, setConfirmDelete] = useState<string | null>(null);
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    approved: 0,
    rejected: 0,
  });

  // Fetch excuses using our custom hook
  const {
    excuses,
    loading,
    error,
    fetchExcuses,
    updateExcuseStatus,
    deleteExcuse,
  } = useExcuses({
    role: "admin",
    status: statusFilter !== "all" ? (statusFilter as any) : undefined,
  });

  // Fetch teachers
  useEffect(() => {
    const fetchTeachers = async () => {
      try {
        const { data, error } = await supabase
          .from("profiles")
          .select("id, name")
          .eq("role", "teacher");

        if (error) throw error;
        setTeachers(data || []);
      } catch (err) {
        console.error("Error fetching teachers:", err);
      }
    };

    fetchTeachers();
  }, []);

  // Calculate statistics
  useEffect(() => {
    if (!loading && excuses.length > 0) {
      const total = excuses.length;
      const pending = excuses.filter((e) => e.status === "pending").length;
      const approved = excuses.filter((e) => e.status === "approved").length;
      const rejected = excuses.filter((e) => e.status === "rejected").length;

      setStats({
        total,
        pending,
        approved,
        rejected,
      });
    }
  }, [excuses, loading]);

  // Filter excuses based on search query and teacher filter
  const filteredExcuses = excuses.filter((excuse) => {
    // Apply teacher filter
    if (selectedTeacher !== "all" && excuse.teacher_id !== selectedTeacher) {
      return false;
    }

    // Apply search filter
    if (!searchQuery) return true;

    const searchLower = searchQuery.toLowerCase();
    return (
      excuse.studentName?.toLowerCase().includes(searchLower) ||
      excuse.roomName?.toLowerCase().includes(searchLower) ||
      excuse.reason.toLowerCase().includes(searchLower)
    );
  });

  // Handle excuse approval
  const handleApproveExcuse = async () => {
    if (!selectedExcuse) return;

    try {
      await updateExcuseStatus(selectedExcuse.id, "approved", teacherNotes);
      setSelectedExcuse(null);
      setTeacherNotes("");
    } catch (error) {
      console.error("Error approving excuse:", error);
    }
  };

  // Handle excuse rejection
  const handleRejectExcuse = async () => {
    if (!selectedExcuse) return;

    try {
      await updateExcuseStatus(selectedExcuse.id, "rejected", teacherNotes);
      setSelectedExcuse(null);
      setTeacherNotes("");
    } catch (error) {
      console.error("Error rejecting excuse:", error);
    }
  };

  // Handle excuse deletion
  const handleDeleteExcuse = async (excuseId: string) => {
    try {
      await deleteExcuse(excuseId);
      setConfirmDelete(null);
      toast({
        title: t("admin.excusesManagement.excuseDeleted"),
        description: t("admin.excusesManagement.excuseDeletedSuccess"),
      });
    } catch (error) {
      console.error("Error deleting excuse:", error);
      toast({
        title: t("admin.excusesManagement.error"),
        description: t("admin.excusesManagement.failedToDelete"),
        variant: "destructive",
      });
    }
  };

  // Export excuses to CSV
  const exportExcuses = () => {
    // Create CSV content
    const headers = [
      t("admin.excusesManagement.date"),
      t("admin.excusesManagement.student"),
      t("admin.excusesManagement.room"),
      t("admin.excusesManagement.reason"),
      t("admin.excusesManagement.status"),
      t("admin.excusesManagement.teacher"),
      t("admin.excusesManagement.notes"),
    ];
    const rows = filteredExcuses.map((excuse) => [
      format(new Date(excuse.date), "yyyy-MM-dd"),
      excuse.studentName || t("admin.excusesManagement.unknownStudent"),
      excuse.roomName || t("admin.excusesManagement.unknownRoom"),
      excuse.reason,
      excuse.status,
      excuse.teacherName || t("admin.excusesManagement.notApplicable"),
      excuse.notes || "",
    ]);

    const csvContent = [
      headers.join(","),
      ...rows.map((row) =>
        row.map((cell) => `"${cell.replace(/"/g, '""')}"`).join(",")
      ),
    ].join("\n");

    // Create and download the file
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute(
      "download",
      `excuses_export_${format(new Date(), "yyyy-MM-dd")}.csv`
    );
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast({
      title: t("admin.excusesManagement.exportComplete"),
      description: t("admin.excusesManagement.excusesExported", {
        count: filteredExcuses.length,
      }),
    });
  };

  // Group excuses by status
  const pendingExcuses = filteredExcuses.filter(
    (excuse) => excuse.status === "pending"
  );
  const approvedExcuses = filteredExcuses.filter(
    (excuse) => excuse.status === "approved"
  );
  const rejectedExcuses = filteredExcuses.filter(
    (excuse) => excuse.status === "rejected"
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("admin.excusesManagement.title")}</CardTitle>
        <CardDescription>
          {t("admin.excusesManagement.description")}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {/* Statistics Cards */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-4">
              <div className="flex justify-between items-center">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    {t("admin.excusesManagement.total")}
                  </p>
                  <p className="text-2xl font-bold">{stats.total}</p>
                </div>
                <FileText className="h-8 w-8 text-muted-foreground opacity-50" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex justify-between items-center">
                <div>
                  <p className="text-sm font-medium text-yellow-600">
                    {t("admin.excusesManagement.pending")}
                  </p>
                  <p className="text-2xl font-bold">{stats.pending}</p>
                </div>
                <Clock className="h-8 w-8 text-yellow-500 opacity-50" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex justify-between items-center">
                <div>
                  <p className="text-sm font-medium text-green-600">
                    {t("admin.excusesManagement.approved")}
                  </p>
                  <p className="text-2xl font-bold">{stats.approved}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-500 opacity-50" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex justify-between items-center">
                <div>
                  <p className="text-sm font-medium text-red-600">
                    {t("admin.excusesManagement.rejected")}
                  </p>
                  <p className="text-2xl font-bold">{stats.rejected}</p>
                </div>
                <XCircle className="h-8 w-8 text-red-500 opacity-50" />
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={t("admin.excusesManagement.searchPlaceholder")}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-8 w-full"
            />
            {searchQuery && (
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-1 top-1 h-7 w-7"
                onClick={() => setSearchQuery("")}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
          <div className="flex gap-2">
            <Select value={selectedTeacher} onValueChange={setSelectedTeacher}>
              <SelectTrigger className="w-[180px]">
                <SelectValue
                  placeholder={t("admin.excusesManagement.filterByTeacher")}
                />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">
                  {t("admin.excusesManagement.allTeachers")}
                </SelectItem>
                {teachers.map((teacher) => (
                  <SelectItem key={teacher.id} value={teacher.id}>
                    {teacher.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue
                  placeholder={t("admin.excusesManagement.filterByStatus")}
                />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">
                  {t("admin.excusesManagement.allStatuses")}
                </SelectItem>
                <SelectItem value="pending">
                  {t("admin.excusesManagement.pending")}
                </SelectItem>
                <SelectItem value="approved">
                  {t("admin.excusesManagement.approved")}
                </SelectItem>
                <SelectItem value="rejected">
                  {t("admin.excusesManagement.rejected")}
                </SelectItem>
              </SelectContent>
            </Select>
            <Button
              variant="outline"
              className="flex items-center gap-1"
              onClick={exportExcuses}
            >
              <Download className="h-4 w-4" />
              {t("admin.excusesManagement.export")}
            </Button>
          </div>
        </div>

        <Tabs defaultValue="all">
          <TabsList className="grid grid-cols-4 mb-4">
            <TabsTrigger value="all">
              {t("admin.excusesManagement.all")}
              <Badge variant="secondary" className="ml-2">
                {filteredExcuses.length}
              </Badge>
            </TabsTrigger>
            <TabsTrigger value="pending" className="relative">
              {t("admin.excusesManagement.pending")}
              {pendingExcuses.length > 0 && (
                <Badge variant="secondary" className="ml-2">
                  {pendingExcuses.length}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="approved">
              {t("admin.excusesManagement.approved")}
              {approvedExcuses.length > 0 && (
                <Badge variant="secondary" className="ml-2">
                  {approvedExcuses.length}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="rejected">
              {t("admin.excusesManagement.rejected")}
              {rejectedExcuses.length > 0 && (
                <Badge variant="secondary" className="ml-2">
                  {rejectedExcuses.length}
                </Badge>
              )}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="all">
            {loading ? (
              <ExcusesSkeleton />
            ) : filteredExcuses.length === 0 ? (
              <EmptyState
                icon={
                  <FileText className="w-12 h-12 text-muted-foreground opacity-30" />
                }
                title={t("admin.excusesManagement.noExcusesFound")}
                description={t(
                  "admin.excusesManagement.noExcuseRequestsMatching"
                )}
              />
            ) : (
              <div className="space-y-4">
                {filteredExcuses.map((excuse) => (
                  <ExcuseCard
                    key={excuse.id}
                    excuse={excuse}
                    onClick={() => {
                      setSelectedExcuse(excuse);
                      setTeacherNotes(excuse.notes || "");
                    }}
                  />
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="pending">
            {loading ? (
              <ExcusesSkeleton />
            ) : pendingExcuses.length === 0 ? (
              <EmptyState
                icon={
                  <Clock className="w-12 h-12 text-muted-foreground opacity-30" />
                }
                title={t("admin.excusesManagement.noPendingExcuses")}
                description={t(
                  "admin.excusesManagement.noPendingExcuseRequests"
                )}
              />
            ) : (
              <div className="space-y-4">
                {pendingExcuses.map((excuse) => (
                  <ExcuseCard
                    key={excuse.id}
                    excuse={excuse}
                    onClick={() => {
                      setSelectedExcuse(excuse);
                      setTeacherNotes("");
                    }}
                    onDelete={() => setConfirmDelete(excuse.id)}
                  />
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="approved">
            {loading ? (
              <ExcusesSkeleton />
            ) : approvedExcuses.length === 0 ? (
              <EmptyState
                icon={
                  <CheckCircle className="w-12 h-12 text-muted-foreground opacity-30" />
                }
                title={t("admin.excusesManagement.noApprovedExcuses")}
                description={t(
                  "admin.excusesManagement.noApprovedExcuseRequests"
                )}
              />
            ) : (
              <div className="space-y-4">
                {approvedExcuses.map((excuse) => (
                  <ExcuseCard
                    key={excuse.id}
                    excuse={excuse}
                    onClick={() => {
                      setSelectedExcuse(excuse);
                      setTeacherNotes(excuse.notes || "");
                    }}
                  />
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="rejected">
            {loading ? (
              <ExcusesSkeleton />
            ) : rejectedExcuses.length === 0 ? (
              <EmptyState
                icon={
                  <XCircle className="w-12 h-12 text-muted-foreground opacity-30" />
                }
                title={t("admin.excusesManagement.noRejectedExcuses")}
                description={t(
                  "admin.excusesManagement.noRejectedExcuseRequests"
                )}
              />
            ) : (
              <div className="space-y-4">
                {rejectedExcuses.map((excuse) => (
                  <ExcuseCard
                    key={excuse.id}
                    excuse={excuse}
                    onClick={() => {
                      setSelectedExcuse(excuse);
                      setTeacherNotes(excuse.notes || "");
                    }}
                  />
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>

      {/* Excuse Details Dialog */}
      <Dialog
        open={!!selectedExcuse}
        onOpenChange={(open) => !open && setSelectedExcuse(null)}
      >
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>
              {t("admin.excusesManagement.excuseRequestDetails")}
            </DialogTitle>
            <DialogDescription>
              {t("admin.excusesManagement.reviewStudentExcuse")}
            </DialogDescription>
          </DialogHeader>

          {selectedExcuse && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground mb-1">
                    {t("admin.excusesManagement.student")}
                  </p>
                  <p className="font-medium">{selectedExcuse.studentName}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground mb-1">
                    {t("admin.excusesManagement.room")}
                  </p>
                  <p className="font-medium">
                    {t("admin.excusesManagement.room")}{" "}
                    {selectedExcuse.roomName}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground mb-1">
                    {t("admin.excusesManagement.date")}
                  </p>
                  <p>
                    {format(
                      new Date(selectedExcuse.start_date),
                      "MMMM d, yyyy"
                    )}
                    {selectedExcuse.start_date !== selectedExcuse.end_date &&
                      ` - ${format(
                        new Date(selectedExcuse.end_date),
                        "MMMM d, yyyy"
                      )}`}
                  </p>
                  <p className="text-xs text-primary mt-1">
                    {t("admin.excusesManagement.duration")}:{" "}
                    {formatDuration(
                      calculateDaysBetween(
                        selectedExcuse.start_date,
                        selectedExcuse.end_date
                      )
                    )}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground mb-1">
                    {t("admin.excusesManagement.time")}
                  </p>
                  <p>
                    {selectedExcuse.start_time} - {selectedExcuse.end_time}
                  </p>
                </div>
              </div>

              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">
                  {t("admin.excusesManagement.reason")}
                </p>
                <div className="p-3 bg-muted rounded-md text-sm">
                  {selectedExcuse.reason}
                </div>
              </div>

              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">
                  {t("admin.excusesManagement.status")}
                </p>
                <div className="flex items-center">
                  {getStatusBadge(selectedExcuse.status)}
                  {selectedExcuse.status !== "pending" && (
                    <p className="text-sm ml-2">
                      {t("admin.excusesManagement.by")}{" "}
                      {selectedExcuse.teacherName ||
                        t("admin.excusesManagement.unknownTeacher")}
                    </p>
                  )}
                </div>
              </div>

              {selectedExcuse.status === "pending" ? (
                <>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground mb-1">
                      {t("admin.excusesManagement.adminNotes")}
                    </p>
                    <Textarea
                      placeholder={t(
                        "admin.excusesManagement.addNotesPlaceholder"
                      )}
                      value={teacherNotes}
                      onChange={(e) => setTeacherNotes(e.target.value)}
                      className="resize-none"
                    />
                  </div>

                  <DialogFooter className="flex justify-between">
                    <Button
                      variant="destructive"
                      onClick={handleRejectExcuse}
                      className="flex items-center gap-1"
                    >
                      <X className="w-4 h-4" />
                      {t("admin.excusesManagement.reject")}
                    </Button>
                    <Button
                      onClick={handleApproveExcuse}
                      className="flex items-center gap-1"
                    >
                      <Check className="w-4 h-4" />
                      {t("admin.excusesManagement.approve")}
                    </Button>
                  </DialogFooter>
                </>
              ) : (
                selectedExcuse.notes && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground mb-1">
                      {t("admin.excusesManagement.teacherNotes")}
                    </p>
                    <div className="p-3 bg-muted rounded-md text-sm">
                      {selectedExcuse.notes}
                    </div>
                  </div>
                )
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={!!confirmDelete}
        onOpenChange={(open) => !open && setConfirmDelete(null)}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {t("admin.excusesManagement.confirmDeletion")}
            </DialogTitle>
            <DialogDescription>
              {t("admin.excusesManagement.confirmDeleteExcuse")}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setConfirmDelete(null)}>
              {t("admin.excusesManagement.cancel")}
            </Button>
            <Button
              variant="destructive"
              onClick={() => confirmDelete && handleDeleteExcuse(confirmDelete)}
            >
              {t("admin.excusesManagement.delete")}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
}

// Helper components
interface ExcuseCardProps {
  excuse: Excuse;
  onClick?: () => void;
  onDelete?: () => void;
}

function ExcuseCard({ excuse, onClick, onDelete }: ExcuseCardProps) {
  const handleClick = (e: React.MouseEvent) => {
    // If we're clicking the delete button, don't trigger the card click
    if ((e.target as HTMLElement).closest(".delete-button")) {
      e.stopPropagation();
      return;
    }
    onClick?.();
  };

  return (
    <div
      className="border rounded-lg p-4 hover:border-primary/50 transition-colors cursor-pointer"
      onClick={handleClick}
    >
      <div className="flex items-start justify-between">
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <User className="w-4 h-4 text-muted-foreground" />
            <h3 className="font-medium">
              {excuse.studentName ||
                t("admin.excusesManagement.unknownStudent")}
            </h3>
          </div>
          <div className="flex items-center gap-2">
            <DoorClosed className="w-4 h-4 text-muted-foreground" />
            <p className="text-sm text-muted-foreground">
              {t("admin.excusesManagement.room")}{" "}
              {excuse.roomName || t("admin.excusesManagement.unknownRoom")}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          {getStatusBadge(excuse.status)}
          {excuse.status === "pending" && onDelete && (
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 text-muted-foreground hover:text-destructive delete-button"
              onClick={(e) => {
                e.stopPropagation();
                onDelete();
              }}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      <div className="mt-3 grid grid-cols-2 gap-2 text-sm">
        <div className="flex flex-col gap-1">
          <div className="flex items-center gap-1.5 text-muted-foreground">
            <Calendar className="w-3.5 h-3.5" />
            <span>
              {format(new Date(excuse.start_date), "MMM dd, yyyy")}
              {excuse.start_date !== excuse.end_date &&
                ` - ${format(new Date(excuse.end_date), "MMM dd, yyyy")}`}
            </span>
          </div>
          {excuse.start_date && excuse.end_date && (
            <div className="text-xs text-primary ml-5">
              {t("admin.excusesManagement.duration")}:{" "}
              {formatDuration(
                calculateDaysBetween(excuse.start_date, excuse.end_date)
              )}
            </div>
          )}
        </div>
        <div className="flex items-center gap-1.5 text-muted-foreground">
          <Clock className="w-3.5 h-3.5" />
          <span>
            {excuse.start_time} - {excuse.end_time}
          </span>
        </div>
      </div>

      <div className="mt-2 flex items-start gap-1.5">
        <FileText className="w-3.5 h-3.5 text-muted-foreground mt-0.5" />
        <p className="text-sm text-muted-foreground line-clamp-1">
          {excuse.reason}
        </p>
      </div>
    </div>
  );
}

function getStatusBadge(status: string) {
  switch (status) {
    case "pending":
      return (
        <Badge
          variant="outline"
          className="bg-yellow-50 text-yellow-700 border-yellow-200 flex items-center gap-1"
        >
          <Clock className="w-3 h-3" />
          <span>{t("admin.excusesManagement.pending")}</span>
        </Badge>
      );
    case "approved":
      return (
        <Badge
          variant="outline"
          className="bg-green-50 text-green-700 border-green-200 flex items-center gap-1"
        >
          <CheckCircle className="w-3 h-3" />
          <span>{t("admin.excusesManagement.approved")}</span>
        </Badge>
      );
    case "rejected":
      return (
        <Badge
          variant="outline"
          className="bg-red-50 text-red-700 border-red-200 flex items-center gap-1"
        >
          <XCircle className="w-3 h-3" />
          <span>{t("admin.excusesManagement.rejected")}</span>
        </Badge>
      );
    default:
      return null;
  }
}

function EmptyState({
  icon,
  title,
  description,
}: {
  icon: React.ReactNode;
  title: string;
  description: string;
}) {
  return (
    <div className="text-center py-8">
      <div className="mx-auto mb-4">{icon}</div>
      <h3 className="text-lg font-medium mb-1">{title}</h3>
      <p className="text-sm text-muted-foreground">{description}</p>
    </div>
  );
}

function ExcusesSkeleton() {
  return (
    <div className="space-y-4">
      {[1, 2, 3].map((i) => (
        <div key={i} className="border rounded-lg p-4">
          <div className="flex items-start justify-between">
            <div className="space-y-2">
              <Skeleton className="h-5 w-[150px]" />
              <Skeleton className="h-4 w-[100px]" />
            </div>
            <Skeleton className="h-6 w-[80px]" />
          </div>
          <div className="mt-3 grid grid-cols-2 gap-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
          </div>
          <div className="mt-2">
            <Skeleton className="h-4 w-full" />
          </div>
        </div>
      ))}
    </div>
  );
}
