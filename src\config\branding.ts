/**
 * 🎨 APP BRANDING CONFIGURATION
 * =====================================================
 * This file centralizes all app branding and naming.
 * All values come from environment variables for easy customization.
 * 
 * 🚀 TO CHANGE APP NAME:
 * 1. Edit the .env file
 * 2. Restart dev server: npm run dev
 * 3. That's it! ✨
 */

// Get environment variables with fallbacks
const getEnvVar = (key: string, fallback: string): string => {
  const value = import.meta.env[key];
  if (!value || value === '') {
    console.warn(`⚠️ Environment variable ${key} is not set, using fallback: ${fallback}`);
    return fallback;
  }
  return value;
};

// 🎨 Main App Branding
export const BRANDING = {
  // Core App Identity
  APP_NAME: getEnvVar('VITE_APP_NAME', 'Attendance Tracking System'),
  APP_SHORT_NAME: getEnvVar('VITE_APP_SHORT_NAME', 'ATS'),
  APP_DESCRIPTION: getEnvVar('VITE_APP_DESCRIPTION', 'Secure and efficient attendance tracking system for educational institutions'),
  
  // Company/Organization Info
  COMPANY_NAME: getEnvVar('VITE_COMPANY_NAME', 'Attendance Tracking System Team'),
  COMPANY_WEBSITE: getEnvVar('VITE_COMPANY_WEBSITE', 'https://attendancetracking.edu'),
  
  // Contact Information
  CONTACT_EMAIL: getEnvVar('VITE_CONTACT_EMAIL', '<EMAIL>'),
  SUPPORT_EMAIL: getEnvVar('VITE_SUPPORT_EMAIL', '<EMAIL>'),
  NOREPLY_EMAIL: getEnvVar('VITE_NOREPLY_EMAIL', '<EMAIL>'),
} as const;

// 📧 Email Templates
export const EMAIL_TEMPLATES = {
  // Email signatures
  SIGNATURE: BRANDING.APP_NAME,
  FOOTER: `Thank you,\n${BRANDING.APP_NAME}`,
  
  // Subject prefixes
  SUBJECT_PREFIX: `${BRANDING.APP_SHORT_NAME} -`,
  
  // Common email content
  SUPPORT_CONTACT: `If you have any questions, please contact us at ${BRANDING.SUPPORT_EMAIL}`,
} as const;

// 🌐 SEO & Meta Tags
export const SEO = {
  TITLE: BRANDING.APP_NAME,
  DESCRIPTION: BRANDING.APP_DESCRIPTION,
  KEYWORDS: `attendance, tracking, school, education, ${BRANDING.APP_SHORT_NAME.toLowerCase()}`,
  AUTHOR: BRANDING.COMPANY_NAME,
  
  // Open Graph
  OG_TITLE: BRANDING.APP_NAME,
  OG_DESCRIPTION: BRANDING.APP_DESCRIPTION,
  OG_SITE_NAME: BRANDING.APP_NAME,
} as const;

// 🎯 UI Text Helpers
export const UI_TEXT = {
  // Loading messages
  LOADING: `Loading ${BRANDING.APP_NAME}...`,
  WELCOME: `Welcome to ${BRANDING.APP_NAME}!`,
  
  // Login/Auth
  LOGIN_TITLE: BRANDING.APP_NAME,
  LOGIN_SUBTITLE: 'Login to access the attendance system',
  SIGNUP_SUBTITLE: `${BRANDING.APP_NAME} Registration`,
  
  // Dashboard
  DASHBOARD_TITLE: `${BRANDING.APP_NAME} Dashboard`,
  
  // Footer
  FOOTER_COPYRIGHT: `© ${new Date().getFullYear()} ${BRANDING.COMPANY_NAME}. All rights reserved.`,
} as const;

// 🔧 Development Helpers
export const DEV_INFO = {
  // For debugging
  ENV_CHECK: () => {
    console.group('🎨 App Branding Configuration');
    console.log('App Name:', BRANDING.APP_NAME);
    console.log('Short Name:', BRANDING.APP_SHORT_NAME);
    console.log('Company:', BRANDING.COMPANY_NAME);
    console.log('Contact:', BRANDING.CONTACT_EMAIL);
    console.groupEnd();
  },
  
  // Validate configuration
  VALIDATE: () => {
    const issues: string[] = [];
    
    if (BRANDING.APP_NAME.length > 50) {
      issues.push('App name is too long (>50 chars)');
    }
    
    if (!BRANDING.CONTACT_EMAIL.includes('@')) {
      issues.push('Contact email appears invalid');
    }
    
    if (issues.length > 0) {
      console.warn('⚠️ Branding Configuration Issues:', issues);
    } else {
      console.log('✅ Branding configuration looks good!');
    }
    
    return issues.length === 0;
  }
} as const;

// 🚀 Export everything for easy importing
export default BRANDING;

// Type definitions for TypeScript
export type BrandingConfig = typeof BRANDING;
export type EmailTemplates = typeof EMAIL_TEMPLATES;
export type SEOConfig = typeof SEO;
export type UITextConfig = typeof UI_TEXT;
