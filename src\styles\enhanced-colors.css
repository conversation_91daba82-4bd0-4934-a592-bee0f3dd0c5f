/* Enhanced Color System for Student UI */

/* Color Variables */
:root {
  /* Base Colors */
  --orange-primary: #f39228;
  --pink-accent: #ff3366;
  --dark-gray: #1e2124;
  --light-gray: #c9d1d9;

  /* Status Colors */
  --status-present: #10b981;
  --status-late: #f59e0b;
  --status-absent: #ef4444;
  --status-excused: #3b82f6;

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, #f39228, #ff8f00);
  --gradient-accent: linear-gradient(135deg, #ff3366, #ff5f6d);
  --gradient-success: linear-gradient(135deg, #10b981, #059669);

  /* Shadows */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-accent: 0 4px 14px rgba(255, 51, 102, 0.25);
  --shadow-primary: 0 4px 14px rgba(243, 146, 40, 0.25);
}

/* Dark Mode Enhanced Colors */
.dark {
  /* Override primary with vibrant orange */
  --primary: 32 89% 56%;
  --primary-foreground: 0 0% 100%;

  /* Add pink accent color */
  --accent: 346 100% 60%;
  --accent-foreground: 0 0% 100%;

  /* Enhanced tab colors */
  --tab-background: 220 7% 13%;
  --tab-active: 220 7% 17%;
  --tab-hover: 220 7% 15%;

  /* Status colors with proper HSL values */
  --status-present-hsl: 158 84% 40%;
  --status-late-hsl: 38 92% 50%;
  --status-absent-hsl: 0 84% 60%;
  --status-excused-hsl: 217 91% 60%;

  /* Enhanced card styling */
  --card-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  --card-border: 1px solid rgba(255, 255, 255, 0.05);
  --card-hover-transform: translateY(-2px);
}

/* Light Mode Enhanced Colors */
:root:not(.dark) {
  /* Use darker orange for light mode for better contrast */
  --primary: 32 80% 48%;
  --primary-foreground: 0 0% 100%;

  /* Add pink accent color */
  --accent: 346 100% 50%;
  --accent-foreground: 0 0% 100%;

  /* Status colors with proper HSL values */
  --status-present-hsl: 158 84% 32%;
  --status-late-hsl: 38 92% 40%;
  --status-absent-hsl: 0 84% 50%;
  --status-excused-hsl: 217 91% 50%;

  /* Enhanced card styling */
  --card-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  --card-border: 1px solid rgba(0, 0, 0, 0.05);
  --card-hover-transform: translateY(-1px);
}

/* Enhanced Card Styling */
.enhanced-card {
  border-radius: var(--radius);
  transition: all 0.2s ease-in-out;
  overflow: hidden;
}

.dark .enhanced-card {
  background: linear-gradient(145deg, #1e2124, #262a2e);
  box-shadow: var(--card-shadow);
  border: var(--card-border);
}

.enhanced-card:hover {
  transform: var(--card-hover-transform);
  box-shadow: var(--shadow-md);
}

/* Status Indicators */
.status-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
}

.status-present {
  background-color: var(--status-present);
  color: white;
}

.status-late {
  background-color: var(--status-late);
  color: white;
}

.status-absent {
  background-color: var(--status-absent);
  color: white;
}

.status-excused {
  background-color: var(--status-excused);
  color: white;
}

/* Button Enhancements */
.btn-primary {
  background: var(--gradient-primary);
  transition: all 0.2s ease;
  box-shadow: var(--shadow-primary);
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(243, 146, 40, 0.3);
}

.btn-accent {
  background: var(--gradient-accent);
  transition: all 0.2s ease;
  box-shadow: var(--shadow-accent);
}

.btn-accent:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(255, 51, 102, 0.3);
}

/* Focus States */
.focus-ring:focus-visible {
  outline: none;
  box-shadow: 0 0 0 2px var(--background), 0 0 0 4px hsl(var(--primary));
}

/* Animations */
@keyframes pulse-primary {
  0% {
    box-shadow: 0 0 0 0 rgba(243, 146, 40, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(243, 146, 40, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(243, 146, 40, 0);
  }
}

@keyframes pulse-accent {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 51, 102, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 51, 102, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 51, 102, 0);
  }
}

.animate-pulse-primary {
  animation: pulse-primary 2s infinite;
}

.animate-pulse-accent {
  animation: pulse-accent 2s infinite;
}
