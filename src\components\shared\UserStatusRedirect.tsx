import React, { useEffect, useState } from "react";
import { useAuth } from "@/context/AuthContext";
import { supabase } from "@/lib/supabase";
import BlockedUserPage from "./BlockedUserPage";
import MaintenanceModePage from "./MaintenanceModePage";
import { Loader2 } from "lucide-react";
import { LoadingSpinner } from "@/components/ui/loading-spinner";

interface UserStatusRedirectProps {
  children: React.ReactNode;
}

const UserStatusRedirect: React.FC<UserStatusRedirectProps> = ({
  children,
}) => {
  const { user } = useAuth();
  const [isBlocked, setIsBlocked] = useState<boolean | null>(null);
  const [isInMaintenanceMode, setIsInMaintenanceMode] = useState<
    boolean | null
  >(null);
  const [userName, setUserName] = useState<string>("");
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkUserStatus = async () => {
      if (!user) {
        setLoading(false);
        return;
      }

      try {
        // Get the user's profile directly from the database
        const { data, error } = await supabase
          .from("profiles")
          .select(
            "name, profile_completed, department, maintenance_mode, is_blocked"
          )
          .eq("id", user.id)
          .single();

        if (error) {
          console.error("Error fetching user status:", error);
          setLoading(false);
          return;
        }

        // Check if user is blocked (only check is_blocked flag)
        // Profile completion is handled by AuthContext redirect logic
        const isUserBlocked = data.is_blocked === true;
        setIsBlocked(isUserBlocked);

        // Check if user is in maintenance mode (either maintenance_mode is true or department contains "Maintenance Mode")
        const isUserInMaintenanceMode =
          data.maintenance_mode === true ||
          (data.department && data.department.includes("Maintenance Mode"));
        setIsInMaintenanceMode(isUserInMaintenanceMode);

        // Set user name for personalized messages
        setUserName(data.name || "");

        // Set up real-time subscription to profile changes
        const subscription = supabase
          .channel(`profile-changes-${user.id}`)
          .on(
            "postgres_changes",
            {
              event: "UPDATE",
              schema: "public",
              table: "profiles",
              filter: `id=eq.${user.id}`,
            },
            (payload) => {
              const newData = payload.new as any;
              // Check if user is blocked (only check is_blocked flag)
              const isUserBlocked = newData.is_blocked === true;
              const isUserInMaintenanceMode =
                newData.maintenance_mode === true ||
                (newData.department &&
                  newData.department.includes("Maintenance Mode"));

              // Update the state with the new values
              setIsBlocked(isUserBlocked);
              setIsInMaintenanceMode(isUserInMaintenanceMode);
              setUserName(newData.name || "");
            }
          )
          .subscribe();

        setLoading(false);

        // Clean up subscription on unmount
        return () => {
          subscription.unsubscribe();
        };
      } catch (error) {
        console.error("Error in user status check:", error);
        setLoading(false);
      }
    };

    checkUserStatus();
  }, [user]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner message="Checking user status..." size="lg" />
      </div>
    );
  }

  if (isBlocked) {
    return <BlockedUserPage userName={userName} />;
  }

  if (isInMaintenanceMode) {
    return <MaintenanceModePage userName={userName} />;
  }

  return <>{children}</>;
};

export default UserStatusRedirect;
