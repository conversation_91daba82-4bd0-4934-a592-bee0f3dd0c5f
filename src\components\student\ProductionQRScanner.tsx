import { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { toast as sonnerToast } from "sonner";
import {
  Camera,
  Check,
  Loader2,
  KeyRound,
  Fingerprint,
  MapPin,
  Clock,
  AlertCircle,
  Shield,
  X,
  RefreshCw,
} from "lucide-react";
import { useAuth } from "@/context/AuthContext";
import { supabase } from "@/integrations/supabase/client";
import { useTranslation } from "react-i18next";
import QrScanner from "qr-scanner";
import {
  verifyQRCode,
  validateRoomAssignment,
  checkReplayAttack,
  generateDeviceFingerprint,
  validateLocation,
  type SignedQRData,
} from "@/lib/utils/qr-security";

// Use the SignedQRData type from security utils
type QRData = SignedQRData;

interface LocationState {
  latitude: number;
  longitude: number;
  accuracy: number;
}

export default function ProductionQRScanner() {
  // Scanner state
  const [isScanning, setIsScanning] = useState(false);
  const [isCameraActive, setIsCameraActive] = useState(false);
  const [qrData, setQrData] = useState<QRData | null>(null);
  const [scanError, setScanError] = useState<string>("");

  // Verification state
  const [showVerification, setShowVerification] = useState(false);
  const [verificationType, setVerificationType] = useState<
    "biometric" | "pin" | null
  >(null);
  const [pin, setPin] = useState("");
  const [pinError, setPinError] = useState("");

  // Attendance state
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [success, setSuccess] = useState(false);
  const [isAlreadyPresent, setIsAlreadyPresent] = useState(false);
  const [location, setLocation] = useState<LocationState | null>(null);

  // Room validation state
  const [roomValidation, setRoomValidation] = useState<{
    isValid: boolean;
    message: string;
    roomName?: string;
    blockName?: string;
  } | null>(null);

  const { profile, user } = useAuth();
  const { t } = useTranslation();
  const videoRef = useRef<HTMLVideoElement>(null);
  const qrScannerRef = useRef<QrScanner | null>(null);

  // Cleanup camera on unmount
  useEffect(() => {
    return () => {
      if (qrScannerRef.current) {
        qrScannerRef.current.stop();
        qrScannerRef.current.destroy();
      }
    };
  }, []);

  // Start camera and QR scanning
  const startCamera = async () => {
    try {
      setIsScanning(true);
      setIsCameraActive(true);
      setScanError("");

      if (!videoRef.current) {
        throw new Error("Video element not found");
      }

      // Initialize QR Scanner
      qrScannerRef.current = new QrScanner(
        videoRef.current,
        (result) => handleQRScanResult(result.data),
        {
          highlightScanRegion: true,
          highlightCodeOutline: true,
          preferredCamera: "environment", // Use back camera on mobile
        }
      );

      await qrScannerRef.current.start();
    } catch (error) {
      console.error("Error starting camera:", error);
      setScanError("Failed to access camera. Please check permissions.");
      setIsScanning(false);
      setIsCameraActive(false);
    }
  };

  // Stop camera
  const stopCamera = () => {
    if (qrScannerRef.current) {
      qrScannerRef.current.stop();
    }
    setIsCameraActive(false);
    setIsScanning(false);
  };

  // Handle QR scan result
  const handleQRScanResult = async (data: string) => {
    try {
      console.log("QR Code scanned:", data);

      // Parse QR data
      const parsedData: QRData = JSON.parse(data);

      // Cryptographic verification
      const verification = verifyQRCode(parsedData);
      if (!verification.isValid) {
        throw new Error(verification.error || "QR code verification failed");
      }

      // Validate school
      if (parsedData.school_id !== profile?.school_id) {
        throw new Error("This QR code is for a different school.");
      }

      // Check for replay attacks
      const isReplay = await checkReplayAttack(
        parsedData.session_id,
        user?.id || ""
      );
      if (isReplay) {
        throw new Error(
          "This QR code has already been used. Please scan a fresh code."
        );
      }

      setQrData(parsedData);
      stopCamera();

      // Validate room assignment
      await validateStudentRoomAssignment(parsedData);
    } catch (error) {
      console.error("QR scan error:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Invalid QR code";
      setScanError(errorMessage);
      sonnerToast.error("Scan Error", {
        description: errorMessage,
      });
    }
  };

  // Validate if student is assigned to this room
  const validateStudentRoomAssignment = async (qrData: QRData) => {
    try {
      // Get room and block information
      const { data: roomData, error: roomError } = await supabase
        .from("rooms")
        .select(
          `
          id,
          name,
          block_id,
          blocks (
            id,
            name
          )
        `
        )
        .eq("id", qrData.room_id)
        .single();

      if (roomError || !roomData) {
        throw new Error("Room not found");
      }

      // Use security utility for room validation
      const validation = validateRoomAssignment(
        qrData,
        profile?.room_id || null,
        profile?.block_id || null
      );

      if (validation.level === "perfect") {
        setRoomValidation({
          isValid: true,
          message: `Welcome! You're scanning from your assigned room (${roomData.name}).`,
          roomName: roomData.name,
          blockName: roomData.blocks?.name,
        });
        setShowVerification(true);
      } else if (validation.level === "warning") {
        setRoomValidation({
          isValid: true,
          message: `You're in ${roomData.blocks?.name} but not your assigned room. Attendance will still be recorded.`,
          roomName: roomData.name,
          blockName: roomData.blocks?.name,
        });
        setShowVerification(true);
      } else {
        setRoomValidation({
          isValid: false,
          message: `This QR code is for ${roomData.blocks?.name} - ${roomData.name}. Please scan the QR code in your assigned room.`,
          roomName: roomData.name,
          blockName: roomData.blocks?.name,
        });
      }
    } catch (error) {
      console.error("Room validation error:", error);
      setScanError("Failed to validate room assignment");
    }
  };

  // Handle verification method selection
  const handleVerificationMethod = async (method: "biometric" | "pin") => {
    setVerificationType(method);

    if (method === "biometric") {
      // TODO: Implement biometric verification
      sonnerToast.info(
        "Biometric verification not yet implemented. Please use PIN."
      );
      return;
    }

    // For PIN verification, just show the PIN input
    // The actual verification happens in handlePinSubmit
  };

  // Handle PIN submission
  const handlePinSubmit = async () => {
    if (!pin.trim()) {
      setPinError("Please enter your PIN");
      return;
    }

    if (!profile?.pin) {
      setPinError("No PIN set in your profile. Please update your profile.");
      return;
    }

    if (pin !== profile.pin) {
      setPinError("Incorrect PIN. Please try again.");
      setPin("");
      return;
    }

    setPinError("");
    await processAttendance();
  };

  // Process attendance recording
  const processAttendance = async () => {
    if (!qrData || !user) return;

    setIsSubmitting(true);

    try {
      // Get current location
      await getCurrentLocation();

      // Check if already present today
      const today = new Date().toISOString().split("T")[0];
      const { data: existingAttendance, error: checkError } = await supabase
        .from("attendance_records")
        .select("*")
        .eq("student_id", user.id)
        .eq("room_id", qrData.room_id)
        .gte("timestamp", `${today}T00:00:00.000Z`)
        .lt("timestamp", `${today}T23:59:59.999Z`);

      if (checkError) {
        throw checkError;
      }

      if (existingAttendance && existingAttendance.length > 0) {
        setIsAlreadyPresent(true);
        setSuccess(true);
        sonnerToast.info("Already Present", {
          description: `Your attendance was already recorded at ${new Date(
            existingAttendance[0].timestamp
          ).toLocaleTimeString()}`,
        });
        return;
      }

      // Record new attendance
      await recordAttendance();
    } catch (error) {
      console.error("Error processing attendance:", error);
      sonnerToast.error("Error", {
        description: "Failed to record attendance. Please try again.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get current location
  const getCurrentLocation = (): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        console.warn("Geolocation not supported");
        resolve();
        return;
      }

      navigator.geolocation.getCurrentPosition(
        (position) => {
          setLocation({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy,
          });
          resolve();
        },
        (error) => {
          console.warn("Location error:", error);
          resolve(); // Don't fail attendance for location issues
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 60000,
        }
      );
    });
  };

  // Record attendance in database
  const recordAttendance = async () => {
    if (!qrData || !user) return;

    // Generate device fingerprint for additional security
    const deviceFingerprint = generateDeviceFingerprint();

    const attendanceRecord = {
      student_id: user.id,
      room_id: qrData.room_id,
      qr_session_id: qrData.session_id,
      timestamp: new Date().toISOString(),
      verification_method: verificationType || "pin",
      location_data: location
        ? {
            type: "Point",
            coordinates: [location.longitude, location.latitude],
            accuracy: location.accuracy,
          }
        : null,
      device_info: {
        user_agent: navigator.userAgent,
        fingerprint: deviceFingerprint,
        screen_resolution: `${screen.width}x${screen.height}`,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        language: navigator.language,
      },
      status: "present",
      school_id: profile?.school_id,
      block_id: qrData.block_id,
      qr_data_hash: qrData.signature, // Store QR signature for audit trail
    };

    const { error } = await supabase
      .from("attendance_records")
      .insert(attendanceRecord);

    if (error) {
      throw error;
    }

    setSuccess(true);
    sonnerToast.success("Attendance Recorded", {
      description: "Your attendance has been successfully recorded!",
    });
  };

  // Reset scanner
  const resetScanner = () => {
    setQrData(null);
    setShowVerification(false);
    setVerificationType(null);
    setPin("");
    setPinError("");
    setSuccess(false);
    setIsAlreadyPresent(false);
    setRoomValidation(null);
    setScanError("");
    setLocation(null);
  };

  // Render success state
  if (success) {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardHeader className="text-center">
          <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
            <Check className="w-8 h-8 text-green-600" />
          </div>
          <CardTitle className="text-green-600">
            {isAlreadyPresent ? "Already Present" : "Attendance Recorded"}
          </CardTitle>
          <CardDescription>
            {isAlreadyPresent
              ? "Your attendance was already recorded for today"
              : "Your attendance has been successfully recorded"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={resetScanner} className="w-full">
            Scan Another QR Code
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="w-5 h-5" />
          QR Code Attendance
        </CardTitle>
        <CardDescription>
          Scan the QR code displayed in your classroom
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Camera View */}
        {isCameraActive && (
          <div className="relative">
            <video
              ref={videoRef}
              className="w-full h-64 object-cover rounded-lg border"
              playsInline
              muted
            />
            <Button
              onClick={stopCamera}
              variant="outline"
              size="sm"
              className="absolute top-2 right-2"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        )}

        {/* Scan Error */}
        {scanError && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-md">
            <div className="flex items-center gap-2">
              <AlertCircle className="w-4 h-4 text-red-600" />
              <p className="text-sm text-red-700">{scanError}</p>
            </div>
          </div>
        )}

        {/* Room Validation */}
        {roomValidation && !roomValidation.isValid && (
          <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
            <div className="flex items-center gap-2">
              <AlertCircle className="w-4 h-4 text-yellow-600" />
              <p className="text-sm text-yellow-700">
                {roomValidation.message}
              </p>
            </div>
          </div>
        )}

        {/* Start Scanning Button */}
        {!isCameraActive && !showVerification && (
          <Button
            onClick={startCamera}
            className="w-full"
            disabled={isScanning}
          >
            {isScanning ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Starting Camera...
              </>
            ) : (
              <>
                <Camera className="w-4 h-4 mr-2" />
                Start Scanning
              </>
            )}
          </Button>
        )}

        {/* Verification Methods */}
        {showVerification && !verificationType && roomValidation?.isValid && (
          <div className="space-y-3">
            <div className="p-3 bg-green-50 border border-green-200 rounded-md">
              <p className="text-sm text-green-700">{roomValidation.message}</p>
            </div>
            <p className="text-sm font-medium">Choose verification method:</p>
            <div className="grid grid-cols-2 gap-2">
              <Button
                onClick={() => handleVerificationMethod("biometric")}
                variant="outline"
                className="flex flex-col items-center p-4 h-auto"
              >
                <Fingerprint className="w-6 h-6 mb-1" />
                <span className="text-xs">Biometric</span>
              </Button>
              <Button
                onClick={() => handleVerificationMethod("pin")}
                variant="outline"
                className="flex flex-col items-center p-4 h-auto"
              >
                <KeyRound className="w-6 h-6 mb-1" />
                <span className="text-xs">PIN</span>
              </Button>
            </div>
          </div>
        )}

        {/* PIN Input */}
        {verificationType === "pin" && (
          <div className="space-y-3">
            <div>
              <Input
                type="password"
                placeholder="Enter your PIN"
                value={pin}
                onChange={(e) => setPin(e.target.value)}
                onKeyPress={(e) => e.key === "Enter" && handlePinSubmit()}
                className={pinError ? "border-red-500" : ""}
              />
              {pinError && (
                <p className="text-sm text-red-600 mt-1">{pinError}</p>
              )}
            </div>
            <div className="grid grid-cols-2 gap-2">
              <Button
                onClick={() => setVerificationType(null)}
                variant="outline"
              >
                Back
              </Button>
              <Button onClick={handlePinSubmit} disabled={isSubmitting}>
                {isSubmitting ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  "Verify"
                )}
              </Button>
            </div>
          </div>
        )}

        {/* Reset Button */}
        {(scanError || (roomValidation && !roomValidation.isValid)) && (
          <Button onClick={resetScanner} variant="outline" className="w-full">
            <RefreshCw className="w-4 h-4 mr-2" />
            Try Again
          </Button>
        )}
      </CardContent>
    </Card>
  );
}
