import { useState, useEffect, useMemo } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell,
} from "@/components/ui/table";
import {
  User,
  Check,
  X,
  Search,
  Building2,
  DoorOpen,
  UserCircle,
  MoreVertical,
  Edit,
  Trash2,
  LayoutGrid,
  LayoutList,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { toast as sonnerToast } from "sonner";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Student, AttendanceRecord, Block, Room } from "@/lib/types";
import { supabase } from "@/lib/supabase";
import { format } from "date-fns";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { getStatusIcon } from "../teacher/dashboard/utils/statusIcons";
import { useAuth } from "@/context/AuthContext";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { Label } from "@/components/ui/label";
import { useTranslation } from "react-i18next";

export default function AdminStudentDirectory() {
  const { t } = useTranslation();
  const [students, setStudents] = useState<Student[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [attendanceRecords, setAttendanceRecords] = useState<
    Record<string, AttendanceRecord>
  >({});
  const [selectedRoom, setSelectedRoom] = useState<string>(() => {
    const savedRoom = localStorage.getItem("adminSelectedRoom");
    return savedRoom || "none";
  });
  const [blocks, setBlocks] = useState<Block[]>([]);
  const [rooms, setRooms] = useState<Room[]>([]);
  const [selectedBlock, setSelectedBlock] = useState<string>(() => {
    const savedBlock = localStorage.getItem("adminSelectedBlock");
    return savedBlock || "all";
  });
  const [editingBlock, setEditingBlock] = useState<Block | null>(null);
  const [editingRoom, setEditingRoom] = useState<Room | null>(null);
  const [isAddingBlock, setIsAddingBlock] = useState(false);
  const [isAddingRoom, setIsAddingRoom] = useState(false);
  const [newBlockName, setNewBlockName] = useState("");
  const [newRoomName, setNewRoomName] = useState("");
  const [viewMode, setViewMode] = useState<"list" | "grid">(() => {
    const savedViewMode = localStorage.getItem("adminStudentDirectoryViewMode");
    return (savedViewMode as "list" | "grid") || "list";
  });
  const { toast } = useToast();
  const { profile } = useAuth();

  // Toggle view mode between list and grid
  const toggleViewMode = () => {
    const newMode = viewMode === "list" ? "grid" : "list";
    setViewMode(newMode);
    localStorage.setItem("adminStudentDirectoryViewMode", newMode);
  };

  // Fetch blocks and rooms
  useEffect(() => {
    const fetchBlocksAndRooms = async () => {
      try {
        // Fetch blocks for the current school (if school admin)
        let blocksQuery = supabase.from("blocks").select("*").order("name");

        // Filter by school_id if available (for school admins)
        if (profile?.school_id && profile?.access_level !== 3) {
          // Only filter by school_id if the admin is not a system admin (access_level 3)
          blocksQuery = blocksQuery.eq("school_id", profile.school_id);
        }

        const { data: blocksData, error: blocksError } = await blocksQuery;

        if (blocksError) throw blocksError;
        console.log("Fetched blocks:", blocksData);
        setBlocks(blocksData || []);

        // After fetching blocks, check localStorage and set selected block
        const savedBlock = localStorage.getItem("adminSelectedBlock");
        console.log("Saved block:", savedBlock);

        if (savedBlock) {
          // Check if the saved block exists in the fetched blocks
          const blockExists = blocksData?.some(
            (block) => block.id === savedBlock
          );
          console.log("Block exists:", blockExists);

          if (blockExists) {
            setSelectedBlock(savedBlock);
          } else {
            // If saved block doesn't exist anymore, reset to "all"
            localStorage.setItem("adminSelectedBlock", "all");
            setSelectedBlock("all");
          }
        }

        // Fetch rooms for the current school (if school admin)
        let roomsQuery = supabase.from("rooms").select("*").order("name");

        // Filter by school_id if available (for school admins)
        if (profile?.school_id && profile?.access_level !== 3) {
          // Only filter by school_id if the admin is not a system admin (access_level 3)
          roomsQuery = roomsQuery.eq("school_id", profile.school_id);
        }

        const { data: roomsData, error: roomsError } = await roomsQuery;

        if (roomsError) throw roomsError;
        console.log("Fetched rooms:", roomsData);
        setRooms(roomsData || []);

        // Check saved room
        const savedRoom = localStorage.getItem("adminSelectedRoom");
        console.log("Saved room:", savedRoom);

        if (savedRoom === "none") {
          // If explicitly saved as "none", keep it as "none"
          setSelectedRoom("none");
        } else if (savedRoom) {
          const roomExists = roomsData?.some((room) => room.id === savedRoom);
          if (roomExists) {
            setSelectedRoom(savedRoom);
          } else {
            // If saved room doesn't exist anymore, reset to "none"
            localStorage.setItem("adminSelectedRoom", "none");
            setSelectedRoom("none");
          }
        } else {
          // If no saved room, default to "none"
          localStorage.setItem("adminSelectedRoom", "none");
          setSelectedRoom("none");
        }
      } catch (error) {
        console.error("Error fetching blocks and rooms:", error);
        toast({
          title: t("common.error"),
          description: t("students.directory.fetchBlocksRoomsError"),
          variant: "destructive",
        });
      }
    };

    fetchBlocksAndRooms();
  }, []);

  // Fetch students data
  const fetchStudents = async () => {
    setLoading(true);
    try {
      console.log("Fetching students...");

      // Get student profiles with their assigned block and room information
      let query = supabase.from("profiles").select("*").eq("role", "student");

      // Filter by school_id if available (for school admins)
      if (profile?.school_id && profile?.access_level !== 3) {
        // Only filter by school_id if the admin is not a system admin (access_level 3)
        query = query.eq("school_id", profile.school_id);
      }

      const { data: profileData, error: profileError } = await query;

      if (profileError) {
        console.error("Error fetching students:", profileError.message);
        toast({
          title: "Error",
          description:
            profileError.message || "Could not load student directory",
          variant: "destructive",
        });
        return;
      }

      console.log("Raw student profiles:", profileData);

      if (profileData) {
        const studentProfiles: Student[] = profileData.map((profile) => {
          // Create the student object
          const student = {
            id: profile.id,
            user_id: profile.user_id || "",
            name: profile.name || "Unknown",
            email: profile.email || "",
            role: "student",
            studentId: profile.student_id || "",
            course: profile.course || "",
            biometricRegistered: profile.biometric_registered || false,
            block_id: profile.block_id || "",
            room_id: profile.room_id || "",
            blockName: profile.block_name || "Not Assigned",
            roomNumber: profile.room_number || "Not Assigned",
            pin: profile.pin || "",
            photoUrl: profile.photo_url || "",
            blocks: profile.block_name
              ? {
                  id: profile.block_id || "",
                  name: profile.block_name,
                }
              : undefined,
            rooms: profile.room_number
              ? {
                  id: profile.room_id || "",
                  name: profile.room_number,
                  block_id: profile.block_id || "",
                }
              : undefined,
          };

          console.log("Processing student:", {
            name: student.name,
            block_name: profile.block_name,
            room_number: profile.room_number,
            block_id: profile.block_id,
            room_id: profile.room_id,
          });

          return student;
        });

        console.log("Processed student profiles:", studentProfiles);
        setStudents(studentProfiles);
      }
    } catch (error) {
      console.error("Error in fetchStudents:", error);
      toast({
        title: "Error",
        description: "Could not load student directory. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch attendance records
  const fetchAttendanceRecords = async () => {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const todayStr = today.toISOString();
      const tomorrowStr = new Date(
        today.getTime() + 24 * 60 * 60 * 1000
      ).toISOString();

      console.log(
        "AdminStudentDirectory: Fetching attendance records for date range:",
        {
          from: todayStr,
          to: tomorrowStr,
        }
      );

      // Get all student IDs for the query
      let studentIds = students.map((student) => student.id);

      // If we don't have any students yet, fetch all student IDs from the database
      if (studentIds.length === 0) {
        console.log(
          "No students in state, fetching all student IDs from database"
        );
        const { data: profileData, error: profileError } = await supabase
          .from("profiles")
          .select("id")
          .eq("role", "student");

        if (profileError) {
          console.error("Error fetching student IDs:", profileError);
          return;
        }

        if (profileData && profileData.length > 0) {
          studentIds = profileData.map((profile) => profile.id);
        } else {
          console.log("No students found in database");
          return;
        }
      }

      // Fetch attendance records for today for students in the admin's school
      let recordsQuery = supabase
        .from("attendance_records")
        .select("*")
        .in("student_id", studentIds)
        .gte("timestamp", todayStr)
        .lt("timestamp", tomorrowStr);

      // Filter by school_id if available (for school admins)
      if (profile?.school_id && profile?.access_level !== 3) {
        // Only filter by school_id if the admin is not a system admin (access_level 3)
        recordsQuery = recordsQuery.eq("school_id", profile.school_id);
      }

      const { data: recordsData, error: recordsError } = await recordsQuery;

      if (recordsError) {
        console.error("Error fetching attendance records:", recordsError);
        throw recordsError;
      }

      console.log(
        `AdminStudentDirectory: Fetched ${
          recordsData?.length || 0
        } attendance records`
      );

      if (recordsData) {
        // Group records by student_id
        const recordsByStudent: Record<string, any[]> = {};
        recordsData.forEach((record) => {
          if (!recordsByStudent[record.student_id]) {
            recordsByStudent[record.student_id] = [];
          }
          recordsByStudent[record.student_id].push(record);
        });

        // For each student, get the most recent record
        const records: Record<string, AttendanceRecord> = {};
        Object.entries(recordsByStudent).forEach(
          ([studentId, studentRecords]) => {
            // Sort records by timestamp in descending order (newest first)
            const sortedRecords = studentRecords.sort(
              (a, b) =>
                new Date(b.timestamp).getTime() -
                new Date(a.timestamp).getTime()
            );

            // Use the most recent record
            const mostRecent = sortedRecords[0];

            records[studentId] = {
              id: mostRecent.id,
              studentId: mostRecent.student_id,
              roomId: mostRecent.room_id,
              timestamp: mostRecent.timestamp,
              deviceInfo: mostRecent.device_info || "",
              location: mostRecent.location || null,
              verificationMethod: (mostRecent.verification_method ||
                "manual") as "biometric" | "pin" | "manual",
              status: (mostRecent.status || "absent") as
                | "present"
                | "absent"
                | "late"
                | "excused",
            };
          }
        );

        // Set default status for students without records
        studentIds.forEach((studentId) => {
          if (!records[studentId]) {
            records[studentId] = {
              id: "",
              studentId: studentId,
              roomId: null,
              timestamp: todayStr,
              deviceInfo: "",
              location: null,
              verificationMethod: "manual",
              status: "absent",
            };
          }
        });

        console.log(
          "AdminStudentDirectory: Setting attendance records:",
          records
        );
        setAttendanceRecords(records);
      }
    } catch (error) {
      console.error("Error fetching attendance records:", error);
    }
  };

  // Filter assigned students based on selected block and room
  const assignedStudents = useMemo(() => {
    console.log("Filtering students with:", {
      selectedBlock,
      selectedRoom,
      totalStudents: students.length,
    });

    return students.filter((student) => {
      // First filter by search query if it exists
      const query = searchQuery.toLowerCase().trim();
      const matchesSearch =
        query === "" ||
        student.name.toLowerCase().includes(query) ||
        (student.studentId &&
          student.studentId.toLowerCase().includes(query)) ||
        (student.email && student.email.toLowerCase().includes(query));

      // Get the block name from the blocks list
      const selectedBlockName = blocks.find(
        (b) => b.id === selectedBlock
      )?.name;
      const selectedRoomNumber = rooms.find((r) => r.id === selectedRoom)?.name;

      // Then filter by block and room using names
      const matchesBlock =
        selectedBlock === "all" || student.blockName === selectedBlockName;
      const matchesRoom =
        selectedRoom === "none" || student.roomNumber === selectedRoomNumber;

      console.log("Filtering student:", {
        name: student.name,
        blockName: student.blockName,
        roomNumber: student.roomNumber,
        selectedBlockName,
        selectedRoomNumber,
        matchesBlock,
        matchesRoom,
      });

      return matchesSearch && matchesBlock && matchesRoom;
    });
  }, [students, selectedBlock, selectedRoom, searchQuery, blocks, rooms]);

  // Initialize data and restore selections
  useEffect(() => {
    const initializeData = async () => {
      try {
        // Set loading state
        setLoading(true);

        // Fetch blocks for the current school (if school admin)
        let blocksQuery = supabase.from("blocks").select("*").order("name");

        // Filter by school_id if available (for school admins)
        if (profile?.school_id && profile?.access_level !== 3) {
          // Only filter by school_id if the admin is not a system admin (access_level 3)
          blocksQuery = blocksQuery.eq("school_id", profile.school_id);
        }

        const { data: blocksData, error: blocksError } = await blocksQuery;

        if (blocksError) {
          console.error("Error fetching blocks:", blocksError);
          throw blocksError;
        }

        console.log("Fetched blocks:", blocksData);
        setBlocks(blocksData || []);

        // Restore saved block selection
        const savedBlock = localStorage.getItem("adminSelectedBlock");
        if (
          savedBlock &&
          blocksData?.some((block) => block.id === savedBlock)
        ) {
          setSelectedBlock(savedBlock);
        }

        // Fetch rooms for the current school (if school admin)
        let roomsQuery = supabase
          .from("rooms")
          .select(
            `
            id,
            name,
            building,
            floor,
            capacity,
            teacher_id,
            block_id,
            school_id,
            blocks (
              id,
              name
            )
          `
          )
          .order("name");

        // Filter by school_id if available (for school admins)
        if (profile?.school_id && profile?.access_level !== 3) {
          // Only filter by school_id if the admin is not a system admin (access_level 3)
          roomsQuery = roomsQuery.eq("school_id", profile.school_id);
        }

        const { data: roomsData, error: roomsError } = await roomsQuery;

        if (roomsError) {
          console.error("Error fetching rooms:", roomsError);
          throw roomsError;
        }

        console.log("Fetched rooms:", roomsData);
        setRooms(roomsData || []);

        // Restore saved room selection
        const savedRoom = localStorage.getItem("adminSelectedRoom");
        if (savedRoom && roomsData?.some((room) => room.id === savedRoom)) {
          setSelectedRoom(savedRoom);
        }

        // First fetch attendance records to ensure we have the latest status
        await fetchAttendanceRecords();

        // Then fetch students
        await fetchStudents();

        // Set loading to false
        setLoading(false);
      } catch (error) {
        console.error("Error initializing data:", error);
        toast({
          title: "Error",
          description: "Failed to load initial data. Please refresh the page.",
          variant: "destructive",
        });
        setLoading(false);
      }
    };

    initializeData();

    // Set up a polling interval to refresh attendance data periodically (every 5 seconds)
    const pollingInterval = setInterval(() => {
      console.log("AdminStudentDirectory: Polling for attendance updates");
      fetchAttendanceRecords();
    }, 5000);

    // Clean up the interval when the component unmounts
    return () => {
      clearInterval(pollingInterval);
    };
  }, []);

  // Handle block selection
  const handleBlockChange = async (value: string) => {
    console.log("Block changed to:", value);
    localStorage.setItem("adminSelectedBlock", value);
    setSelectedBlock(value);

    // Reset room selection when changing blocks
    if (value === "all") {
      setSelectedRoom("none");
      localStorage.removeItem("adminSelectedRoom");
    } else {
      // If changing to a specific block, check if current room is in that block
      const currentRoom = rooms.find((r) => r.id === selectedRoom);
      if (currentRoom && currentRoom.block_id !== value) {
        setSelectedRoom("none");
        localStorage.removeItem("adminSelectedRoom");
      }
    }
  };

  // Handle room selection
  const handleRoomChange = (value: string) => {
    console.log("Room changed to:", value);
    if (value === "none") {
      localStorage.setItem("adminSelectedRoom", "none");
      setSelectedRoom("none");
    } else {
      localStorage.setItem("adminSelectedRoom", value);
      setSelectedRoom(value);

      // If selecting a room, also select its block
      const selectedRoomData = rooms.find((r) => r.id === value);
      if (selectedRoomData && selectedRoomData.block_id !== selectedBlock) {
        setSelectedBlock(selectedRoomData.block_id);
        localStorage.setItem("adminSelectedBlock", selectedRoomData.block_id);
      }
    }
  };

  // Get current attendance status for a student
  const getAttendanceStatus = (
    studentId: string
  ): "present" | "absent" | "late" | "excused" => {
    return attendanceRecords[studentId]?.status || "absent";
  };

  // Toggle student attendance status
  const toggleAttendanceStatus = async (student: Student) => {
    try {
      const now = new Date();
      const currentStatus = getAttendanceStatus(student.id);

      // Determine the new status based on current status
      const newStatus = currentStatus === "present" ? "absent" : "present";

      // Update local state immediately for better UX
      setAttendanceRecords((prev) => ({
        ...prev,
        [student.id]: {
          ...(prev[student.id] || {}),
          studentId: student.id,
          status: newStatus,
          timestamp: now.toISOString(),
          verificationMethod: "manual",
          deviceInfo: "Manual update by admin",
        },
      }));

      // Show success message immediately
      toast({
        title: "Status Updated",
        description: `${student.name} has been marked ${newStatus}`,
      });

      // Show additional notification
      sonnerToast.success(`${student.name} marked ${newStatus}`, {
        description: "Attendance record updated successfully",
      });

      // Get today's date at midnight for comparison
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // Find a valid room ID to use
      let roomId = student.room_id;
      if (!roomId && selectedRoom !== "none") {
        roomId = selectedRoom;
      } else if (!roomId && rooms.length > 0) {
        roomId = rooms[0].id;
      }

      // If we still don't have a room ID, we can't proceed with database update
      if (!roomId) {
        console.error("No room ID available for attendance record");
        toast({
          title: "Warning",
          description:
            "Attendance status updated in UI only. No room available for database record.",
          variant: "warning",
        });
        return;
      }

      // Check for existing record today
      const { data: existingRecords, error: fetchError } = await supabase
        .from("attendance_records")
        .select("*")
        .eq("student_id", student.id)
        .gte("timestamp", today.toISOString())
        .lt(
          "timestamp",
          new Date(today.getTime() + 24 * 60 * 60 * 1000).toISOString()
        );

      if (fetchError) {
        console.error("Error fetching existing records:", fetchError);
        throw fetchError;
      }

      // Disable the database trigger temporarily to avoid notification errors
      let triggerDisabled = false;
      try {
        try {
          await supabase.rpc("disable_attendance_notification_trigger");
          triggerDisabled = true;
        } catch (triggerError) {
          console.warn("Could not disable notification trigger:", triggerError);
          // Continue anyway
        }

        if (existingRecords && existingRecords.length > 0) {
          // Update existing record
          const existingRecord = existingRecords[0];
          const { data, error } = await supabase
            .from("attendance_records")
            .update({
              status: newStatus,
              verification_method: "manual",
              device_info: "Manual update by admin",
              timestamp: now.toISOString(),
            })
            .eq("id", existingRecord.id)
            .select()
            .single();

          if (error) {
            console.error("Error updating attendance record:", error);
            throw error;
          }

          console.log("Successfully updated attendance record:", data);
        } else {
          // Create new record
          const recordData: any = {
            student_id: student.id,
            room_id: roomId,
            timestamp: now.toISOString(),
            device_info: "Manual update by admin",
            verification_method: "manual",
            status: newStatus,
            location: null,
            created_at: now.toISOString(),
          };

          // Add school_id if available
          if (profile?.school_id) {
            recordData.school_id = profile.school_id;
          }

          const { data, error } = await supabase
            .from("attendance_records")
            .insert(recordData)
            .select()
            .single();

          if (error) {
            console.error("Error creating attendance record:", error);
            throw error;
          }

          console.log("Successfully created attendance record:", data);
        }
      } finally {
        // Re-enable the trigger if it was disabled
        if (triggerDisabled) {
          try {
            await supabase.rpc("enable_attendance_notification_trigger");
          } catch (triggerError) {
            console.warn(
              "Could not re-enable notification trigger:",
              triggerError
            );
          }
        }
      }

      // Create a notification for the student about the status change
      try {
        // Get room name for the notification message
        let roomName = "Unknown";
        if (roomId) {
          const { data: roomData, error: roomError } = await supabase
            .from("rooms")
            .select("name")
            .eq("id", roomId)
            .single();

          if (!roomError && roomData) {
            roomName = roomData.name;
          }
        }

        // Get admin name for the notification message
        let adminName = "an administrator";
        if (profile?.id) {
          const { data: adminData, error: adminError } = await supabase
            .from("profiles")
            .select("name")
            .eq("id", profile.id)
            .single();

          if (!adminError && adminData) {
            adminName = adminData.name;
          }
        }

        // Create a simple notification that should work with any schema
        const notificationData = {
          student_id: student.id,
          title:
            newStatus === "present" ? "✅ Marked Present" : "❌ Marked Absent",
          message:
            newStatus === "present"
              ? `You have been marked present in Room ${roomName} by ${adminName}.`
              : `You have been marked absent in Room ${roomName} by ${adminName}. If this is incorrect, please contact your administrator.`,
          type: "system", // Use system type which should be supported in any schema
          read: false,
        };

        console.log(
          "Creating notification for student:",
          student.id,
          JSON.stringify(notificationData, null, 2)
        );

        try {
          const { data: notificationResult, error: notificationError } =
            await supabase
              .from("notifications")
              .insert(notificationData)
              .select();

          if (notificationError) {
            console.error("Notification error details:", notificationError);

            // Try to diagnose the issue
            console.log("Checking notifications table structure...");

            // Use RPC to query information_schema
            const { data: tableInfo, error: tableError } = await supabase.rpc(
              "execute_sql",
              {
                sql: `SELECT column_name, data_type, is_nullable
                      FROM information_schema.columns
                      WHERE table_schema = 'public'
                      AND table_name = 'notifications'`,
              }
            );

            if (tableError) {
              console.error("Error checking table structure:", tableError);
            } else {
              console.log("Notifications table columns:", tableInfo);
            }

            throw notificationError;
          }

          console.log("Successfully created notification:", notificationResult);
        } catch (insertError) {
          console.error("Error during notification insert:", insertError);

          // Try a simpler notification format as fallback
          console.log("Trying simplified notification format...");

          // Check if the error is related to the school_id column
          const isSchoolIdError =
            insertError.message && insertError.message.includes("school_id");

          // Create an ultra-minimal notification with only required fields
          const simplifiedNotification = {
            student_id: student.id,
            title: "Status Changed",
            message: `Your attendance status was changed to ${newStatus}.`,
            type: "system",
            read: false,
          };

          const { data: fallbackResult, error: fallbackError } = await supabase
            .from("notifications")
            .insert(simplifiedNotification)
            .select();

          if (fallbackError) {
            console.error("Fallback notification also failed:", fallbackError);

            // Try to get the actual table structure
            try {
              const { data: sampleData, error: sampleError } = await supabase
                .from("notifications")
                .select("*")
                .limit(1);

              if (sampleError) {
                console.error(
                  "Error fetching sample notification:",
                  sampleError
                );
              } else if (sampleData && sampleData.length > 0) {
                console.log("Sample notification structure:", sampleData[0]);

                // Try one more time with a notification that matches the exact structure
                const exactStructure = {};
                Object.keys(sampleData[0]).forEach((key) => {
                  if (key === "id") return; // Skip ID
                  if (key === "student_id") exactStructure[key] = student.id;
                  else if (key === "title")
                    exactStructure[key] = "✅ Status Changed";
                  else if (key === "message")
                    exactStructure[key] = "Your attendance status was updated.";
                  else if (key === "read") exactStructure[key] = false;
                  else exactStructure[key] = null; // Set other fields to null
                });

                console.log("Trying exact structure match:", exactStructure);

                const { data: exactResult, error: exactError } = await supabase
                  .from("notifications")
                  .insert(exactStructure)
                  .select();

                if (exactError) {
                  console.error(
                    "Exact structure notification failed:",
                    exactError
                  );
                } else {
                  console.log(
                    "Exact structure notification succeeded:",
                    exactResult
                  );
                }
              }
            } catch (error) {
              console.error("Error analyzing notifications table:", error);
            }
          } else {
            console.log(
              "Fallback notification created successfully:",
              fallbackResult
            );
          }
        }
      } catch (notificationError) {
        console.error("Error creating notification:", notificationError);
        // Continue execution even if notification creation fails
      }

      // Fetch attendance records again to ensure UI is up to date
      await fetchAttendanceRecords();
    } catch (error) {
      console.error("Error updating attendance:", error);
      toast({
        title: "Error",
        description: "Failed to update attendance status. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Get available rooms for selected block
  const availableRooms = useMemo(() => {
    if (selectedBlock === "all") {
      return rooms;
    }
    return rooms.filter((room) => room.block_id === selectedBlock);
  }, [selectedBlock, rooms]);

  return (
    <div className="space-y-6">
      {/* Main Student Directory Table */}
      <Card>
        <CardHeader>
          <div className="flex flex-col md:flex-row justify-between md:items-center gap-4">
            <div>
              <CardTitle>{t("admin.studentDirectory.title")}</CardTitle>
              <CardDescription>
                {t("admin.studentDirectory.description")}
              </CardDescription>
            </div>
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex flex-col md:flex-row gap-4 mb-4">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder={t("admin.studentDirectory.searchPlaceholder")}
                    className="pl-8 pr-8 w-full md:w-[280px]"
                    value={searchQuery}
                    onChange={(e) => {
                      // Update in real-time
                      setSearchQuery(e.target.value);
                    }}
                  />
                  {searchQuery && (
                    <button
                      onClick={() => setSearchQuery("")}
                      className="absolute right-2.5 top-2.5 h-4 w-4 text-muted-foreground hover:text-foreground"
                      aria-label="Clear search"
                    >
                      <X size={16} />
                    </button>
                  )}
                </div>
                {searchQuery && (
                  <Badge
                    variant="outline"
                    className="flex items-center gap-1 h-9 px-3"
                  >
                    <Search className="h-3 w-3" />
                    <span>
                      {searchQuery.length > 15
                        ? `${searchQuery.substring(0, 15)}...`
                        : searchQuery}
                    </span>
                  </Badge>
                )}
              </div>
              <div className="flex flex-col md:flex-row gap-4">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={toggleViewMode}
                  className="h-9 w-9"
                  title={
                    viewMode === "list"
                      ? t("admin.studentDirectory.gridView")
                      : t("admin.studentDirectory.listView")
                  }
                >
                  {viewMode === "list" ? (
                    <LayoutGrid className="h-4 w-4" />
                  ) : (
                    <LayoutList className="h-4 w-4" />
                  )}
                </Button>
                <div className="flex items-center gap-2">
                  <Building2 className="h-4 w-4 text-muted-foreground" />
                  <Select
                    value={selectedBlock}
                    onValueChange={handleBlockChange}
                  >
                    <SelectTrigger className="w-[180px]">
                      <SelectValue
                        placeholder={t("students.directory.selectBlock")}
                      />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">
                        {t("students.directory.allBlocks")}
                      </SelectItem>
                      {blocks.map((block) => (
                        <SelectItem key={block.id} value={block.id}>
                          {t("common.block")} {block.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-center gap-2">
                  <DoorOpen className="h-4 w-4 text-muted-foreground" />
                  <Select value={selectedRoom} onValueChange={handleRoomChange}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue
                        placeholder={t("students.directory.selectRoom")}
                      />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">
                        {t("students.directory.allRooms")}
                      </SelectItem>
                      {availableRooms.map((room) => (
                        <SelectItem key={room.id} value={room.id}>
                          {t("common.room")} {room.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center items-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
            </div>
          ) : viewMode === "grid" ? (
            // Grid View
            <div className="p-2">
              {assignedStudents.length > 0 ? (
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                  {assignedStudents.map((student) => {
                    const status = getAttendanceStatus(student.id);
                    return (
                      <div
                        key={student.id}
                        className="border rounded-md p-4 flex flex-col items-center hover:shadow-md transition-shadow"
                      >
                        <div className="relative mb-2">
                          {student.photoUrl ? (
                            <img
                              src={student.photoUrl}
                              alt={student.name}
                              className="w-24 h-24 rounded-full object-cover ring-2 ring-primary/10"
                            />
                          ) : (
                            <div className="w-24 h-24 rounded-full bg-muted flex items-center justify-center ring-2 ring-primary/10">
                              <UserCircle className="w-14 h-14 text-muted-foreground" />
                            </div>
                          )}
                          <div className="absolute -bottom-1 -right-1">
                            {getStatusIcon(status)}
                          </div>
                        </div>

                        <div className="font-medium text-center truncate w-full mb-1">
                          {student.name}
                        </div>

                        <div className="flex gap-2 mt-2 w-full">
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex-1 h-8"
                            onClick={() => setSelectedStudent(student)}
                          >
                            {t("common.view")}
                          </Button>

                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant={
                                  status === "present"
                                    ? "destructive"
                                    : "default"
                                }
                                size="sm"
                                className="flex-1 h-8"
                              >
                                {status === "present"
                                  ? t("teacher.dashboard.absent")
                                  : t("teacher.dashboard.present")}
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent>
                              <DropdownMenuItem
                                onClick={() =>
                                  toggleAttendanceStatus({
                                    ...student,
                                    status: "present",
                                  })
                                }
                                className="gap-2"
                              >
                                {getStatusIcon("present")}{" "}
                                {t("teacher.attendance.markPresent")}
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() =>
                                  toggleAttendanceStatus({
                                    ...student,
                                    status: "absent",
                                  })
                                }
                                className="gap-2"
                              >
                                {getStatusIcon("absent")}{" "}
                                {t("teacher.attendance.markAbsent")}
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() =>
                                  toggleAttendanceStatus({
                                    ...student,
                                    status: "late",
                                  })
                                }
                                className="gap-2"
                              >
                                {getStatusIcon("late")}{" "}
                                {t("teacher.attendance.markLate")}
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() =>
                                  toggleAttendanceStatus({
                                    ...student,
                                    status: "excused",
                                  })
                                }
                                className="gap-2"
                              >
                                {getStatusIcon("excused")}{" "}
                                {t("teacher.attendance.markExcused")}
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center h-32 text-center text-muted-foreground">
                  {searchQuery ? (
                    <>
                      <Search className="w-10 h-10 opacity-20 mb-2" />
                      <p>{t("admin.studentDirectory.noStudentsFound")}</p>
                      <p className="text-sm mt-1">
                        {t("students.directory.trySearching")}
                      </p>
                    </>
                  ) : selectedBlock !== "all" || selectedRoom !== "none" ? (
                    <>
                      <UserCircle className="w-10 h-10 opacity-20 mb-2" />
                      <p>{t("teacher.dashboard.noStudentsInRoom")}</p>
                    </>
                  ) : (
                    <>
                      <UserCircle className="w-10 h-10 opacity-20 mb-2" />
                      <p>{t("admin.studentDirectory.noStudentsFound")}</p>
                    </>
                  )}
                </div>
              )}
            </div>
          ) : (
            // List View
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow className="bg-muted/50">
                    <TableHead className="w-[80px]">
                      {t("admin.studentDirectory.profile")}
                    </TableHead>
                    <TableHead>{t("admin.studentDirectory.name")}</TableHead>
                    <TableHead>
                      {t("admin.studentDirectory.studentId")}
                    </TableHead>
                    <TableHead>{t("admin.studentDirectory.course")}</TableHead>
                    <TableHead>{t("admin.studentDirectory.block")}</TableHead>
                    <TableHead>{t("admin.studentDirectory.room")}</TableHead>
                    <TableHead>{t("admin.studentDirectory.status")}</TableHead>
                    <TableHead className="text-right">
                      {t("admin.studentDirectory.actions")}
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {assignedStudents.length > 0 ? (
                    assignedStudents.map((student) => {
                      const status = getAttendanceStatus(student.id);
                      return (
                        <TableRow key={student.id}>
                          <TableCell>
                            {student.photoUrl ? (
                              <img
                                src={student.photoUrl}
                                alt={student.name}
                                className="w-10 h-10 rounded-full object-cover ring-2 ring-primary/10"
                              />
                            ) : (
                              <div className="w-10 h-10 rounded-full bg-muted flex items-center justify-center ring-2 ring-primary/10">
                                <UserCircle className="w-6 h-6 text-muted-foreground" />
                              </div>
                            )}
                          </TableCell>
                          <TableCell className="font-medium">
                            {student.name}
                          </TableCell>
                          <TableCell>
                            {student.studentId ||
                              t("admin.studentDirectory.notSet")}
                          </TableCell>
                          <TableCell>
                            {student.course ||
                              t("admin.studentDirectory.notSet")}
                          </TableCell>
                          <TableCell>
                            <Badge variant="secondary">
                              {student.blocks
                                ? `${t("common.block")} ${student.blocks.name}`
                                : t("admin.studentDirectory.notAssigned")}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">
                              {student.rooms
                                ? `${t("common.room")} ${student.rooms.name}`
                                : t("admin.studentDirectory.notAssigned")}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              {getStatusIcon(status)}
                              <span className="capitalize">
                                {t(`attendance.${status}`)}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end space-x-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setSelectedStudent(student)}
                              >
                                {t("common.view")}
                              </Button>
                              <Button
                                variant={
                                  status === "present"
                                    ? "destructive"
                                    : "default"
                                }
                                size="sm"
                                onClick={() => toggleAttendanceStatus(student)}
                              >
                                {status === "present" ? (
                                  <>
                                    <X className="w-4 h-4 mr-1" />
                                    {t("teacher.dashboard.absent")}
                                  </>
                                ) : (
                                  <>
                                    <Check className="w-4 h-4 mr-1" />
                                    {t("teacher.dashboard.present")}
                                  </>
                                )}
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })
                  ) : (
                    <TableRow>
                      <TableCell colSpan={8} className="h-24 text-center">
                        <div className="flex flex-col items-center justify-center text-muted-foreground">
                          {searchQuery ? (
                            <>
                              <Search className="w-10 h-10 opacity-20 mb-2" />
                              <p>
                                {t("admin.studentDirectory.noStudentsFound")}
                              </p>
                              <p className="text-sm mt-1">
                                {t("students.directory.trySearching")}
                              </p>
                            </>
                          ) : selectedBlock !== "all" ||
                            selectedRoom !== "none" ? (
                            <>
                              <UserCircle className="w-10 h-10 opacity-20 mb-2" />
                              <p>{t("teacher.dashboard.noStudentsInRoom")}</p>
                            </>
                          ) : (
                            <>
                              <UserCircle className="w-10 h-10 opacity-20 mb-2" />
                              <p>
                                {t("admin.studentDirectory.noStudentsFound")}
                              </p>
                            </>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Student Profile Dialog */}
      {selectedStudent && (
        <Dialog
          open={!!selectedStudent}
          onOpenChange={(open) => !open && setSelectedStudent(null)}
        >
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>{t("students.profile.title")}</DialogTitle>
              <DialogDescription>
                {t("students.profile.detailedInfo", {
                  name: selectedStudent.name,
                })}
              </DialogDescription>
            </DialogHeader>

            <div className="flex flex-col items-center space-y-4 pt-4">
              {/* Student Photo */}
              <div className="relative">
                {selectedStudent.photoUrl ? (
                  <img
                    src={selectedStudent.photoUrl}
                    alt={selectedStudent.name}
                    className="w-24 h-24 rounded-full object-cover border-2 border-primary"
                  />
                ) : (
                  <div className="w-24 h-24 rounded-full bg-muted flex items-center justify-center border-2 border-primary">
                    <User size={32} />
                  </div>
                )}
              </div>

              {/* Student Name */}
              <h2 className="text-xl font-bold">{selectedStudent.name}</h2>

              {/* Student Details */}
              <div className="w-full space-y-3">
                <div className="grid grid-cols-2 gap-2">
                  <div className="font-medium">
                    {t("students.profile.studentId")}:
                  </div>
                  <div>
                    {selectedStudent.studentId ||
                      t("admin.studentDirectory.notSet")}
                  </div>

                  <div className="font-medium">
                    {t("students.profile.email")}:
                  </div>
                  <div className="break-all">{selectedStudent.email}</div>

                  <div className="font-medium">
                    {t("students.profile.course")}:
                  </div>
                  <div>
                    {selectedStudent.course ||
                      t("admin.studentDirectory.notSet")}
                  </div>

                  <div className="font-medium">
                    {t("students.profile.block")}:
                  </div>
                  <div>
                    {selectedStudent.blocks
                      ? `${t("common.block")} ${selectedStudent.blocks.name}`
                      : t("admin.studentDirectory.notAssigned")}
                  </div>

                  <div className="font-medium">
                    {t("students.profile.room")}:
                  </div>
                  <div>
                    {selectedStudent.rooms
                      ? `${t("common.room")} ${selectedStudent.rooms.name}`
                      : t("admin.studentDirectory.notAssigned")}
                  </div>

                  <div className="font-medium">
                    {t("students.profile.biometric")}:
                  </div>
                  <div>
                    {selectedStudent.biometricRegistered ? (
                      <Badge className="bg-green-100 text-green-800">
                        {t("students.profile.registered")}
                      </Badge>
                    ) : (
                      <Badge
                        variant="outline"
                        className="text-muted-foreground"
                      >
                        {t("students.profile.notRegistered")}
                      </Badge>
                    )}
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex w-full space-x-2 pt-4">
                <Button
                  onClick={() => toggleAttendanceStatus(selectedStudent)}
                  className="flex-1"
                  variant={
                    getAttendanceStatus(selectedStudent.id) === "present"
                      ? "destructive"
                      : "default"
                  }
                >
                  {getAttendanceStatus(selectedStudent.id) === "present" ? (
                    <>
                      <X className="mr-2 h-4 w-4" />
                      {t("teacher.attendance.markAbsent")}
                    </>
                  ) : (
                    <>
                      <Check className="mr-2 h-4 w-4" />
                      {t("teacher.attendance.markPresent")}
                    </>
                  )}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
