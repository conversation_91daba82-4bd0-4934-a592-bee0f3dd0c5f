import { UnifiedToaster } from "@/components/ui/unified-toast";
import { TooltipProvider } from "@/components/ui/tooltip";
import { OfflineAlert } from "@/components/ui/offline-alert";
import SupabaseConnectionAlert from "./components/shared/SupabaseConnectionAlert";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { AuthProvider } from "./context/AuthContext";
import { SchoolProvider } from "./context/SchoolContext";
import { SchoolThemeProvider } from "./components/providers/SchoolThemeProvider";
import { ThemeProvider } from "./components/providers/ThemeProvider";
import { LanguageProvider } from "./context/SimpleLanguageContext";
import { TranslationUpdater } from "./components/providers/TranslationUpdater";
import MaintenanceRedirect from "./components/shared/MaintenanceRedirect";
import UserStatusRedirect from "./components/shared/UserStatusRedirect";
import { getUIText } from "./config/branding";
import "./i18n";
import Index from "./pages/Index";
import Login from "./pages/Login";
import SignUp from "./pages/SignUp";
import SystemAdminSignUp from "./pages/SystemAdminSignUp";
import Student from "./pages/Student";
import Teacher from "./pages/Teacher";
import Admin from "./pages/Admin";
import SystemAdmin from "./pages/SystemAdmin";
import AdminProfileSetup from "./pages/AdminProfileSetup";
import NotFound from "./pages/NotFound";
import Offline from "./pages/Offline";
import RunMigrations from "./pages/RunMigrations";
import { useAuth } from "./context/AuthContext";
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { LoadingSpinner } from "@/components/ui/loading-spinner";

const queryClient = new QueryClient();

// Component to handle protected routes
const ProtectedRoute = ({
  children,
  requiredRole,
}: {
  children: React.ReactNode;
  requiredRole?: "student" | "teacher" | "admin";
}) => {
  const { profile, loading } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (!loading) {
      if (!profile) {
        // If not logged in, redirect to login
        navigate("/login", { replace: true });
      } else if (requiredRole && profile.role !== requiredRole) {
        // If user doesn't have the required role, redirect to their appropriate page
        navigate(`/${profile.role}`, { replace: true });
      }
    }
  }, [loading, profile, requiredRole, navigate]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <LoadingSpinner message="Authenticating..." size="lg" />
      </div>
    );
  }

  return profile ? <>{children}</> : null;
};

const AppRoutes = () => {
  const { profile, loading } = useAuth();

  if (loading) {
    // Get loading message based on browser language
    const browserLang = navigator.language.startsWith("tr") ? "tr" : "en";
    const uiText = getUIText(browserLang);

    return (
      <div className="flex items-center justify-center h-screen">
        <LoadingSpinner message={uiText.LOADING} size="lg" />
      </div>
    );
  }

  return (
    <Routes>
      <Route
        path="/"
        element={
          profile ? (
            profile.role === "admin" && profile.accessLevel === 3 ? (
              <Navigate to="/system-admin" replace />
            ) : (
              <Navigate to={`/${profile.role}`} replace />
            )
          ) : (
            <Index />
          )
        }
      />
      <Route
        path="/login"
        element={
          profile ? (
            profile.role === "admin" && profile.accessLevel === 3 ? (
              <Navigate to="/system-admin" replace />
            ) : (
              <Navigate to={`/${profile.role}`} replace />
            )
          ) : (
            <Login />
          )
        }
      />
      <Route
        path="/signup"
        element={
          profile ? (
            profile.role === "admin" && profile.accessLevel === 3 ? (
              <Navigate to="/system-admin" replace />
            ) : (
              <Navigate to={`/${profile.role}`} replace />
            )
          ) : (
            <SignUp />
          )
        }
      />
      <Route
        path="/system-admin-signup"
        element={
          profile ? (
            profile.role === "admin" && profile.accessLevel === 3 ? (
              <Navigate to="/system-admin" replace />
            ) : (
              <Navigate to={`/${profile.role}`} replace />
            )
          ) : (
            <SystemAdminSignUp />
          )
        }
      />
      <Route
        path="/student"
        element={
          <ProtectedRoute requiredRole="student">
            <Student />
          </ProtectedRoute>
        }
      />
      <Route
        path="/teacher"
        element={
          <ProtectedRoute requiredRole="teacher">
            <Teacher />
          </ProtectedRoute>
        }
      />
      <Route
        path="/admin"
        element={
          <ProtectedRoute requiredRole="admin">
            <Admin />
          </ProtectedRoute>
        }
      />
      <Route
        path="/system-admin"
        element={
          <ProtectedRoute requiredRole="admin">
            {/* Additional check for system admin access level is in the SystemAdmin component */}
            <SystemAdmin />
          </ProtectedRoute>
        }
      />
      <Route
        path="/admin-profile-setup"
        element={
          <ProtectedRoute requiredRole="admin">
            <AdminProfileSetup />
          </ProtectedRoute>
        }
      />
      <Route path="/offline" element={<Offline />} />
      <Route path="/run-migrations" element={<RunMigrations />} />
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
};

const App = () => (
  <QueryClientProvider client={queryClient}>
    <BrowserRouter
      future={{ v7_startTransition: true, v7_relativeSplatPath: true }}
    >
      <AuthProvider>
        <LanguageProvider>
          <TranslationUpdater>
            <SchoolProvider>
              <ThemeProvider
                defaultTheme="dark"
                storageKey="campus-guardian-theme"
              >
                <SchoolThemeProvider>
                  <TooltipProvider>
                    <OfflineAlert />
                    {/* Use our safe toast provider */}
                    <UnifiedToaster />
                    <SupabaseConnectionAlert />
                    <MaintenanceRedirect>
                      <UserStatusRedirect>
                        <div className="flex flex-col min-h-screen">
                          <div className="flex-1">
                            <AppRoutes />
                          </div>
                          {/* Footer will be conditionally rendered in each page */}
                        </div>
                      </UserStatusRedirect>
                    </MaintenanceRedirect>
                  </TooltipProvider>
                </SchoolThemeProvider>
              </ThemeProvider>
            </SchoolProvider>
          </TranslationUpdater>
        </LanguageProvider>
      </AuthProvider>
    </BrowserRouter>
  </QueryClientProvider>
);

export default App;
