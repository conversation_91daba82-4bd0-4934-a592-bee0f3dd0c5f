-- Create attendance_records table for QR code attendance tracking
CREATE TABLE IF NOT EXISTS attendance_records (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  room_id UUID NOT NULL REFERENCES rooms(id) ON DELETE CASCADE,
  qr_session_id UUID NOT NULL,
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  verification_method TEXT NOT NULL DEFAULT 'pin',
  location_data JSONB,
  device_info JSONB,
  status TEXT NOT NULL DEFAULT 'present',
  school_id UUID REFERENCES schools(id) ON DELETE CASCADE,
  block_id UUID REFERENCES blocks(id) ON DELETE CASCADE,
  qr_data_hash TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_attendance_records_student_id ON attendance_records(student_id);
CREATE INDEX IF NOT EXISTS idx_attendance_records_room_id ON attendance_records(room_id);
CREATE INDEX IF NOT EXISTS idx_attendance_records_timestamp ON attendance_records(timestamp);
CREATE INDEX IF NOT EXISTS idx_attendance_records_school_id ON attendance_records(school_id);
CREATE INDEX IF NOT EXISTS idx_attendance_records_qr_session_id ON attendance_records(qr_session_id);
CREATE INDEX IF NOT EXISTS idx_attendance_records_date ON attendance_records(DATE(timestamp));

-- Create composite index for common queries
CREATE INDEX IF NOT EXISTS idx_attendance_records_student_room_date 
ON attendance_records(student_id, room_id, DATE(timestamp));

-- Add RLS (Row Level Security)
ALTER TABLE attendance_records ENABLE ROW LEVEL SECURITY;

-- Policy for students to view their own attendance records
CREATE POLICY "Students can view their own attendance records" ON attendance_records
  FOR SELECT USING (auth.uid() = student_id);

-- Policy for students to insert their own attendance records
CREATE POLICY "Students can insert their own attendance records" ON attendance_records
  FOR INSERT WITH CHECK (auth.uid() = student_id);

-- Policy for teachers to view attendance records for their school
CREATE POLICY "Teachers can view school attendance records" ON attendance_records
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.access_level >= 2 
      AND profiles.school_id = attendance_records.school_id
    )
  );

-- Policy for school admins to view all attendance records for their school
CREATE POLICY "School admins can view all school attendance records" ON attendance_records
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.access_level >= 2 
      AND profiles.school_id = attendance_records.school_id
    )
  );

-- Policy for super admins to view all attendance records
CREATE POLICY "Super admins can view all attendance records" ON attendance_records
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.access_level = 3
    )
  );

-- Create QR sessions table for tracking QR code sessions
CREATE TABLE IF NOT EXISTS qr_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  room_id UUID NOT NULL REFERENCES rooms(id) ON DELETE CASCADE,
  session_id UUID NOT NULL UNIQUE,
  qr_data TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  is_active BOOLEAN DEFAULT true,
  school_id UUID REFERENCES schools(id) ON DELETE CASCADE,
  block_id UUID REFERENCES blocks(id) ON DELETE CASCADE,
  generated_by UUID REFERENCES auth.users(id) ON DELETE SET NULL
);

-- Create indexes for QR sessions
CREATE INDEX IF NOT EXISTS idx_qr_sessions_room_id ON qr_sessions(room_id);
CREATE INDEX IF NOT EXISTS idx_qr_sessions_session_id ON qr_sessions(session_id);
CREATE INDEX IF NOT EXISTS idx_qr_sessions_expires_at ON qr_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_qr_sessions_school_id ON qr_sessions(school_id);

-- Add RLS for QR sessions
ALTER TABLE qr_sessions ENABLE ROW LEVEL SECURITY;

-- Policy for teachers and admins to manage QR sessions
CREATE POLICY "Teachers can manage QR sessions for their school" ON qr_sessions
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.access_level >= 2 
      AND profiles.school_id = qr_sessions.school_id
    )
  );

-- Policy for students to view active QR sessions (for validation)
CREATE POLICY "Students can view active QR sessions" ON qr_sessions
  FOR SELECT USING (
    is_active = true 
    AND expires_at > NOW()
    AND EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.school_id = qr_sessions.school_id
    )
  );

-- Create function to automatically clean up expired QR sessions
CREATE OR REPLACE FUNCTION cleanup_expired_qr_sessions()
RETURNS void AS $$
BEGIN
  UPDATE qr_sessions 
  SET is_active = false 
  WHERE expires_at < NOW() AND is_active = true;
END;
$$ LANGUAGE plpgsql;

-- Create function to prevent duplicate attendance records
CREATE OR REPLACE FUNCTION check_duplicate_attendance()
RETURNS TRIGGER AS $$
BEGIN
  -- Check if student already has attendance record for this room today
  IF EXISTS (
    SELECT 1 FROM attendance_records 
    WHERE student_id = NEW.student_id 
    AND room_id = NEW.room_id 
    AND DATE(timestamp) = DATE(NEW.timestamp)
    AND id != COALESCE(NEW.id, '00000000-0000-0000-0000-000000000000'::uuid)
  ) THEN
    RAISE EXCEPTION 'Student already has attendance record for this room today';
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for duplicate attendance check
CREATE TRIGGER check_duplicate_attendance_trigger
  BEFORE INSERT OR UPDATE ON attendance_records
  FOR EACH ROW
  EXECUTE FUNCTION check_duplicate_attendance();

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at
CREATE TRIGGER update_attendance_records_updated_at
  BEFORE UPDATE ON attendance_records
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Add columns to rooms table for QR code management (if not exists)
DO $$ 
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'rooms' AND column_name = 'current_qr_code') THEN
    ALTER TABLE rooms ADD COLUMN current_qr_code TEXT;
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'rooms' AND column_name = 'qr_expiry') THEN
    ALTER TABLE rooms ADD COLUMN qr_expiry TIMESTAMP WITH TIME ZONE;
  END IF;
END $$;

-- Create view for attendance analytics
CREATE OR REPLACE VIEW attendance_analytics AS
SELECT 
  ar.school_id,
  ar.block_id,
  ar.room_id,
  r.name as room_name,
  b.name as block_name,
  DATE(ar.timestamp) as attendance_date,
  COUNT(*) as total_attendance,
  COUNT(DISTINCT ar.student_id) as unique_students,
  AVG(EXTRACT(EPOCH FROM (ar.timestamp - DATE_TRUNC('day', ar.timestamp)))) as avg_check_in_time
FROM attendance_records ar
JOIN rooms r ON ar.room_id = r.id
JOIN blocks b ON ar.block_id = b.id
GROUP BY ar.school_id, ar.block_id, ar.room_id, r.name, b.name, DATE(ar.timestamp);

-- Grant permissions for the view
GRANT SELECT ON attendance_analytics TO authenticated;

-- Create function to get attendance summary for a student
CREATE OR REPLACE FUNCTION get_student_attendance_summary(
  p_student_id UUID,
  p_start_date DATE DEFAULT NULL,
  p_end_date DATE DEFAULT NULL
)
RETURNS TABLE (
  total_days INTEGER,
  present_days INTEGER,
  attendance_rate DECIMAL
) AS $$
BEGIN
  -- Set default dates if not provided
  p_start_date := COALESCE(p_start_date, CURRENT_DATE - INTERVAL '30 days');
  p_end_date := COALESCE(p_end_date, CURRENT_DATE);
  
  RETURN QUERY
  WITH date_range AS (
    SELECT generate_series(p_start_date, p_end_date, '1 day'::interval)::date as date
  ),
  attendance_days AS (
    SELECT DISTINCT DATE(timestamp) as attendance_date
    FROM attendance_records
    WHERE student_id = p_student_id
    AND DATE(timestamp) BETWEEN p_start_date AND p_end_date
  )
  SELECT 
    (p_end_date - p_start_date + 1)::INTEGER as total_days,
    COUNT(ad.attendance_date)::INTEGER as present_days,
    ROUND(
      (COUNT(ad.attendance_date)::DECIMAL / (p_end_date - p_start_date + 1)) * 100, 
      2
    ) as attendance_rate
  FROM date_range dr
  LEFT JOIN attendance_days ad ON dr.date = ad.attendance_date;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
