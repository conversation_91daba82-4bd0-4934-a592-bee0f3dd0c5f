import { useState, useEffect } from "react";
import { useExcuses } from "@/hooks/useExcuses";
import { useAuth } from "@/context/AuthContext";
import { supabase } from "@/lib/supabase";
import { format } from "date-fns";
import { calculateDaysBetween, formatDuration } from "@/lib/date-utils";
import { useToast } from "@/hooks/use-toast";
import { Excuse } from "@/lib/types";
import { useTranslation } from "react-i18next";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import {
  CheckCircle,
  XCircle,
  Clock,
  FileText,
  Calendar,
  Search,
  Filter,
  User,
  DoorClosed,
  X,
  Check,
  Trash2,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";

export function ExcusesManagement() {
  const { t } = useTranslation();
  const { profile } = useAuth();
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [selectedExcuse, setSelectedExcuse] = useState<Excuse | null>(null);
  const [teacherNotes, setTeacherNotes] = useState("");
  const [rooms, setRooms] = useState<{ id: string; name: string }[]>([]);
  const [selectedRoom, setSelectedRoom] = useState<string>("all");
  const [confirmDelete, setConfirmDelete] = useState<string | null>(null);

  // Fetch excuses using our custom hook
  const {
    excuses,
    loading,
    error,
    fetchExcuses,
    updateExcuseStatus,
    deleteExcuse,
  } = useExcuses({
    role: "teacher",
    roomId: selectedRoom !== "all" ? selectedRoom : undefined,
    status: statusFilter !== "all" ? (statusFilter as any) : undefined,
  });

  // Fetch rooms assigned to this teacher
  useEffect(() => {
    const fetchTeacherRooms = async () => {
      if (!profile?.id) return;

      try {
        const { data, error } = await supabase
          .from("rooms")
          .select("id, name")
          .eq("teacher_id", profile.id);

        if (error) throw error;
        setRooms(data || []);
      } catch (err) {
        console.error("Error fetching teacher rooms:", err);
      }
    };

    fetchTeacherRooms();
  }, [profile?.id]);

  // Filter excuses based on search query
  const filteredExcuses = excuses.filter((excuse) => {
    if (!searchQuery) return true;

    const searchLower = searchQuery.toLowerCase();
    return (
      excuse.studentName?.toLowerCase().includes(searchLower) ||
      excuse.roomName?.toLowerCase().includes(searchLower) ||
      excuse.reason.toLowerCase().includes(searchLower)
    );
  });

  // Handle excuse approval
  const handleApproveExcuse = async () => {
    if (!selectedExcuse) return;

    try {
      await updateExcuseStatus(selectedExcuse.id, "approved", teacherNotes);
      setSelectedExcuse(null);
      setTeacherNotes("");
    } catch (error) {
      console.error("Error approving excuse:", error);
    }
  };

  // Handle excuse rejection
  const handleRejectExcuse = async () => {
    if (!selectedExcuse) return;

    try {
      await updateExcuseStatus(selectedExcuse.id, "rejected", teacherNotes);
      setSelectedExcuse(null);
      setTeacherNotes("");
    } catch (error) {
      console.error("Error rejecting excuse:", error);
    }
  };

  // Handle excuse deletion
  const handleDeleteExcuse = async (excuseId: string) => {
    try {
      await deleteExcuse(excuseId);
      setConfirmDelete(null);
      toast({
        title: t("teacher.excuses.excuseDeleted"),
        description: t("common.success"),
      });
    } catch (error) {
      console.error("Error deleting excuse:", error);
      toast({
        title: t("common.error"),
        description: t("common.error"),
        variant: "destructive",
      });
    }
  };

  // Group excuses by status
  const pendingExcuses = filteredExcuses.filter(
    (excuse) => excuse.status === "pending"
  );
  const approvedExcuses = filteredExcuses.filter(
    (excuse) => excuse.status === "approved"
  );
  const rejectedExcuses = filteredExcuses.filter(
    (excuse) => excuse.status === "rejected"
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("teacher.excuses.excusesManagement")}</CardTitle>
        <CardDescription>
          {t("teacher.excuses.manageStudentExcuses")}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={t("common.search") + "..."}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-8 w-full"
            />
            {searchQuery && (
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-1 top-1 h-7 w-7"
                onClick={() => setSearchQuery("")}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
          <div className="flex gap-2">
            <Select value={selectedRoom} onValueChange={setSelectedRoom}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={t("common.filter") + "..."} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">
                  {t("common.all")} {t("common.rooms")}
                </SelectItem>
                {rooms.map((room) => (
                  <SelectItem key={room.id} value={room.id}>
                    {t("common.room")} {room.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder={t("common.filter") + "..."} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t("common.all")}</SelectItem>
                <SelectItem value="pending">
                  {t("teacher.excuses.pending")}
                </SelectItem>
                <SelectItem value="approved">
                  {t("teacher.excuses.approved")}
                </SelectItem>
                <SelectItem value="rejected">
                  {t("teacher.excuses.rejected")}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Tabs defaultValue="pending">
          <TabsList className="grid grid-cols-3 mb-4">
            <TabsTrigger value="pending" className="relative">
              {t("teacher.excuses.pending")}
              {pendingExcuses.length > 0 && (
                <Badge variant="secondary" className="ml-2">
                  {pendingExcuses.length}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="approved">
              {t("teacher.excuses.approved")}
            </TabsTrigger>
            <TabsTrigger value="rejected">
              {t("teacher.excuses.rejected")}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="pending">
            {loading ? (
              <div className="flex justify-center items-center h-32">
                <LoadingSpinner message="Loading excuses..." />
              </div>
            ) : pendingExcuses.length === 0 ? (
              <EmptyState
                icon={
                  <Clock className="w-12 h-12 text-muted-foreground opacity-30" />
                }
                title={t("teacher.excuses.noPendingExcuses")}
                description={t("teacher.excuses.noExcusesMessage")}
              />
            ) : (
              <div className="space-y-4">
                {pendingExcuses.map((excuse) => (
                  <ExcuseCard
                    key={excuse.id}
                    excuse={excuse}
                    onClick={() => {
                      setSelectedExcuse(excuse);
                      setTeacherNotes("");
                    }}
                    onDelete={() => setConfirmDelete(excuse.id)}
                  />
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="approved">
            {loading ? (
              <div className="flex justify-center items-center h-32">
                <LoadingSpinner message="Loading excuses..." />
              </div>
            ) : approvedExcuses.length === 0 ? (
              <EmptyState
                icon={
                  <CheckCircle className="w-12 h-12 text-muted-foreground opacity-30" />
                }
                title={t("teacher.excuses.noApprovedExcuses")}
                description={t("teacher.excuses.noExcusesMessage")}
              />
            ) : (
              <div className="space-y-4">
                {approvedExcuses.map((excuse) => (
                  <ExcuseCard
                    key={excuse.id}
                    excuse={excuse}
                    onClick={() => {
                      setSelectedExcuse(excuse);
                      setTeacherNotes(excuse.notes || "");
                    }}
                  />
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="rejected">
            {loading ? (
              <div className="flex justify-center items-center h-32">
                <LoadingSpinner message="Loading excuses..." />
              </div>
            ) : rejectedExcuses.length === 0 ? (
              <EmptyState
                icon={
                  <XCircle className="w-12 h-12 text-muted-foreground opacity-30" />
                }
                title={t("teacher.excuses.noRejectedExcuses")}
                description={t("teacher.excuses.noExcusesMessage")}
              />
            ) : (
              <div className="space-y-4">
                {rejectedExcuses.map((excuse) => (
                  <ExcuseCard
                    key={excuse.id}
                    excuse={excuse}
                    onClick={() => {
                      setSelectedExcuse(excuse);
                      setTeacherNotes(excuse.notes || "");
                    }}
                  />
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>

      {/* Excuse Details Dialog */}
      <Dialog
        open={!!selectedExcuse}
        onOpenChange={(open) => !open && setSelectedExcuse(null)}
      >
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>{t("teacher.excuses.excuseDetails")}</DialogTitle>
            <DialogDescription>
              {t("teacher.excuses.reviewExcuse")}
            </DialogDescription>
          </DialogHeader>

          {selectedExcuse && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground mb-1">
                    {t("teacher.excuses.student")}
                  </p>
                  <p className="font-medium">{selectedExcuse.studentName}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground mb-1">
                    {t("common.room")}
                  </p>
                  <p className="font-medium">
                    {t("common.room")} {selectedExcuse.roomName}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground mb-1">
                    {t("teacher.excuses.date")}
                  </p>
                  <p>
                    {format(
                      new Date(selectedExcuse.start_date),
                      "MMMM d, yyyy"
                    )}
                    {selectedExcuse.start_date !== selectedExcuse.end_date &&
                      ` - ${format(
                        new Date(selectedExcuse.end_date),
                        "MMMM d, yyyy"
                      )}`}
                  </p>
                  <p className="text-xs text-primary mt-1">
                    {t("teacher.excuses.duration")}:{" "}
                    {formatDuration(
                      calculateDaysBetween(
                        selectedExcuse.start_date,
                        selectedExcuse.end_date
                      )
                    )}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground mb-1">
                    {t("common.time")}
                  </p>
                  <p>
                    {selectedExcuse.start_time} - {selectedExcuse.end_time}
                  </p>
                </div>
              </div>

              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">
                  {t("teacher.excuses.reason")}
                </p>
                <div className="p-3 bg-muted rounded-md text-sm">
                  {selectedExcuse.reason}
                </div>
              </div>

              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">
                  {t("common.status")}
                </p>
                <div className="flex items-center">
                  {getStatusBadge(selectedExcuse.status)}
                  {selectedExcuse.status !== "pending" && (
                    <p className="text-sm ml-2">
                      by {selectedExcuse.teacherName || "Unknown Teacher"}
                    </p>
                  )}
                </div>
              </div>

              {selectedExcuse.status === "pending" ? (
                <>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground mb-1">
                      {t("teacher.excuses.notes")}
                    </p>
                    <Textarea
                      placeholder={t("teacher.excuses.enterNotes")}
                      value={teacherNotes}
                      onChange={(e) => setTeacherNotes(e.target.value)}
                      className="resize-none"
                    />
                  </div>

                  <DialogFooter className="flex justify-between">
                    <Button
                      variant="destructive"
                      onClick={handleRejectExcuse}
                      className="flex items-center gap-1"
                    >
                      <X className="w-4 h-4" />
                      {t("teacher.excuses.reject")}
                    </Button>
                    <Button
                      onClick={handleApproveExcuse}
                      className="flex items-center gap-1"
                    >
                      <Check className="w-4 h-4" />
                      {t("teacher.excuses.approve")}
                    </Button>
                  </DialogFooter>
                </>
              ) : (
                selectedExcuse.notes && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground mb-1">
                      {t("teacher.excuses.notes")}
                    </p>
                    <div className="p-3 bg-muted rounded-md text-sm">
                      {selectedExcuse.notes}
                    </div>
                  </div>
                )
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={!!confirmDelete}
        onOpenChange={(open) => !open && setConfirmDelete(null)}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t("common.confirmDelete")}</DialogTitle>
            <DialogDescription>
              {t("teacher.excuses.confirmDeleteExcuse")}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setConfirmDelete(null)}>
              {t("common.cancel")}
            </Button>
            <Button
              variant="destructive"
              onClick={() => confirmDelete && handleDeleteExcuse(confirmDelete)}
            >
              {t("common.delete")}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
}

// Helper components
interface ExcuseCardProps {
  excuse: Excuse;
  onClick?: () => void;
  onDelete?: () => void;
}

function ExcuseCard({ excuse, onClick, onDelete }: ExcuseCardProps) {
  const handleClick = (e: React.MouseEvent) => {
    // If we're clicking the delete button, don't trigger the card click
    if ((e.target as HTMLElement).closest(".delete-button")) {
      e.stopPropagation();
      return;
    }
    onClick?.();
  };

  return (
    <div
      className="border rounded-lg p-4 hover:border-primary/50 transition-colors cursor-pointer"
      onClick={handleClick}
    >
      <div className="flex items-start justify-between">
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <User className="w-4 h-4 text-muted-foreground" />
            <h3 className="font-medium">
              {excuse.studentName || "Unknown Student"}
            </h3>
          </div>
          <div className="flex items-center gap-2">
            <DoorClosed className="w-4 h-4 text-muted-foreground" />
            <p className="text-sm text-muted-foreground">
              {t("common.room")} {excuse.roomName || t("common.unknown")}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          {getStatusBadge(excuse.status)}
          {excuse.status === "pending" && onDelete && (
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 text-muted-foreground hover:text-destructive delete-button"
              onClick={(e) => {
                e.stopPropagation();
                onDelete();
              }}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      <div className="mt-3 grid grid-cols-2 gap-2 text-sm">
        <div className="flex flex-col gap-1">
          <div className="flex items-center gap-1.5 text-muted-foreground">
            <Calendar className="w-3.5 h-3.5" />
            <span>
              {format(new Date(excuse.start_date), "MMM dd, yyyy")}
              {excuse.start_date !== excuse.end_date &&
                ` - ${format(new Date(excuse.end_date), "MMM dd, yyyy")}`}
            </span>
          </div>
          {excuse.start_date && excuse.end_date && (
            <div className="text-xs text-primary ml-5">
              Duration:{" "}
              {formatDuration(
                calculateDaysBetween(excuse.start_date, excuse.end_date)
              )}
            </div>
          )}
        </div>
        <div className="flex items-center gap-1.5 text-muted-foreground">
          <Clock className="w-3.5 h-3.5" />
          <span>
            {excuse.start_time} - {excuse.end_time}
          </span>
        </div>
      </div>

      <div className="mt-2 flex items-start gap-1.5">
        <FileText className="w-3.5 h-3.5 text-muted-foreground mt-0.5" />
        <p className="text-sm text-muted-foreground line-clamp-1">
          {excuse.reason}
        </p>
      </div>
    </div>
  );
}

function getStatusBadge(status: string) {
  // Use the useTranslation hook inside the component
  const { t } = useTranslation();

  switch (status) {
    case "pending":
      return (
        <Badge
          variant="outline"
          className="bg-yellow-50 text-yellow-700 border-yellow-200 flex items-center gap-1"
        >
          <Clock className="w-3 h-3" />
          <span>{t("teacher.excuses.pending")}</span>
        </Badge>
      );
    case "approved":
      return (
        <Badge
          variant="outline"
          className="bg-green-50 text-green-700 border-green-200 flex items-center gap-1"
        >
          <CheckCircle className="w-3 h-3" />
          <span>{t("teacher.excuses.approved")}</span>
        </Badge>
      );
    case "rejected":
      return (
        <Badge
          variant="outline"
          className="bg-red-50 text-red-700 border-red-200 flex items-center gap-1"
        >
          <XCircle className="w-3 h-3" />
          <span>{t("teacher.excuses.rejected")}</span>
        </Badge>
      );
    default:
      return null;
  }
}

function EmptyState({
  icon,
  title,
  description,
}: {
  icon: React.ReactNode;
  title: string;
  description: string;
}) {
  return (
    <div className="text-center py-8">
      <div className="mx-auto mb-4">{icon}</div>
      <h3 className="text-lg font-medium mb-1">{title}</h3>
      <p className="text-sm text-muted-foreground">{description}</p>
    </div>
  );
}

// Default export
export default ExcusesManagement;
